"""Cache tables for bigquery cacher."""

from django.db import models



class BigQueryCache(models.Model):
    """
    Model to store cached BigQuery results.
    """
    key = models.TextField(unique=True)
    result = models.BinaryField()
    query = models.TextField()
    job_config = models.BinaryField(null=True)
    last_accessed = models.DateTimeField(null=True)
    last_updated = models.DateTimeField(null=True)
