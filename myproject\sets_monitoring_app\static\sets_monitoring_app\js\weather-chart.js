/**
 * Weather Chart JavaScript - <PERSON>les weather forecast chart rendering
 * Uses Chart.js to create interactive 3-day weather forecasts with dual timezone display
 */

// Create a custom plugin for night hours background
const nightHoursPlugin = {
    id: 'nightHoursBackground',
    beforeDraw: function(chart, _args, options) {
        const {ctx, chartArea} = chart;
        if (!chartArea) return;

        // Get the first dataset's metadata to access x positions
        const meta = chart.getDatasetMeta(0);
        if (!meta || !meta.data || meta.data.length === 0) return;

        // Draw gray background for night hours
        ctx.save();

        // Find consecutive night hours and draw rectangles
        let startX = null;

        // Access nightHours from the plugin options
        const nightHours = options.nightHours || [];

        nightHours.forEach((isNight, i) => {
            if (i >= meta.data.length) return;

            const x = meta.data[i].x;

            // Start a new rectangle
            if (isNight && startX === null) {
                startX = x;
            }

            // End the rectangle if we reach a day hour or the end of the data
            if ((!isNight || i === meta.data.length - 1) && startX !== null) {
                let width;

                if (i === meta.data.length - 1 && isNight) {
                    // If this is the last point and it's night, extend to the end of the chart
                    width = chartArea.right - startX;
                } else {
                    // Normal case - width is the difference between current x and start x
                    width = x - startX;
                }

                ctx.fillStyle = 'rgba(200, 200, 200, 0.3)'; // Light gray with transparency
                ctx.fillRect(startX, chartArea.top, width, chartArea.height);

                startX = null;
            }
        });

        ctx.restore();
    }
};

// Register the plugin with Chart.js
function registerChartPlugins() {
    if (typeof Chart !== 'undefined') {
        Chart.register(nightHoursPlugin);
        // Register zoom plugin if available
        if (typeof ChartZoom !== 'undefined') {
            Chart.register(ChartZoom);
        }
    }
}

// Register plugins when Chart.js is available
if (typeof Chart !== 'undefined') {
    registerChartPlugins();
} else {
    // Wait for Chart.js to load
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof Chart !== 'undefined') {
            registerChartPlugins();
        }
    });
}

class WeatherChart {
    static chartInstances = new Map();

    /**
     * Render weather chart with Chart.js
     * @param {string} containerId - ID of the container element
     * @param {Object} weatherData - Weather data from API
     */
    static render(containerId, weatherData) {
        if (!weatherData || !weatherData.hourly) {
            return;
        }

        const hourlyData = weatherData.hourly;

        // Prepare data for Chart.js
        const times = hourlyData.map(item => {
            // Convert ISO string to Date object for Chart.js
            return new Date(item.time);
        });
        const localTimes = hourlyData.map(item => item.localTime);
        const rigaTimes = hourlyData.map(item => item.rigaTime);
        const temperatures = hourlyData.map(item => item.temperature);
        const precipitation = hourlyData.map(item => item.precipitationIntensity);
        const windSpeeds = hourlyData.map(item => item.windSpeed);
        const windGusts = hourlyData.map(item => item.windGust);

        // Calculate night hours based on local timezone (23:00-7:00)
        const nightHours = hourlyData.map(item => {
            const hourStr = item.localTime.split(' ')[1];
            const hour = parseInt(hourStr.split(':')[0]);
            return (hour >= 23 || hour < 7);
        });

        // Get canvas element
        const canvas = document.getElementById(containerId);
        if (!canvas) {
            return;
        }

        const ctx = canvas.getContext('2d');

        // Destroy existing chart for this container if it exists
        if (WeatherChart.chartInstances.has(containerId)) {
            WeatherChart.chartInstances.get(containerId).destroy();
            WeatherChart.chartInstances.delete(containerId);
        }

        // Clear any existing reset zoom elements from the container
        const container = canvas.parentElement;
        if (container) {
            // Remove any existing weather chart reset zoom elements
            const existingResetElements = container.querySelectorAll('.weather-chart-reset-zoom-container');
            existingResetElements.forEach(element => {
                element.remove();
            });
        }

        // Calculate data ranges for dynamic scaling
        const tempMin = Math.min(...temperatures);
        const tempMax = Math.max(...temperatures);
        const precipMin = Math.min(...precipitation);
        const precipMax = Math.max(...precipitation);
        const windMin = Math.min(...windSpeeds);
        const windMax = Math.max(...windSpeeds);
        const windGustMin = Math.min(...windGusts);
        const windGustMax = Math.max(...windGusts);

        // Calculate appropriate scale ranges with padding (include wind gust in wind range calculation)
        const allWindMin = Math.min(windMin, windGustMin);
        const allWindMax = Math.max(windMax, windGustMax);
        const tempRange = WeatherChart.calculateScaleRange(tempMin, tempMax, allWindMin, allWindMax);
        const precipRange = WeatherChart.calculateScaleRange(precipMin, precipMax);



        // Detect if mobile for responsive design
        const isMobile = window.innerWidth <= 768;

        // Create Chart.js configuration
        const chartConfig = {
            type: 'line',
            data: {
                labels: times,
                datasets: [
                    {
                        label: 'Temperature (°C)',
                        data: temperatures,
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        yAxisID: 'y-temp',
                        tension: 0.3,
                        borderWidth: isMobile ? 2 : 3,
                        pointRadius: isMobile ? 2 : 3,
                        pointHoverRadius: isMobile ? 4 : 6
                    },
                    {
                        label: 'Precipitation (mm/h)',
                        data: precipitation,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        yAxisID: 'y-precip',
                        tension: 0.3,
                        borderWidth: isMobile ? 2 : 3,
                        pointRadius: isMobile ? 2 : 3,
                        pointHoverRadius: isMobile ? 4 : 6
                    },
                    {
                        label: 'Wind Speed (m/s)',
                        data: windSpeeds,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        yAxisID: 'y-temp',
                        tension: 0.3,
                        borderWidth: isMobile ? 2 : 3,
                        pointRadius: isMobile ? 2 : 3,
                        pointHoverRadius: isMobile ? 4 : 6
                    },
                    {
                        label: 'Wind Gust (m/s)',
                        data: windGusts,
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        yAxisID: 'y-temp',
                        tension: 0.3,
                        borderWidth: isMobile ? 2 : 3,
                        pointRadius: isMobile ? 2 : 3,
                        pointHoverRadius: isMobile ? 4 : 6
                    }
                ]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                animation: false, // Remove all animations for instant display
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: false // We'll display title separately
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        cornerRadius: 6,
                        displayColors: true,
                        position: 'nearest',
                        callbacks: {
                            afterLabel: function(context) {
                                const index = context.dataIndex;
                                const setTimezone = weatherData.location?.timezone || 'Local';
                                return [
                                    `Europe/Riga: ${rigaTimes[index]}`,
                                    `${setTimezone}: ${localTimes[index]}`
                                ];
                            }
                        }
                    },
                    legend: WeatherChart.createLegendConfig(),
                    // Configure the night hours plugin
                    nightHoursBackground: {
                        nightHours: nightHours // Pass the nightHours array to the plugin
                    },
                    // Configure zoom plugin
                    zoom: {
                        zoom: {
                            wheel: {
                                enabled: true,
                            },
                            pinch: {
                                enabled: true
                            },
                            drag: {
                                enabled: true,
                                backgroundColor: 'rgba(48, 162, 214, 0.2)', // Light blue selection area
                                borderColor: 'rgba(55, 162, 214, 0.8)',
                                borderWidth: 2,
                            },
                            mode: 'x', // Only zoom on x-axis (time)
                            scaleMode: 'x', // Only scale x-axis
                        },
                        pan: {
                            enabled: true,
                            mode: 'x', // Only pan on x-axis (time)
                            scaleMode: 'x', // Only scale x-axis
                            // Disable pan when drag zoom is active
                            modifierKey: 'ctrl',
                        },
                        limits: {
                            x: {
                                min: 'original',
                                max: 'original'
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        position: 'bottom',
                        time: {
                            unit: 'hour',
                            stepSize: 6,
                            displayFormats: {
                                hour: 'MM/dd HH:mm',
                                day: 'MM/dd'
                            },
                            tooltipFormat: 'MMM dd, HH:mm'
                        },
                        title: {
                            display: true,
                            text: weatherData.location?.timezone || 'Local Time',
                            color: '#666',
                            font: {
                                size: 11,
                                weight: 'bold'
                            }
                        },
                        grid: { color: '#e5e7eb' },
                        ticks: {
                            color: '#666',
                            maxTicksLimit: 8,
                            callback: function(value) {
                                const date = new Date(value);
                                return new Intl.DateTimeFormat('en-US', {
                                    timeZone: weatherData.location?.timezone || 'UTC',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: false
                                }).format(date);
                            }
                        }
                    },
                    'x-riga': {
                        type: 'time',
                        position: 'top',
                        time: {
                            unit: 'hour',
                            stepSize: 6,
                            displayFormats: {
                                hour: 'MM/dd HH:mm',
                                day: 'MM/dd'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Europe/Riga',
                            color: '#666',
                            font: {
                                size: 11,
                                weight: 'bold'
                            }
                        },
                        grid: { display: false },
                        ticks: {
                            color: '#666',
                            maxTicksLimit: 8,
                            callback: function(value) {
                                const date = new Date(value);
                                return new Intl.DateTimeFormat('en-US', {
                                    timeZone: 'Europe/Riga',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: false
                                }).format(date);
                            }
                        }
                    },
                    'y-temp': {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        min: tempRange.min,
                        max: tempRange.max,
                        title: {
                            display: true,
                            text: 'Temp (°C) / Wind (m/s)',
                            font: {
                                size: isMobile ? 10 : 12,
                                family: 'Inter, system-ui, sans-serif'
                            },
                            color: '#6b7280' // Grey color for title
                        },
                        ticks: {
                            stepSize: tempRange.stepSize,
                            font: {
                                size: isMobile ? 9 : 11
                            },
                            color: '#6b7280', // Grey color for values
                            callback: function(value) {
                                return Math.round(value * 10) / 10; // Round to 1 decimal place
                            }
                        },
                        grid: {
                            color: '#f3f4f6'
                        }
                    },
                    'y-precip': {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        min: precipRange.min,
                        max: precipRange.max,
                        title: {
                            display: true,
                            text: 'Precipitation (mm/h)',
                            font: {
                                size: isMobile ? 10 : 12,
                                family: 'Inter, system-ui, sans-serif'
                            },
                            color: '#6b7280' // Grey color for title
                        },
                        ticks: {
                            stepSize: precipRange.stepSize,
                            font: {
                                size: isMobile ? 9 : 11
                            },
                            color: '#6b7280', // Grey color for values
                            callback: function(value) {
                                return value.toFixed(2); // Show values with 2 decimal places
                            }
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                layout: {
                    padding: {
                        top: 40,
                        bottom: 40,
                        left: 15,
                        right: 15
                    }
                }
            }
        };

        // Set fixed canvas dimensions for Chart.js
        canvas.width = 1000;
        canvas.height = 450;

        // Add responsive CSS styling to the canvas
        canvas.style.maxWidth = '100%';
        canvas.style.height = 'auto';

        // Create additional HTML elements for reset zoom button
        // Use setTimeout to ensure canvas is fully rendered before positioning elements
        setTimeout(() => {
            WeatherChart.createChartElements(containerId, weatherData);
        }, 10);

        // Create the chart
        try {
            const chartInstance = new Chart(ctx, chartConfig);
            WeatherChart.chartInstances.set(containerId, chartInstance);
        } catch (error) {
            // Chart creation failed silently
        }
    }

    /**
     * Calculate appropriate scale range for chart axes
     * @param {number} dataMin - Minimum value in the data
     * @param {number} dataMax - Maximum value in the data
     * @param {number} secondaryMin - Optional secondary data minimum (for combined axes)
     * @param {number} secondaryMax - Optional secondary data maximum (for combined axes)
     * @returns {Object} Scale configuration with min, max, and stepSize
     */
    static calculateScaleRange(dataMin, dataMax, secondaryMin = null, secondaryMax = null) {
        // Include secondary data if provided (for temp/wind combined axis)
        const actualMin = secondaryMin !== null ? Math.min(dataMin, secondaryMin) : dataMin;
        const actualMax = secondaryMax !== null ? Math.max(dataMax, secondaryMax) : dataMax;

        // Add 10% padding to the range
        const range = actualMax - actualMin;
        const padding = Math.max(range * 0.1, 1); // At least 1 unit of padding

        let min = actualMin - padding;
        let max = actualMax + padding;

        // Round to nice numbers
        const magnitude = Math.pow(10, Math.floor(Math.log10(range)));
        const stepSize = magnitude / 2; // Use half the magnitude as step size

        // Round min down and max up to nearest step
        min = Math.floor(min / stepSize) * stepSize;
        max = Math.ceil(max / stepSize) * stepSize;

        // Ensure minimum range
        if (max - min < stepSize * 4) {
            const center = (min + max) / 2;
            min = center - stepSize * 2;
            max = center + stepSize * 2;
        }

        // For very small ranges, use smaller step sizes
        if (range < 1) {
            const smallStepSize = range / 5;
            return {
                min: Math.floor(actualMin * 10) / 10 - 0.1,
                max: Math.ceil(actualMax * 10) / 10 + 0.1,
                stepSize: Math.max(smallStepSize, 0.1)
            };
        }

        return {
            min: Math.max(min, 0), // Don't go below 0 for weather data
            max: max,
            stepSize: stepSize
        };
    }

    /**
     * Create legend configuration for Chart.js
     * @returns {Object} Legend configuration object
     */
    static createLegendConfig() {
        return {
            display: true,
            position: 'bottom',
            align: 'center',
            onClick: function(event, legendItem, legend) {
                // Ignore clicks on night hours legend item
                if (legendItem.datasetIndex === -1) {
                    return;
                }
                // Use default behavior for other legend items
                Chart.defaults.plugins.legend.onClick.call(this, event, legendItem, legend);
            },
            labels: {
                usePointStyle: false,
                font: { size: 11 },
                color: '#666',
                padding: 15,
                boxWidth: 15,
                boxHeight: 2,
                generateLabels: function(chart) {
                    const original = chart.constructor.defaults.plugins.legend.labels.generateLabels;
                    const labels = original.call(this, chart);

                    labels.forEach(label => {
                        label.pointStyle = 'line';
                    });

                    labels.push({
                        text: 'Night Hours (23:00-07:00)',
                        fillStyle: 'rgba(200, 200, 200, 0.3)',
                        strokeStyle: 'rgba(200, 200, 200, 0.3)',
                        lineWidth: 1,
                        pointStyle: 'rect',
                        hidden: false,
                        // Add required properties to prevent errors
                        datasetIndex: -1, // Indicate this is not associated with a dataset
                        index: -1
                    });

                    return labels;
                }
            }
        };
    }

    /**
     * Create additional HTML elements for reset zoom button
     * @param {string} containerId - ID of the canvas element
     * @param {Object} weatherData - Weather data from API
     */
    static createChartElements(containerId, weatherData) {
        const canvas = document.getElementById(containerId);
        if (!canvas || !canvas.parentElement) return;

        const container = canvas.parentElement;

        // Remove existing elements if they exist
        const existingElements = container.querySelectorAll('.weather-chart-reset-zoom');
        existingElements.forEach(el => el.remove());

        // Create reset zoom button as a simple centered element
        const resetZoomButton = document.createElement('button');
        resetZoomButton.className = 'weather-chart-reset-zoom';
        resetZoomButton.style.cssText = `
            display: block;
            margin: 0 auto;
            margin-top: 4px;
            background-color: #517aee;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-family: Inter, system-ui, sans-serif;
            cursor: pointer;
            transition: background-color 0.2s;
        `;
        resetZoomButton.innerHTML = 'Reset Zoom';
        resetZoomButton.addEventListener('click', () => {
            WeatherChart.resetZoom(containerId);
        });
        resetZoomButton.addEventListener('mouseenter', () => {
            resetZoomButton.style.backgroundColor = '#3b82f6';
        });
        resetZoomButton.addEventListener('mouseleave', () => {
            resetZoomButton.style.backgroundColor = '#517aee';
        });

        container.appendChild(resetZoomButton);
    }





    /**
     * Reset zoom for a specific chart instance
     * @param {string} containerId - ID of the container element
     */
    static resetZoom(containerId) {
        if (containerId && WeatherChart.chartInstances.has(containerId)) {
            const chartInstance = WeatherChart.chartInstances.get(containerId);
            if (chartInstance && chartInstance.resetZoom) {
                chartInstance.resetZoom();
            }
        }
    }

    /**
     * Destroy a specific chart instance by container ID
     * @param {string} containerId - ID of the container element
     */
    static destroy(containerId) {
        if (containerId && WeatherChart.chartInstances.has(containerId)) {
            // Destroy the chart instance
            WeatherChart.chartInstances.get(containerId).destroy();
            WeatherChart.chartInstances.delete(containerId);



            // Clean up any remaining reset zoom elements for this specific chart
            const canvas = document.getElementById(containerId);
            if (canvas && canvas.parentElement) {
                const container = canvas.parentElement;
                const resetZoomElements = container.querySelectorAll('.weather-chart-reset-zoom');
                resetZoomElements.forEach(element => element.remove());
            }
        }
    }

    /**
     * Destroy all chart instances
     */
    static destroyAll() {
        WeatherChart.chartInstances.forEach((chart) => {
            chart.destroy();
        });
        WeatherChart.chartInstances.clear();
    }

    /**
     * Resize the chart (disabled to prevent resize loops)
     */
    static resize() {
        // Disabled to prevent continuous resizing issues
    }

    /**
     * Clean up orphaned chart instances that no longer have DOM elements
     */
    static cleanupOrphanedCharts() {
        const orphanedContainers = [];

        WeatherChart.chartInstances.forEach((_, containerId) => {
            if (!document.getElementById(containerId)) {
                orphanedContainers.push(containerId);
            }
        });

        orphanedContainers.forEach(containerId => {
            WeatherChart.destroy(containerId);
        });
    }
}

/**
 * Weather API Manager - Handles API calls and data management
 */
class WeatherAPI {
    /**
     * Fetch weather data for a set
     * @param {string} setName - Name of the set
     * @returns {Promise<Object>} Weather data or error
     */
    static async fetchWeatherData(setName) {
        try {
            const response = await fetch(`/sets-monitoring/api/weather-forecast/${encodeURIComponent(setName)}/`);
            const result = await response.json();
            
            if (result.success) {
                return { success: true, data: result.data };
            } else {
                return { success: false, error: result.error || 'Failed to load weather data' };
            }
        } catch (error) {
            return { success: false, error: 'Network error: ' + error.message };
        }
    }
}

/**
 * Global Weather Accordion Manager - Manages exclusive accordion behavior
 */
class WeatherAccordionManager {
    constructor() {
        this.activeAccordion = null;
        this.accordions = new Map();
    }

    /**
     * Register a weather accordion component
     * @param {string} setName - Name of the set
     * @param {Object} component - Alpine.js component instance
     */
    registerAccordion(setName, component) {
        this.accordions.set(setName, component);
    }

    /**
     * Unregister a weather accordion component
     * @param {string} setName - Name of the set
     */
    unregisterAccordion(setName) {
        this.accordions.delete(setName);
        if (this.activeAccordion === setName) {
            this.activeAccordion = null;
        }
    }

    /**
     * Open a specific accordion and close all others
     * @param {string} setName - Name of the set to open
     */
    openAccordion(setName) {
        // Close all other accordions
        this.accordions.forEach((component, name) => {
            if (name !== setName && component.weatherOpen) {
                component.weatherOpen = false;
            }
        });

        // Set the active accordion
        this.activeAccordion = setName;
    }

    /**
     * Close a specific accordion
     * @param {string} setName - Name of the set to close
     */
    closeAccordion(setName) {
        if (this.activeAccordion === setName) {
            this.activeAccordion = null;
        }
    }

    /**
     * Clear all accordion registrations (for cleanup)
     */
    clearAll() {
        this.accordions.clear();
        this.activeAccordion = null;
    }
}

// Create global instance
const weatherAccordionManager = new WeatherAccordionManager();

/**
 * Weather Component Manager - Handles Alpine.js component logic
 */
class WeatherComponent {
    /**
     * Create Alpine.js data object for weather component
     * @param {string} setName - Name of the set
     * @returns {Object} Alpine.js data object
     */
    static createAlpineData(setName) {
        return {
            weatherOpen: false,
            weatherLoading: false,
            weatherData: null,
            weatherError: null,
            setName: setName,

            init() {
                // Register this component with the global manager
                weatherAccordionManager.registerAccordion(this.setName, this);
            },

            destroy() {
                // Unregister this component when destroyed
                weatherAccordionManager.unregisterAccordion(this.setName);

                // Destroy the chart for this set
                const chartId = 'weather-chart-' + this.setName.toLowerCase().replace(/[^a-z0-9]/g, '-');
                WeatherChart.destroy(chartId);
            },

            async toggleWeather() {
                if (this.weatherOpen) {
                    // Closing the accordion
                    this.weatherOpen = false;
                    weatherAccordionManager.closeAccordion(this.setName);
                } else {
                    // Opening the accordion - close others first
                    weatherAccordionManager.openAccordion(this.setName);
                    this.weatherOpen = true;

                    // Load data if needed
                    if (!this.weatherData && !this.weatherLoading) {
                        await this.loadWeatherData();
                    }
                }
            },
            
            async loadWeatherData() {
                this.weatherLoading = true;
                this.weatherError = null;
                
                const result = await WeatherAPI.fetchWeatherData(this.setName);
                
                if (result.success) {
                    this.weatherData = result.data;
                    this.$nextTick(() => {
                        this.renderWeatherChart();
                    });
                } else {
                    this.weatherError = result.error;
                }
                
                this.weatherLoading = false;
            },
            
            renderWeatherChart() {
                if (this.weatherData) {
                    const chartId = 'weather-chart-' + this.setName.toLowerCase().replace(/[^a-z0-9]/g, '-');
                    WeatherChart.render(chartId, this.weatherData);
                }
            }
        };
    }
}

// Export for global use
window.WeatherChart = WeatherChart;
window.WeatherAPI = WeatherAPI;
window.WeatherComponent = WeatherComponent;

// HTMX lifecycle event handlers to prevent memory leaks
document.addEventListener('htmx:beforeSwap', function() {
    // Clean up all chart instances before content replacement
    WeatherChart.destroyAll();

    // Also clean up accordion manager registrations
    if (typeof weatherAccordionManager !== 'undefined') {
        weatherAccordionManager.clearAll();
    }
});

document.addEventListener('htmx:afterSwap', function() {
    // Clean up any orphaned charts after content swap
    setTimeout(() => {
        WeatherChart.cleanupOrphanedCharts();
    }, 100);
});

// Periodic cleanup to catch any missed orphaned charts
setInterval(() => {
    WeatherChart.cleanupOrphanedCharts();
}, 30000); // Every 30 seconds

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    WeatherChart.destroyAll();
});
