// Main project table initialization and event handling
import { initializeDataTable, filterTable } from './datatable-config.js';

// Debug log to verify script loading
// console.log('Project table script loaded:', new Date().toISOString());
// console.log('Project data URL:', projectDataUrl);

$(document).ready(function () {
    // Initialize DataTable
    const dataTable = initializeDataTable(projectDataUrl);

    // Handle window resize
    $(window).resize(function () {
        dataTable.columns.adjust();
    });

    // Enhance search field
    enhanceSearchField();
});

// Function to enhance the search field
function enhanceSearchField() {
    // Get the search input
    const $searchInput = $('.dataTables_filter input');

    // Store the original input attributes
    const inputAttrs = {
        id: $searchInput.attr('id'),
        class: $searchInput.attr('class'),
        placeholder: 'Search projects...',
    };

    // Get the parent container
    const $container = $('.dataTables_filter');

    // Replace the entire content with our custom structure
    $container.html(`
        <div class="relative w-[250px] mr-4">
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="#64748b">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <input type="text" id="${inputAttrs.id}" class="${inputAttrs.class}" placeholder="${inputAttrs.placeholder}" 
                   style="width: 100%; padding: 0.5rem 0.75rem 0.5rem 2.25rem; border: 1px solid #e2e8f0; border-radius: 0.5rem; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);">
            <button class="search-clear-btn" style="display:none; position: absolute; right: 10px; top: 50%; transform: translateY(-50%); z-index: 20; background: none; border: none; color: #64748b; font-size: 1.25rem; cursor: pointer;">×</button>
        </div>
    `);

    // Re-attach event handlers
    const $newSearchInput = $container.find('input');
    const $clearBtn = $container.find('.search-clear-btn');

    // Show/hide clear button based on input content
    $newSearchInput.on('input keyup', function () {
        if ($(this).val()) {
            $clearBtn.show();
        } else {
            $clearBtn.hide();
        }

        // Manually trigger DataTable search
        const table = $('#projects-table').DataTable();
        table.search($(this).val()).draw();
    });

    // Clear search when button is clicked
    $clearBtn.on('click', function () {
        $newSearchInput.val('').trigger('input');
    });
}
