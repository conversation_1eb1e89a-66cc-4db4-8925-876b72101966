from django.urls import path
from . import views

app_name = 'setworkload'

urlpatterns = [
    path('', views.set_workload_view, name='set_workload_view'),
    path('api/data/', views.get_workload_data, name='get_workload_data'),
    path('api/comments/', views.get_comments, name='get_comments'),
    path('api/comments/bulk/', views.get_comments_bulk, name='get_comments_bulk'),
    path('api/comments/add/', views.add_comment, name='add_comment'),
    path('api/status-types/', views.get_status_types, name='get_status_types'),
    path('api/status-changes/', views.get_status_changes, name='get_status_changes'),
    path('api/status-changes/set/', views.set_status_change, name='set_status_change'),
    path('api/status-changes/bulk/', views.get_status_changes_bulk, name='get_status_changes_bulk'),
    path('api/status-changes/history/', views.get_status_changes_history, name='get_status_changes_history'),
]