# Migration for adding workload status models

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


def create_initial_status_types(apps, schema_editor):
    """Create initial status types for the workload status system."""
    WorkloadStatusType = apps.get_model('setworkload', 'WorkloadStatusType')
    
    initial_status_types = [
        {
            'status_code': 'no_activity',
            'status_name': 'No Activity',
            'color_class': 'bg-red-50',
            'description': 'Default status for days with no recorded activity'
        },
        {
            'status_code': 'weather',
            'status_name': 'Weather',
            'color_class': 'bg-blue-100',
            'description': 'Work stopped due to weather conditions'
        },
        {
            'status_code': 'travel',
            'status_name': 'Travel',
            'color_class': 'bg-purple-100',
            'description': 'Set in travel/transit status'
        },
        {
            'status_code': 'idle',
            'status_name': 'Idle',
            'color_class': 'bg-orange-100',
            'description': 'Set idle/waiting for instructions'
        }
    ]
    
    for status_data in initial_status_types:
        WorkloadStatusType.objects.create(**status_data)


def remove_initial_status_types(apps, schema_editor):
    """Remove initial status types (for migration rollback)."""
    WorkloadStatusType = apps.get_model('setworkload', 'WorkloadStatusType')
    WorkloadStatusType.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('setworkload', '0001_add_comments_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkloadStatusType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status_code', models.CharField(help_text='Unique code for the status', max_length=20, unique=True)),
                ('status_name', models.CharField(help_text='Display name for the status', max_length=50)),
                ('color_class', models.CharField(help_text='CSS class for the status color', max_length=50)),
                ('description', models.TextField(blank=True, help_text='Description of what this status means')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this status is available for selection')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, help_text='When this status type was created')),
            ],
            options={
                'db_table': 'workload_status_types',
                'ordering': ['status_name'],
            },
        ),
        migrations.CreateModel(
            name='WorkloadStatusChange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('set_name', models.CharField(help_text='Name of the set this status change relates to', max_length=255)),
                ('date', models.DateField(help_text='Date this status change relates to')),
                ('user_email', models.EmailField(help_text='Email of the user who made this change', max_length=255)),
                ('changed_at', models.DateTimeField(default=django.utils.timezone.now, help_text='When this status change was made')),
                ('previous_status_code', models.CharField(blank=True, help_text='Previous status code before this change', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Optional notes about this status change')),
                ('status_code', models.ForeignKey(help_text='The status code assigned to this day', on_delete=django.db.models.deletion.PROTECT, to='setworkload.workloadstatustype', to_field='status_code')),
            ],
            options={
                'db_table': 'workload_status_changes',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.AddIndex(
            model_name='workloadstatuschange',
            index=models.Index(fields=['set_name', 'date'], name='workload_st_set_nam_a4d8db_idx'),
        ),
        migrations.AddIndex(
            model_name='workloadstatuschange',
            index=models.Index(fields=['user_email'], name='workload_st_user_em_ead521_idx'),
        ),
        migrations.AddIndex(
            model_name='workloadstatuschange',
            index=models.Index(fields=['changed_at'], name='workload_st_changed_4bc1f7_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='workloadstatuschange',
            unique_together={('set_name', 'date')},
        ),
        migrations.RunPython(
            create_initial_status_types,
            remove_initial_status_types,
        ),
    ] 