@tailwind base;
@tailwind components;
@tailwind utilities;

/* add the code bellow */
@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* Activity table with consistent height */
  .activity-section {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .activity-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .activity-table-scroll {
    flex: 1;
    overflow-y: auto;
  }

  .activity-pagination {
    border-top: 1px solid #e5e7eb;
  }

  /* Allow flexible row height for wrapped text */
  #activity-table tr {
    min-height: 40px; /* Minimum height for each row */
  }
}
