import logging
import os
from pathlib import Path

from typing import Dict, Any

import sqlalchemy as sa  # Still needed for telemetry database connection
from django.http import HttpRequest
from google.oauth2 import service_account

from querycache.manager import BigQueryClientWithCache

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define BASE_DIR to point to your project directory
BASE_DIR = Path(__file__).resolve().parent.parent

COMMIT_SHA = os.environ.get("CI_COMMIT_SHA", "")

# Path to your Google BigQuery credentials file
GOOGLE_APPLICATION_CREDENTIALS = os.path.join(
    BASE_DIR, "keys", "workorder-service-acc.json"
)

# Environment variables are loaded by Docker from the env files



def _get_bigquery_client():
    """
    Creates and returns a BigQuery client using the service account credentials file.
    """
    try:
        if os.path.exists(GOOGLE_APPLICATION_CREDENTIALS):
            # Load the credentials
            credentials = service_account.Credentials.from_service_account_file(
                GOOGLE_APPLICATION_CREDENTIALS,
                scopes=["https://www.googleapis.com/auth/cloud-platform"],
            )

            # Create and return the BigQuery client
            return BigQueryClientWithCache(
                credentials=credentials, project=credentials.project_id
            )
        else:
            logger.error(
                f"BigQuery credentials file not found: {GOOGLE_APPLICATION_CREDENTIALS}"
            )
            return None
    except Exception as e:
        logger.error(f"Error creating BigQuery client: {str(e)}")
        return None


def get_bigquery_client():
    """Create a fresh BigQuery client to prevent memory leaks.
    
    Creates a new client instance each time to avoid global state and memory accumulation.
    This is the recommended pattern for BigQuery clients.
    """
    return _get_bigquery_client()


def cleanup_memory(*objects):
    """Simple memory cleanup helper for large objects.
    
    Args:
        *objects: Objects to delete and clean up from memory
    """
    import gc
    for obj in objects:
        try:
            del obj
        except:
            pass
    gc.collect()


def _get_telemetry_db_engine():
    """
    Creates and returns a SQLAlchemy engine for the telemetry database.
    Uses environment variables directly for connection details.
    """
    try:
        # Get database connection settings directly from environment variables
        db_host = os.environ.get("TELEMETRY_DB_HOST")
        db_port = int(os.environ.get("TELEMETRY_DB_PORT", "5432"))
        db_name = os.environ.get("TELEMETRY_DB_NAME")
        db_user = os.environ.get("TELEMETRY_DB_USER")
        db_password = os.environ.get("TELEMETRY_DB_PASSWORD")

        # Remove quotes if present in the password
        if db_password:
            if (db_password.startswith('"') and db_password.endswith('"')) or (
                db_password.startswith("'") and db_password.endswith("'")
            ):
                db_password = db_password[1:-1]

        # Check if all required settings are available
        if not all([db_host, db_name, db_user, db_password]):
            missing = []
            if not db_host:
                missing.append("TELEMETRY_DB_HOST")
            if not db_name:
                missing.append("TELEMETRY_DB_NAME")
            if not db_user:
                missing.append("TELEMETRY_DB_USER")
            if not db_password:
                missing.append("TELEMETRY_DB_PASSWORD")
            logger.error(
                f"Missing required telemetry database settings: {', '.join(missing)}"
            )
            return None

        # Create the database engine with connection recycling to prevent memory leaks
        engine = sa.create_engine(
            sa.URL.create(
                drivername="postgresql+psycopg2",
                host=db_host,
                port=db_port,
                database=db_name,
                username=db_user,
                password=db_password,
            ),
            # Connection recycling settings (no limits, just memory leak prevention)
            pool_pre_ping=True,   # Validate connections before use
            pool_recycle=900,     # Recycle connections after 15 minutes
        )

        # Test the connection
        try:
            connection = engine.connect()
            connection.close()
        except Exception as conn_err:
            logger.error(f"Failed to connect to telemetry database: {str(conn_err)}")
            return None

        return engine
    except Exception as e:
        logger.error(f"Error creating telemetry database engine: {str(e)}")
        return None

TELEMETRY_DB_ENGINE = _get_telemetry_db_engine()

def get_telemetry_db_engine():
    """Returns the telemetry database engine.

    This is not nice way for now the quickest solution. Problem with this we do not
    nicely close the connection and we litter with Global variables.

    But there is a lot code that calls this function.
    """
    return TELEMETRY_DB_ENGINE


# The datatools database connection is now handled by Django's ORM
# See the 'datatools' database configuration in settings.py


def extract_request_params(request: HttpRequest) -> Dict[str, Any]:
    """
    Extract and validate common request parameters.
    Handle both single values and multiple values for select fields.
    """
    # Get parameters from request
    # For multi-select fields, we need to handle both single values and lists
    project_name = request.GET.getlist("project_name") or request.GET.get("project_name")
    service_code = request.GET.getlist("service_code") or request.GET.get("service_code")
    set_name = request.GET.getlist("set_name") or request.GET.get("set_name")
    customer = request.GET.getlist("customer") or request.GET.get("customer")

    params = {
        "project_name": project_name,
        "service_code": service_code,
        "set_name": set_name,  # Already URL decoded by Django
        "customer": customer,
        "activity_date": request.GET.get("activity_date"),
        "workorder_name": request.GET.get("workorder_name"),
    }

    return params


def context_processor(request: HttpRequest) -> Dict[str, Any]:
    # Determine the reset URL based on the current app
    reset_url = "/"

    # Get the current URL path
    path = request.path

    # Set the appropriate reset URL based on the current path
    if path.startswith("/workorders"):
        reset_url = "/workorders/"
    elif path.startswith("/sets-monitoring"):
        reset_url = "/sets-monitoring/"
    elif path.startswith("/setworkload"):
        reset_url = "/setworkload/"
    elif path.startswith("/projects"):
        reset_url = "/projects/"

    return {"commit_sha": COMMIT_SHA, "reset_url": reset_url}


def get_navirec_credentials():
    """
    Get Navirec API credentials from environment variables.
    Uses the same pattern as database connections.

    Returns:
        tuple: (account_id, api_key) or (None, None) if not available
    """
    try:
        # Get Navirec API credentials directly from environment variables
        account_id = os.environ.get("NAVIREC_ACCOUNT_ID")
        api_key = os.environ.get("NAVIREC_API_KEY")

        # Check if both credentials are available
        if not account_id or not api_key:
            missing = []
            if not account_id:
                missing.append("NAVIREC_ACCOUNT_ID")
            if not api_key:
                missing.append("NAVIREC_API_KEY")

            logger.warning(f"Missing Navirec API credentials: {', '.join(missing)}")
            return None, None

        return account_id, api_key

    except Exception as e:
        logger.error(f"Error loading Navirec API credentials: {str(e)}")
        return None, None
