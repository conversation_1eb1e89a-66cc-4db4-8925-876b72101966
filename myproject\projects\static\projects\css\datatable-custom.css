/* DataTables Custom Styling */

/* Search field container */
.dataTables_filter {
    margin-bottom: 1rem;
    margin-right: 0.5rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

/* Search label - hide any text */
.dataTables_filter label {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 500;
    color: transparent;
    /* Make any text transparent */
    margin: 0;
    position: relative;
    width: 250px;
}

/* Hide the search label text - from project_table.html */
.dataTables_filter label {
    font-size: 0 !important;
}

.dataTables_filter input {
    font-size: 0.875rem !important;
}

/* Search input */
.dataTables_filter input {
    padding: 0.5rem 0.75rem 0.5rem 2.25rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    width: 100%;
    font-size: 0.875rem;
    color: #1e293b;
    /* Restore text color for input */
}

/* Add search icon */
.dataTables_filter label::before {
    content: "";
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2364748b'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    z-index: 10;
    pointer-events: none;
}

/* Search input focus */
.dataTables_filter input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    outline: none;
}

/* Search input hover */
.dataTables_filter input:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Search clear button */
.search-clear-btn {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #64748b;
    font-size: 1.25rem;
    line-height: 1;
    cursor: pointer;
    padding: 0;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.15s ease-in-out;
}

.search-clear-btn:hover {
    background-color: rgba(100, 116, 139, 0.1);
    color: #ef4444;
}

/* Pagination controls */
.dataTables_paginate {
    margin-top: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem;
    background-color: #f8fafc;
    border-radius: 0.75rem;
}

/* Pagination buttons */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.75rem !important;
    border-radius: 0.5rem !important;
    cursor: pointer !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
    background: transparent !important;
    transition: all 0.2s ease-in-out !important;
    min-width: 2.5rem !important;
    text-align: center !important;
    border: none !important;
    margin: 0 !important;
}

/* Pagination buttons hover */
.dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current) {
    background-color: #eef0f1 !important;
    color: #374151 !important;
    border: none !important;
}

/* Current page button */
.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #3b82f6 !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background-color: #2563eb !important;
    color: white !important;
    border: none !important;
}

/* Previous/Next buttons */
.dataTables_wrapper .dataTables_paginate .paginate_button.previous,
.dataTables_wrapper .dataTables_paginate .paginate_button.next {
    font-weight: 600 !important;
    color: #374151 !important;
    padding: 0.5rem 1rem !important;
    border: none !important;
}

/* Add arrow styling for smaller screens */
@media (max-width: 640px) {
    .dataTables_wrapper .dataTables_paginate .paginate_button.previous,
    .dataTables_wrapper .dataTables_paginate .paginate_button.next {
        font-size: 0 !important; /* Hide text */
        padding: 0.5rem !important;
        width: 2.5rem !important;
        position: relative !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.previous::before,
    .dataTables_wrapper .dataTables_paginate .paginate_button.next::before {
        content: "";
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 1.25rem;
        height: 1.25rem;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.previous::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23475569'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 19l-7-7 7-7'/%3E%3C/svg%3E");
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.next::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23475569'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5l7 7-7 7'/%3E%3C/svg%3E");
    }
}

/* Disabled pagination buttons */
.dataTables_paginate .paginate_button.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
}

/* Info text (Showing X to Y of Z entries) */
.dataTables_info {
    order: 1;
    margin: 0;
    padding: 0 0.5rem;
    font-size: 0.875rem;
    color: #64748b !important;
    font-weight: 500;
}

/* Workorder table pagination controls */
#workorders-table_wrapper {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

#workorders-table_wrapper .dataTables_info {
    order: 1;
    margin: 0;
    padding: 0 0.5rem;
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers {
    order: 2;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
    background-color: #f8fafc;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button {
    padding: 0.625rem !important;
    border-radius: 0.75rem !important;
    cursor: pointer !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #475569 !important;
    background: white !important;
    background-color: white !important;
    transition: all 0.2s ease-in-out !important;
    min-width: 2.5rem !important;
    height: 2.5rem !important;
    text-align: center !important;
    border: 1px solid #e2e8f0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

@media (min-width: 640px) {
    #workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button {
        padding: 0.625rem 0.875rem !important;
        min-width: 2.75rem !important;
        height: 2.75rem !important;
    }

    #workorders-table_wrapper .dataTables_paginate.paging_full_numbers {
        gap: 0.625rem;
        padding: 0.875rem;
    }
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.ellipsis {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
    color: #64748b !important;
    min-width: 1.5rem !important;
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button:hover:not(.current):not(.disabled):not(.ellipsis) {
    background: #f8fafc !important;
    background-color: #f8fafc !important;
    color: #334155 !important;
    border-color: #cbd5e1 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.current,
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.current:hover {
    background: #2563eb !important;
    background-color: #2563eb !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.first,
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.previous,
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.next,
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.last {
    font-size: 0 !important;
    color: #475569 !important;
    background: white !important;
    background-color: white !important;
    border: 1px solid #e2e8f0 !important;
    position: relative !important;
    min-width: 2.5rem !important;
    width: 2.5rem !important;
}

@media (min-width: 640px) {

    #workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.first,
    #workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.previous,
    #workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.next,
    #workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.last {
        min-width: 2.75rem !important;
        width: 2.75rem !important;
    }
}

/* Navigation button icons */
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.first::before,
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.previous::before,
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.next::before,
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.last::before {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 1.125rem;
    height: 1.125rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.first::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23475569'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M11 19l-7-7 7-7m8 14l-7-7 7-7'/%3E%3C/svg%3E");
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.previous::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23475569'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 19l-7-7 7-7'/%3E%3C/svg%3E");
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.next::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23475569'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5l7 7-7 7'/%3E%3C/svg%3E");
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.last::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23475569'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13 5l7 7-7 7M5 5l7 7-7 7'/%3E%3C/svg%3E");
}

#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.disabled,
#workorders-table_wrapper .dataTables_paginate.paging_full_numbers .paginate_button.disabled:hover {
    opacity: 0.4;
    cursor: not-allowed !important;
    pointer-events: none;
    background: #f8fafc !important;
    background-color: #f8fafc !important;
    border-color: #e2e8f0 !important;
    box-shadow: none !important;
}

/* DataTable wrapper and table layout - from project_table.html */
#projects-table_wrapper {
    width: 100%;
}

#projects-table {
    width: 100% !important;
    table-layout: fixed;
}

.dataTables_scrollBody {
    overflow-x: auto;
    width: 100%;
}

#projects-table th,
#projects-table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Remove default DataTables sorting icons */
table.dataTable thead .sorting:before,
table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:before,
table.dataTable thead .sorting_desc_disabled:after {
    display: none !important;
}

/* Custom sorting icons */
table.dataTable thead th {
    position: relative;
    padding-right: 24px !important;
}

table.dataTable thead th:after {
    position: absolute;
    right: 8px;
    display: block;
}

table.dataTable thead .sorting:after {
    content: "↕";
    opacity: 0.2;
}

table.dataTable thead .sorting_asc:after {
    content: "↑";
}

table.dataTable thead .sorting_desc:after {
    content: "↓";
}

/* Metrics view transitions - from project_view.html */
.metrics-view {
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
}

.metrics-view.hidden {
    display: none;
    opacity: 0;
}