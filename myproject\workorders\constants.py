# BigQuery table names
PROJECT_FIELD_ACTIVITY_TABLE = "`budibase-dev-369406.presentation.project_field_activity`"
PROJECT_WORKORDER_TABLE = "`budibase-dev-369406.presentation.project_workorder`"
REPORT_SETTING_TABLE = "`budibase-dev-369406.presentation.tmp_report_setting`"

# Query templates
RAW_WORKORDER_DATA_QUERY = f"""
SELECT
    start_datetime,
    end_datetime,
    duration_min,
    activity_name,
    activity_type,
    activity_duration_type,
    blade_key,
    wo_name,
    wo_type,
    turbine_name,
    max_all_activity_date AS official_completion_date,
    service_code,
    project_name,
    set_name,
    service_activity_comments,
    service_activity_custom_fields,
    time_limit,
    is_over_time_limit,
    activity_overrun_reason_description
FROM {PROJECT_FIELD_ACTIVITY_TABLE}
WHERE workorder_id = @workorder_id
  AND start_datetime IS NOT NULL
  AND (activity_type = 'STANDARD' OR activity_type = 'DELAY')
ORDER BY start_datetime
"""

WORKORDER_METADATA_QUERY = f"""
SELECT *
FROM {PROJECT_WORKORDER_TABLE}
WHERE workorder_id = @workorder_id
"""

WORKORDERS_QUERY_TEMPLATE = f"""
SELECT
    workorder_id,
    ANY_VALUE(wo_name) as wo_name,
    ANY_VALUE(wo_type) as wo_type,
    ANY_VALUE(project_name) as project_name,
    ANY_VALUE(service_code) as service_code,
    ANY_VALUE(set_name) as set_name,
    ANY_VALUE(is_completed) as is_completed,
    MAX(max_all_activity_date) as max_all_activity_date,
    MIN(start_datetime) as min_start_date
FROM {PROJECT_FIELD_ACTIVITY_TABLE}
WHERE 1=1
AND (activity_type = 'STANDARD' OR activity_type = 'DELAY')
{{project_filter}}
{{service_filter}}
{{set_filter}}
{{customer_filter}}
{{name_filter}}
{{id_filter}}
GROUP BY workorder_id
HAVING 1=1
{{date_filter}}
ORDER BY max_all_activity_date DESC
{{limit_clause}}
"""

DROPDOWN_QUERY = f"""
SELECT
    'project' AS dropdown_type,
    project_name AS value,
    project_name AS label
FROM {PROJECT_FIELD_ACTIVITY_TABLE}
WHERE LOWER(project_name) NOT LIKE '%test%'
GROUP BY project_name

UNION ALL

SELECT
    'service' AS dropdown_type,
    service_code AS value,
    service_code AS label
FROM {PROJECT_FIELD_ACTIVITY_TABLE}
WHERE service_code IS NOT NULL
GROUP BY service_code

UNION ALL

SELECT
    'set' AS dropdown_type,
    set_name AS value,
    set_name AS label
FROM {PROJECT_FIELD_ACTIVITY_TABLE}
WHERE set_name IS NOT NULL
GROUP BY set_name

UNION ALL

SELECT
    'customer' AS dropdown_type,
    client_name AS value,
    client_name AS label
FROM {PROJECT_WORKORDER_TABLE}
WHERE client_name IS NOT NULL
GROUP BY client_name

"""

SINGLE_WORKORDER_QUERY = f"""
SELECT
    workorder_id,
    wo_name,
    wo_type,
    project_name,
    service_code,
    set_name,
    turbine_name,
    is_completed,
    max_all_activity_date
FROM {PROJECT_FIELD_ACTIVITY_TABLE}
WHERE workorder_id = @workorder_id
AND (activity_type = 'STANDARD' OR activity_type = 'DELAY')
LIMIT 1
"""

DAILY_REPORTS_QUERY = f"""
WITH workorder_timeframe AS (
    SELECT
        MIN(start_datetime) as min_start_date,
        MAX(end_datetime) as max_end_date,
        FORMAT_DATE('%Y-%m-%d', DATE(MIN(start_datetime))) as start_date_str,
        FORMAT_DATE('%Y-%m-%d', DATE(MAX(end_datetime))) as end_date_str
    FROM {PROJECT_FIELD_ACTIVITY_TABLE}
    WHERE workorder_id = @workorder_id
)

SELECT
    CONCAT('https://portal.aerones.com/html-reports/daily.html?rid=', report_token) as report_url,
    created_at,
    report_type,
    dates
FROM {REPORT_SETTING_TABLE}, workorder_timeframe
WHERE REGEXP_CONTAINS(workorder_ids, CAST(@workorder_id AS STRING))
AND report_type = 'DAILY'
AND archived = true
AND JSON_EXTRACT_ARRAY(dates)[OFFSET(0)] IS NOT NULL
AND (
    -- Get reports with dates that fall within the workorder's timeframe
    -- Check the first date in the array (usually there's only one, but in multi-day reports there could be several)
    PARSE_DATE('%Y-%m-%d', JSON_EXTRACT_SCALAR(dates, '$[0]')) BETWEEN
        PARSE_DATE('%Y-%m-%d', workorder_timeframe.start_date_str) AND
        PARSE_DATE('%Y-%m-%d', workorder_timeframe.end_date_str)
    -- If there's a second date in the array (for multi-day reports), check that too
    OR (
        JSON_EXTRACT_SCALAR(dates, '$[1]') IS NOT NULL
        AND PARSE_DATE('%Y-%m-%d', JSON_EXTRACT_SCALAR(dates, '$[1]')) BETWEEN
            PARSE_DATE('%Y-%m-%d', workorder_timeframe.start_date_str) AND
            PARSE_DATE('%Y-%m-%d', workorder_timeframe.end_date_str)
    )
)
ORDER BY created_at DESC
"""

WORKORDER_SUMMARY_QUERY = f"""
WITH workorder_metadata AS (
    -- Get direct and delay minutes from workorder table (matches metadata exactly)
    SELECT
        workorder_id,
        wo_name,
        COALESCE(duration_in_minutes, 0) as direct_minutes,
        COALESCE(delay_duration_in_minutes, 0) as delay_minutes
    FROM {PROJECT_WORKORDER_TABLE}
    WHERE workorder_id IN UNNEST(@workorder_ids)
),
blade_hours AS (
    -- Get blade-specific minutes from activity table (only source with blade breakdown)
    SELECT
        workorder_id,
        SUM(CASE WHEN (activity_type = 'STANDARD' OR activity_type = 'DELAY') AND activity_duration_type = 'PERIODICAL' AND blade_key = 'A' THEN duration_min ELSE 0 END) as blade_a_minutes,
        SUM(CASE WHEN (activity_type = 'STANDARD' OR activity_type = 'DELAY') AND activity_duration_type = 'PERIODICAL' AND blade_key = 'B' THEN duration_min ELSE 0 END) as blade_b_minutes,
        SUM(CASE WHEN (activity_type = 'STANDARD' OR activity_type = 'DELAY') AND activity_duration_type = 'PERIODICAL' AND blade_key = 'C' THEN duration_min ELSE 0 END) as blade_c_minutes,
        SUM(CASE WHEN (activity_type = 'STANDARD' OR activity_type = 'DELAY') AND activity_duration_type = 'PERIODICAL' AND (blade_key = 'Common' OR blade_key IS NULL) THEN duration_min ELSE 0 END) as common_minutes
    FROM {PROJECT_FIELD_ACTIVITY_TABLE}
    WHERE workorder_id IN UNNEST(@workorder_ids)
        AND start_datetime IS NOT NULL
        AND end_datetime IS NOT NULL
        AND (activity_type = 'STANDARD' OR activity_type = 'DELAY')
    GROUP BY workorder_id
)
SELECT
    wm.workorder_id,
    wm.wo_name,
    wm.direct_minutes,
    wm.delay_minutes,
    COALESCE(bh.blade_a_minutes, 0) as blade_a_minutes,
    COALESCE(bh.blade_b_minutes, 0) as blade_b_minutes,
    COALESCE(bh.blade_c_minutes, 0) as blade_c_minutes,
    COALESCE(bh.common_minutes, 0) as common_minutes
FROM workorder_metadata wm
LEFT JOIN blade_hours bh ON wm.workorder_id = bh.workorder_id
ORDER BY wm.workorder_id
"""