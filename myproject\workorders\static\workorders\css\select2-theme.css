/* Select2 Tailwind CSS customization */
.select2-container--tailwindcss-3 {
    cursor: pointer !important;
    height: 38px !important;
    overflow: visible !important;
}

.select2-container--tailwindcss-3.select2-container--open .select2-dropdown {
    overflow-y: hidden !important;
}

/* Focus and open states */
.select2-container--tailwindcss-3.select2-container--focus .select2-selection--multiple,
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple {
    border-color: #3B82F6 !important;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5) !important;
}

/* Default state - not focused or open */
.select2-container--tailwindcss-3:not(.select2-container--open):not(.select2-container--focus) .select2-selection--multiple {
    border-color: #d1d5db !important;
    box-shadow: none !important;
}

/* Form input focus states to match Select2 */
.form-input:focus, .form-select:focus {
    border-color: #3B82F6 !important;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5) !important;
}

/* Dropdown options */
.select2-container--tailwindcss-3 .select2-results__option {
    cursor: pointer !important;
}

.select2-container--tailwindcss-3 .select2-results__option--highlighted {
    background-color: #3B82F6 !important;
    color: white !important;
}

.select2-container--tailwindcss-3 .select2-results__option--selected:after {
    color: #3B82F6 / var(--tw-text-opacity, 1);
}

/* Selection container */
.select2-container--tailwindcss-3 .select2-selection--multiple {
    height: 38px !important;
    min-height: 38px !important;
    max-height: 38px !important;
    overflow-y: hidden !important;
    position: relative !important;
    cursor: pointer !important;
    padding: 0 !important;
    border-color: #d1d5db !important;
    display: flex !important;
    align-items: center !important;
    overflow: visible !important;
}

/* Form input height consistency */
.form-input {
    height: 38px !important;
}

/* Dropdown arrow */
.select2-container--tailwindcss-3 .select2-selection--multiple::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2364748b'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    pointer-events: none;
    z-index: 1;
}

/* Rotate arrow when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple::after {
    transform: translateY(-50%) rotate(180deg);
}

/* Selection rendered area */
.select2-container--tailwindcss-3 .select2-selection--multiple .select2-selection__rendered {
    display: flex !important;
    flex-wrap: wrap !important;
    overflow-y: hidden !important;
    align-items: center !important;
    height: 100% !important;
    padding-left: 0 !important;
    position: relative !important;
    overflow: visible !important;
    z-index: auto !important;
}

/* Adjust rendered area when dropdown is open to ensure proper search field positioning */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-selection__rendered {
    position: static !important;
}

/* Show search field only when dropdown is open */
.select2-container--tailwindcss-3 .select2-selection--multiple .select2-search--inline {
    display: none !important;
}

/* Show search field when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-search--inline {
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
}

/* Show search input when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-search--inline .select2-search__field {
    width: calc(100% - 30px) !important; /* Account for dropdown arrow */
    margin: 0 !important;
    padding: 0 8px !important;
    height: 36px !important;
    line-height: 36px !important;
    font-size: 0.875rem !important;
    color: #4B5563 !important;
    text-align: left !important;
    position: relative !important;
    left: 0 !important;
}

/* Hide selection choices (tags) in closed state */
.select2-container--tailwindcss-3:not(.select2-container--open) .select2-selection--multiple .select2-selection__choice {
    display: none !important;
}

/* Style for the custom selection display text */
.select2-container--tailwindcss-3 .select2-selection--multiple .selection-display-text {
    color: #4B5563;
    font-size: 0.875rem;
    line-height: 36px;
    padding-left: 8px;
    white-space: nowrap;
    overflow: visible;
    width: calc(100% - 20px);
    pointer-events: none;
}

/* Hide custom text when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .selection-display-text {
    display: none !important;
}

/* Ensure tooltip hover works on parent containers */
.select2-container--tailwindcss-3 {
    overflow: visible !important;
}

.select2-container--tailwindcss-3 .select2-selection--multiple {
    overflow: visible !important;
}

.select2-container--tailwindcss-3 .select2-selection__rendered {
    overflow: visible !important;
    z-index: auto !important;
}