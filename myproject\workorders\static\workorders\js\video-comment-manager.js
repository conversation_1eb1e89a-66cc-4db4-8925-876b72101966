// Video Comment Manager for video activities
const VideoCommentManager = {
    currentVideoData: null,
    currentWorkorderId: null,
    currentUserEmail: null,

    /**
     * Initialize the comment system for the current video
     * @param {Object} videoData - Video activity data
     * @param {number} workorderId - Current workorder ID
     */
    initialize(videoData, workorderId) {
        this.currentVideoData = videoData;
        this.currentWorkorderId = workorderId;
        this.setupCommentButton();
    },

    /**
     * Set up the comment button in the video section header
     */
    setupCommentButton() {
        // Remove existing comment button if any
        const existingBtn = document.getElementById('video-comment-btn');
        if (existingBtn) {
            existingBtn.remove();
        }

        // Find the dedicated button container next to the header
        const buttonContainer = document.getElementById('video-comments-button-container');
        if (!buttonContainer) return;

        // Create button wrapper with relative positioning for badge
        const buttonWrapper = document.createElement('div');
        buttonWrapper.className = 'relative';

        // Create comment button
        const commentBtn = document.createElement('button');
        commentBtn.id = 'video-comment-btn';
        commentBtn.className = 'px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors flex items-center gap-2';
        commentBtn.title = 'View or Add Comments';
        commentBtn.innerHTML = `
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
            </svg>
            Comments
        `;
        
        // Create comment count badge (initially hidden)
        const commentBadge = document.createElement('span');
        commentBadge.id = 'video-comment-badge';
        commentBadge.className = 'absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center hidden z-10';
        commentBadge.textContent = '0';
        
        commentBtn.onclick = () => this.openCommentPopup();
        
        // Assemble button with badge
        buttonWrapper.appendChild(commentBtn);
        buttonWrapper.appendChild(commentBadge);
        
        // Add the button wrapper to the button container
        buttonContainer.appendChild(buttonWrapper);
        
        // Check if button should move below based on content width
        setTimeout(() => this.adjustButtonPosition(), 100); // Small delay to ensure elements are rendered
        
        // Listen for window resize to readjust with debouncing
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => this.adjustButtonPosition(), 150);
        });
        
        // Load and display comment count
        this.loadCommentCount();
    },

    /**
     * Adjust button position based on content width
     */
    adjustButtonPosition() {
        const headerContainer = document.getElementById('video-header-container');
        const buttonContainer = document.getElementById('video-comments-button-container');
        const fallbackContainer = document.getElementById('video-comments-button-fallback');
        const activityContent = document.getElementById('activity-details-content');
        
        // Find button wrapper in either location
        let buttonWrapper = buttonContainer?.querySelector('.relative') || fallbackContainer?.querySelector('.relative');
        
        if (!headerContainer || !buttonContainer || !fallbackContainer || !buttonWrapper || !activityContent) {
            return;
        }

        // Force a reflow to ensure accurate measurements
        headerContainer.offsetHeight;

        // Check if the activity details have wrapped to multiple lines
        const activityChildren = Array.from(activityContent.children);
        
        // If we have children, check if any are on a second line
        let hasWrapped = false;
        if (activityChildren.length > 2) { // Only check if we have more than 2 items
            const firstChildRect = activityChildren[0].getBoundingClientRect();
            const lastChildRect = activityChildren[activityChildren.length - 1].getBoundingClientRect();
            if (Math.abs(lastChildRect.top - firstChildRect.top) > 10) { // Increased tolerance to 10px
                hasWrapped = true;
            }
        }

        // Also check if the header container is too narrow for comfortable spacing
        const headerWidth = headerContainer.getBoundingClientRect().width;
        const isNarrow = headerWidth < 400; // More conservative threshold

        const shouldMoveBelow = hasWrapped || isNarrow;
        const isCurrentlyBelow = buttonWrapper.parentElement === fallbackContainer;

        if (shouldMoveBelow && !isCurrentlyBelow) {
            // Move button below header
            fallbackContainer.appendChild(buttonWrapper);
            fallbackContainer.classList.remove('hidden');
            // Make button full width when below
            const button = buttonWrapper.querySelector('#video-comment-btn');
            if (button) {
                button.className = button.className.includes('w-full') ? button.className : button.className + ' w-full justify-center';
            }
        } else if (!shouldMoveBelow && isCurrentlyBelow) {
            // Move button back to header
            buttonContainer.appendChild(buttonWrapper);
            fallbackContainer.classList.add('hidden');
            // Reset button width when in header
            const button = buttonWrapper.querySelector('#video-comment-btn');
            if (button) {
                button.className = button.className.replace(/\s*w-full\s*/g, ' ').replace(/\s*justify-center\s*/g, ' ').replace(/\s+/g, ' ').trim();
            }
        }
    },

    /**
     * Load and display comment count
     */
    async loadCommentCount() {
        if (!this.currentVideoData || !this.currentWorkorderId) {
            return;
        }

        try {
            const params = new URLSearchParams({
                blade: this.currentVideoData.blade,
                activity_type: this.currentVideoData.activity_type,
                start_datetime: this.currentVideoData.start
            });

            const response = await fetch(`/workorders/workorder/${this.currentWorkorderId}/video-comments/?${params}`);
            const data = await response.json();

            if (data.success) {
                this.updateCommentBadge(data.comments.length);
            }
        } catch (error) {
            console.error('Error loading comment count:', error);
        }
    },

    /**
     * Update the comment count badge
     * @param {number} count - Number of comments
     */
    updateCommentBadge(count) {
        const badge = document.getElementById('video-comment-badge');
        if (!badge) return;

        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count.toString();
            badge.classList.remove('hidden');
        } else {
            badge.classList.add('hidden');
        }
    },

    /**
     * Open the comment popup
     */
    async openCommentPopup() {
        if (!this.currentVideoData || !this.currentWorkorderId) {
            alert('No video activity data available');
            return;
        }

        try {
            // Fetch existing comments
            const params = new URLSearchParams({
                blade: this.currentVideoData.blade,
                activity_type: this.currentVideoData.activity_type,
                start_datetime: this.currentVideoData.start
            });

            const response = await fetch(`/workorders/workorder/${this.currentWorkorderId}/video-comments/?${params}`);
            const data = await response.json();

            if (data.success) {
                this.showCommentPopup(data.comments, data.current_user_email);
            } else {
                console.error('Error fetching comments:', data.error);
                alert('Error loading comments. Please try again.');
            }
        } catch (error) {
            console.error('Error fetching comments:', error);
            alert('Error loading comments. Please try again.');
        }
    },

    /**
     * Show the comment popup with existing comments
     * @param {Array} comments - Array of comment objects
     * @param {string} currentUserEmail - Current user's email
     */
    showCommentPopup(comments, currentUserEmail) {
        // Store current user email for use in other methods
        this.currentUserEmail = currentUserEmail;
        
        // Remove existing popup if any
        this.closeCommentPopup();

        // Create popup overlay
        const overlay = document.createElement('div');
        overlay.id = 'video-comment-popup-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.onclick = (e) => {
            if (e.target === overlay) {
                this.closeCommentPopup();
            }
        };

        // Create popup container
        const popup = document.createElement('div');
        popup.className = 'bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden';
        
                 // Create popup content
        popup.innerHTML = `
            <div class="p-6">
                <!-- Header Section -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-1">
                        <h3 class="text-lg font-semibold text-gray-900">Comments for ${this.currentVideoData.activity_type} - Blade ${this.currentVideoData.blade}</h3>
                        <button id="close-video-comment-popup" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        On ${this.formatActivityDateTime(this.currentVideoData.start)} at ${this.formatActivityTime(this.currentVideoData.start)}
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="mb-6">
                    <div id="video-comments-list" class="max-h-60 overflow-y-auto">
                        ${this.renderComments(comments, currentUserEmail)}
                    </div>
                </div>

                <!-- Add Comment Section -->
                <div class="border-t pt-4">
                    <textarea
                        id="new-video-comment-text"
                        placeholder="Add a comment..."
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="3"
                    ></textarea>

                    <div class="flex justify-end space-x-3 mt-3">
                        <button
                            id="cancel-video-comment"
                            class="px-4 py-2 text-blue-600 hover:text-blue-800 font-medium"
                        >
                            Cancel
                        </button>
                        <button
                            id="submit-video-comment"
                            class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled
                        >
                            Comment
                        </button>
                    </div>
                </div>
            </div>
        `;

        overlay.appendChild(popup);
        document.body.appendChild(overlay);

        // Add event listeners
        this.setupPopupEventListeners();
        this.setupDeleteEventListeners();
    },

    /**
     * Format activity date for display (header)
     * @param {string} dateTimeStr - ISO datetime string
     * @returns {string} Formatted date string
     */
    formatActivityDateTime(dateTimeStr) {
        try {
            const date = new Date(dateTimeStr);
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });
        } catch (error) {
            return dateTimeStr;
        }
    },

    /**
     * Format activity time for display (header)
     * @param {string} dateTimeStr - ISO datetime string
     * @returns {string} Formatted time string
     */
    formatActivityTime(dateTimeStr) {
        try {
            const date = new Date(dateTimeStr);
            return date.toLocaleTimeString('en-GB', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        } catch (error) {
            return '';
        }
    },

    /**
     * Format comment timestamp for display in user's timezone
     * @param {string} isoString - ISO datetime string
     * @returns {string} Formatted timestamp
     */
    formatCommentTimestamp(isoString) {
        try {
            const date = new Date(isoString);
            const options = {
                year: 'numeric',
                month: '2-digit', 
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            };
            
            const formatted = date.toLocaleString('en-GB', options);
            return formatted.replace(',', '');
        } catch (error) {
            return 'Invalid date';
        }
    },

    /**
     * Render comments HTML to match setworkload design
     * @param {Array} comments - Array of comment objects
     * @param {string} currentUserEmail - Current user's email
     * @returns {string} HTML string
     */
    renderComments(comments, currentUserEmail) {
        if (comments.length === 0) {
            return '<div class="text-gray-500 text-sm">No comments yet.</div>';
        }

        return comments.map(comment => {
            const canDelete = comment.user_email === currentUserEmail;
                         const deleteButton = canDelete ? `
                 <button 
                     class="delete-comment-btn ml-2 text-red-500 hover:text-red-700 transition-colors"
                     data-comment-id="${comment.id}"
                     title="Delete comment"
                 >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            ` : '';

                         return `
                 <div class="mb-4 pb-4 border-b border-gray-100 last:border-b-0">
                    <div class="flex items-start mb-2">
                        <svg class="w-4 h-4 mr-2 text-gray-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <div class="flex flex-col flex-1">
                            <div class="flex items-center justify-between">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-900">${comment.user_email}</span>
                                    <span class="text-xs text-gray-500">${this.formatCommentTimestamp(comment.created_at)}</span>
                                </div>
                                ${deleteButton}
                            </div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-700 ml-6">
                        ${this.escapeHtml(comment.comment_text)}
                    </div>
                </div>
            `;
        }).join('');
    },

    /**
     * Escape HTML to prevent XSS attacks
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * Set up event listeners for the popup
     */
    setupPopupEventListeners() {
        const closeBtn = document.getElementById('close-video-comment-popup');
        const cancelBtn = document.getElementById('cancel-video-comment');
        const submitBtn = document.getElementById('submit-video-comment');
        const textarea = document.getElementById('new-video-comment-text');

        closeBtn.onclick = () => this.closeCommentPopup();
        cancelBtn.onclick = () => this.closeCommentPopup();
        submitBtn.onclick = () => this.submitComment();

        // Enable/disable submit button based on textarea content
        textarea.oninput = () => {
            const hasText = textarea.value.trim().length > 0;
            submitBtn.disabled = !hasText;
            submitBtn.className = hasText 
                ? 'px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium'
                : 'px-4 py-2 bg-gray-500 text-white rounded-lg font-medium opacity-50 cursor-not-allowed';
        };

        // Handle Enter key (Ctrl+Enter to submit)
        textarea.onkeydown = (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                if (!submitBtn.disabled) {
                    this.submitComment();
                }
            }
        };
    },

    /**
     * Submit a new comment
     */
    async submitComment() {
        const textarea = document.getElementById('new-video-comment-text');
        const submitBtn = document.getElementById('submit-video-comment');
        const commentText = textarea.value.trim();

        if (!commentText) {
            return;
        }

        // Disable submit button during submission
        submitBtn.disabled = true;
        submitBtn.textContent = 'Submitting...';

        try {
            const response = await fetch(`/workorders/workorder/${this.currentWorkorderId}/video-comments/add/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    blade: this.currentVideoData.blade,
                    activity_type: this.currentVideoData.activity_type,
                    start_datetime: this.currentVideoData.start,
                    comment_text: commentText
                })
            });

            const data = await response.json();

            if (data.success) {
                // Refresh the popup with updated comments
                this.openCommentPopup();
                // Update the comment badge count
                this.loadCommentCount();
            } else {
                console.error('Error adding comment:', data.error);
                alert('Error adding comment. Please try again.');
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Comment';
            }
        } catch (error) {
            console.error('Error adding comment:', error);
            alert('Error adding comment. Please try again.');
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.textContent = 'Comment';
        }
    },

    /**
     * Set up event listeners for delete buttons
     */
    setupDeleteEventListeners() {
        const deleteButtons = document.querySelectorAll('.delete-comment-btn');
        deleteButtons.forEach(button => {
            button.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                const commentId = button.dataset.commentId;
                this.confirmDeleteComment(commentId);
            };
        });
    },

    /**
     * Show confirmation dialog and delete comment if confirmed
     * @param {string} commentId - ID of the comment to delete
     */
    confirmDeleteComment(commentId) {
        if (confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
            this.deleteComment(commentId);
        }
    },

    /**
     * Delete a comment
     * @param {string} commentId - ID of the comment to delete
     */
    async deleteComment(commentId) {
        try {
            const response = await fetch(`/workorders/workorder/${this.currentWorkorderId}/video-comments/${commentId}/delete/`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (data.success) {
                // Refresh the popup with updated comments
                this.openCommentPopup();
                // Update the comment badge count
                this.loadCommentCount();
            } else {
                console.error('Error deleting comment:', data.error);
                alert('Error deleting comment: ' + (data.error || 'Please try again.'));
            }
        } catch (error) {
            console.error('Error deleting comment:', error);
            alert('Error deleting comment. Please try again.');
        }
    },

    /**
     * Close the comment popup
     */
    closeCommentPopup() {
        const overlay = document.getElementById('video-comment-popup-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
}; 