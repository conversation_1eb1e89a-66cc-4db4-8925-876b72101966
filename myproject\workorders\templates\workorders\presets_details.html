{% load static %}
<script src="{% static 'workorders/js/presets_details.js' %}"></script>

{% if preset_details_data %}
<div class="bg-white shadow-lg rounded-lg p-4" id="preset-details-container">
    <!-- Title - hidden by default, shown when presets are displayed -->
    <div class="mb-4 hidden" id="preset-details-header">
        <h2 class="text-2xl font-bold">Presets Details</h2>
        <p class="text-xs text-gray-600 mt-1" id="preset-type-subtitle"></p>
    </div>
    
    <!-- Default message when no timestamp is selected -->
    <div id="preset-details-message" class="text-center py-12">
        <p class="text-lg text-gray-500">Click a preset activity in the Telemetry chart to view data</p>
    </div>
    

    
    <!-- Hidden by default - Group presets by group_key (hash + type) -->
    {% regroup preset_details_data.stages by group_key as preset_groups %}
    
    <div class="space-y-4 hidden" id="preset-details-content">
        {% for group in preset_groups %}
        <div class="border border-gray-200 rounded-lg" data-group-key="{{ group.grouper }}" data-hash="{{ group.list.0.hash }}" data-filter-timestamp="{% for meta in preset_details_data.metadata %}{% if meta.group_key == group.grouper %}{{ meta.filter_timestamp|date:'c' }}{% endif %}{% endfor %}">
            <!-- Accordion Header -->
            <button onclick="toggleAccordion('preset-{{ forloop.counter }}')" 
                    class="w-full px-4 py-3 text-left bg-gray-50 hover:bg-gray-100 rounded-t-lg focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-between">
                <div class="grid grid-cols-[1fr_auto] gap-3 flex-1 min-w-0 items-center">
                    <span class="hidden" data-timestamp="{{ group.list.0.timestamp|date:'c' }}" data-hash="{{ group.list.0.hash }}" data-group-key="{{ group.grouper }}"></span>
                    
                    <!-- Preset Name (takes available space, truncates if needed) -->
                    <div class="min-w-0">
                        {% for meta in preset_details_data.metadata %}
                            {% if meta.group_key == group.grouper %}
                                {% if meta.name == "__NULL_NAME__" %}
                                    <!-- No initialization name available -->
                                    {% if group.grouper|slice:"-9:" == "_applying" %}
                                        <span class="text-sm font-medium text-gray-500 italic truncate block" title="No name available for this preset from initializing">
                                            null
                                        </span>
                                    {% else %}
                                        <span class="text-sm font-medium text-gray-500 italic truncate block" title="Preset's name from initializing">
                                            null
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <!-- Has initialization name -->
                                    <span class="text-sm font-medium text-gray-900 truncate block" title="Preset's name from initializing">
                                        {{ meta.name }}
                                    </span>
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- Hash Display (fixed width, always aligned) -->
                    <div class="flex items-center">
                        <span class="inline-flex items-center justify-center px-3 py-2 text-xs font-mono text-gray-500 bg-gray-50 rounded-l border border-r-0"
                              title="{{ group.list.0.hash }}">
                            {{ group.list.0.hash|slice:":8" }}
                        </span>
                        <span onclick="event.stopPropagation(); copyHashToClipboard('{{ group.list.0.hash }}')" 
                              class="inline-flex items-center justify-center py-2 px-3 text-gray-500 hover:text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-r border transition-colors cursor-pointer"
                              title="Copy preset's hash">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
                            </svg>
                        </span>
                    </div>
                </div>
                <svg id="preset-{{ forloop.counter }}-icon" class="w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            
            <!-- Accordion Content -->
            <div id="preset-{{ forloop.counter }}-content" class="hidden">
                <div class="p-4">
                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-200 mb-4">
                        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                            <button onclick="showTab('preset-{{ forloop.counter }}', 'stages')" 
                                    id="preset-{{ forloop.counter }}-stages-tab" 
                                    class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm tab-button active border-blue-500 text-blue-600">
                                Stages
                                <span class="bg-blue-100 text-blue-600 py-0.5 px-2 rounded-full text-xs font-medium ml-2">{{ group.list|length }}</span>
                            </button>
                            <button onclick="showTab('preset-{{ forloop.counter }}', 'heights')" 
                                    id="preset-{{ forloop.counter }}-heights-tab" 
                                    class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm tab-button">
                                Heights
                                <span class="bg-green-100 text-green-600 py-0.5 px-2 rounded-full text-xs font-medium ml-2" id="preset-{{ forloop.counter }}-heights-count">
                                    0
                                </span>
                            </button>
                            <button onclick="showTab('preset-{{ forloop.counter }}', 'metadata')" 
                                    id="preset-{{ forloop.counter }}-metadata-tab" 
                                    class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm tab-button">
                                Metadata
                                <span class="bg-purple-100 text-purple-600 py-0.5 px-2 rounded-full text-xs font-medium ml-2" id="preset-{{ forloop.counter }}-metadata-count">
                                    0
                                </span>
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="mt-4">
                        <!-- Stages Tab -->
                        <div id="preset-{{ forloop.counter }}-stages-content" class="tab-content">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stage</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Torque</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pitch</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balancer</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Main Piston Pressure</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Main Piston State</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Force Axis</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for stage in group.list %}
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ stage.stage }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {% if stage.torque is None or stage.torque == "" %}-{% else %}{{ stage.torque }}{% endif %}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {% if stage.pitch is None or stage.pitch == "" %}-{% else %}{{ stage.pitch }}{% endif %}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ stage.balancer|default:"-"|safe }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {% if stage.main_piston_pressure is None or stage.main_piston_pressure == "" %}-{% else %}{{ stage.main_piston_pressure }}{% endif %}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {% if stage.main_piston_state is None or stage.main_piston_state == "" %}-{% else %}{{ stage.main_piston_state }}{% endif %}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {% if stage.force_axis is None or stage.force_axis == "" %}-{% else %}{{ stage.force_axis }}{% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Heights Tab -->
                        <div id="preset-{{ forloop.counter }}-heights-content" class="tab-content hidden">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Height</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Height Value</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Side Piston Pressure</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for height in preset_details_data.heights %}
                                            {% if height.group_key == group.grouper %}
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ height.height }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ height.active|default:"-" }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {% if height.height_value is None or height.height_value == "" %}-{% else %}{{ height.height_value }}{% endif %}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {% if height.side_piston_pressure is None or height.side_piston_pressure == "" %}-{% else %}{{ height.side_piston_pressure }}{% endif %}
                                                </td>
                                            </tr>
                                            {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Metadata Tab -->
                        <div id="preset-{{ forloop.counter }}-metadata-content" class="tab-content hidden">
                            <div class="overflow-x-auto">
                                {% comment %}Check if this group contains initializing presets{% endcomment %}
                                {% if group.grouper|slice:"-13:" == "_initializing" %}
                                    {% comment %}For initializing presets, show metadata values or no data message{% endcomment %}
                                    {% with meta=preset_details_data.metadata|first %}
                                        {% for metadata in preset_details_data.metadata %}
                                            {% if metadata.group_key == group.grouper %}
                                                {% if metadata.blade or metadata.extra or metadata.material %}
                                                    <table class="min-w-full divide-y divide-gray-200">
                                                        <thead class="bg-gray-50">
                                                            <tr>
                                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Blade</th>
                                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Extra</th>
                                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="bg-white divide-y divide-gray-200">
                                                            <tr>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                                    {{ metadata.metadata_name|default:"-" }}
                                                                </td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                    {{ metadata.blade|default:"-" }}
                                                                </td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                    {% if metadata.extra is None %}None{% elif metadata.extra == "" %}-{% else %}{{ metadata.extra }}{% endif %}
                                                                </td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                    {{ metadata.material|default:"-" }}
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                {% else %}
                                                    <div class="text-center py-8">
                                                        <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                                                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                            </svg>
                                                        </div>
                                                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Metadata Available</h3>
                                                        <p class="text-sm text-gray-500">
                                                            Metadata values are not available for this preset.
                                                        </p>
                                                    </div>
                                                {% endif %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endwith %}
                                {% else %}
                                    {% comment %}For applying presets, show metadata values or no data message{% endcomment %}
                                    {% with meta=preset_details_data.metadata|first %}
                                        {% for metadata in preset_details_data.metadata %}
                                            {% if metadata.group_key == group.grouper %}
                                                {% if metadata.blade or metadata.extra or metadata.material %}
                                                    <table class="min-w-full divide-y divide-gray-200">
                                                        <thead class="bg-gray-50">
                                                            <tr>
                                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Blade</th>
                                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Extra</th>
                                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="bg-white divide-y divide-gray-200">
                                                            <tr>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                                    {{ metadata.metadata_name|default:"-" }}
                                                                </td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                    {{ metadata.blade|default:"-" }}
                                                                </td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                    {% if metadata.extra is None %}None{% elif metadata.extra == "" %}-{% else %}{{ metadata.extra }}{% endif %}
                                                                </td>
                                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                    {{ metadata.material|default:"-" }}
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                {% else %}
                                                    <div class="text-center py-8">
                                                        <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                                                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                            </svg>
                                                        </div>
                                                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Metadata Available</h3>
                                                        <p class="text-sm text-gray-500">
                                                            Metadata values are not available for this preset.
                                                        </p>
                                                    </div>
                                                {% endif %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endwith %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

{% endif %} 