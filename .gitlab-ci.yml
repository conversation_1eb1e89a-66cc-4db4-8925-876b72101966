variables:
  DOCKER_DRIVER: overlay2
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""

services:
  - docker:dind

stages:
  - build
  - deploy
  - after


.dev-environment:
  environment: datatools-dev
  variables:
    DOCKER_IMAGE: $CI_REGISTRY_IMAGE/datatools-dev
    CI_ENVIRONMENT_NAME: datatools-dev
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_BRANCH == "dev"


.prod-environment:
  environment: datatools-prod
  variables:
    DOCKER_IMAGE: $CI_REGISTRY_IMAGE/datatools-prod
    CI_ENVIRONMENT_NAME: datatools-prod


.build-job:
  stage: build
  image: docker:latest
  tags:
    - calc
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $DOCKER_IMAGE ./myproject
    - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:latest
    - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$CI_COMMIT_SHA
    - docker push $DOCKER_IMAGE:latest
    - docker push $DOCKER_IMAGE:$CI_COMMIT_SHA


.deploy-job:
  stage: deploy
  image: docker:latest
  tags:
    - calc
  variables:
    DEPLOY_TMP_DIR: /tmp/datatools
    DEPLOY_WORKING_DIR: /opt/datatools
  services:
    - docker:dind
  script:
    - chmod +x deployment.sh
    - ./deployment.sh
    - echo "Deployment completed!"


build-dev:
  extends: [.dev-environment, .build-job]


deploy-dev:
  extends: [.dev-environment, .deploy-job]
  variables:
    DEPLOY_DOCKER_IMAGE: $DOCKER_IMAGE:latest


build-prod:
  extends: [.prod-environment, .build-job]
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_BRANCH == "main"


deploy-prod:
  extends: [.prod-environment, .deploy-job]
  variables:
    DEPLOY_DOCKER_IMAGE: $DOCKER_IMAGE:$CI_COMMIT_SHA
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual


send-release-notes:
  variables:
      GIT_DEPTH: 0
  stage: after
  environment: datatools-prod
  image: python:3.12-slim
  tags:
    - calc
  needs:
    - deploy-prod
  script:
    - apt-get update && apt-get install -y git
    - cd myproject
    - python -m pip install -r requirements.txt
    - python -m aeropipe --wrike-api-key "$WRIKE_API_KEY" send-release-notes --webhook-url "$GOOGLE_CHAT_WEBHOOK_URL"
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual