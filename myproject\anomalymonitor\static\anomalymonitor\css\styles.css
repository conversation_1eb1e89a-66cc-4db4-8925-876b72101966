body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 95%;
    margin: 0 auto;
    padding: 15px;
    padding-top: 5px;
}

.controls {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    width: 100%;
    box-sizing: border-box;
}

.image-controls {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.control-group {
    background: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    width: 100%;
    box-sizing: border-box;
    text-align: center;
}

.control-group label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.canvas-preview {
    width: 100%;
    height: 300px;
    border: 1px solid #ccc;
    margin-top: 10px;
}

.canvas-preview canvas {
    width: 100%;
    height: auto;
    display: block;
}

.comparison-results {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    margin-top: 20px;
    width: 100%;
}

.result-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    width: 100%;
    box-sizing: border-box;
    text-align: center;
}

.comparison-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.comparison-header {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.comparison-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2em;
}

.stats-panel {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.visualizations {
    display: grid;
    gap: 20px;
}

.visualization-card {
    background: white;
    border-radius: 5px;
    overflow: hidden;
}

.visualization-card img {
    width: 100%;
    max-height: 800px;
    object-fit: contain;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.visualization-label {
    padding: 8px;
    background: #e9ecef;
    color: #495057;
    font-size: 0.9em;
    text-align: center;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.buttons {
    margin-top: 15px;
}

button {
    display: block;
    width: 200px;
    padding: 10px;
    margin: 20px auto;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #0056b3;
}

button:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.info-icon {
    margin-left: 5px;
    cursor: help;
}

.help-text {
    font-size: 0.85em;
    color: #666;
    margin-top: 8px;
    line-height: 1.4;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
}

.help-text p {
    margin: 0 0 8px 0;
    font-weight: 500;
    color: #2c3e50;
}

.help-text ul {
    margin: 0;
    padding-left: 20px;
}

.help-text li {
    margin-bottom: 6px;
    color: #4a5568;
}

.help-text li:last-child {
    margin-bottom: 0;
}

.threshold-controls {
    width: 100%;
    margin: 20px 0 0;
    padding: 15px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

input[type="number"] {
    width: 120px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

input[type="file"] {
    width: 100%;
    margin-bottom: 8px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #2c3e50;
    margin: 0;
    padding: 0;
}

h2 {
    font-size: 1.5em;
    margin: 15px 0;
    text-align: center;
}

h3 {
    font-size: 1.2em;
    margin-bottom: 12px;
    text-align: center;
}

#results {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    width: 100%;
    box-sizing: border-box;
}
