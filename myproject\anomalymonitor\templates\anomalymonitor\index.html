<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Comparison Tool</title>
    {% load static tailwind_tags %}
    {% tailwind_css %}
    <link rel="stylesheet" href="{% static 'anomalymonitor/css/styles.css' %}">
</head>
<body>
    
    <div class="container">
        <form id="comparisonForm" action="{% url 'anomalymonitor:compare' %}" method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="controls">
                <div class="image-controls">
                    <div class="control-group">
                        <label for="originalImage">Original Image</label>
                        <input type="file" id="originalImage" name="original_image" accept="image/*" required>
                        <div class="canvas-preview">
                            <canvas id="originalCanvas" width="400" height="300"></canvas>
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <label for="yesterdayImage">Yesterday's Image</label>
                        <input type="file" id="yesterdayImage" name="yesterday_image" accept="image/*" required>
                        <div class="canvas-preview">
                            <canvas id="yesterdayCanvas" width="400" height="300"></canvas>
                        </div>
                    </div>

                    <div class="control-group">
                        <label for="todayImage">Today's Image</label>
                        <input type="file" id="todayImage" name="today_image" accept="image/*" required>
                        <div class="canvas-preview">
                            <canvas id="todayCanvas" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <div class="buttons">
                    <button type="submit" id="compareBtn">Compare Images</button>
                </div>
            </div>
        </form>
        
        <div id="results" style="display: none;">
            <h2>Comparison Results</h2>
            
            <div class="results-grid">
                <!-- Original vs Today -->
                <div class="comparison-group">
                    <div class="comparison-header">
                        <h3>Original vs Today</h3>
                    </div>
                    
                    <div class="stats-panel">
                        <p>Similarity Score: <span id="originalTodayScore">-</span></p>
                        <p>Difference Percentage: <span id="originalTodayDiffPercentage">-</span></p>
                        <p>Number of Different Regions: <span id="originalTodayNumRegions">-</span></p>
                    </div>

                    <div class="visualizations">
                        <div class="visualization-card">
                            <div class="visualization-label">Intensity Heatmap</div>
                            <img id="originalTodayDiff" alt="Intensity Heatmap" />
                        </div>
                        <div class="visualization-card">
                            <div class="visualization-label">Highlighted Differences</div>
                            <img id="originalTodayMask" alt="Highlighted Differences" />
                        </div>
                    </div>
                </div>

                <!-- Yesterday vs Today -->
                <div class="comparison-group">
                    <div class="comparison-header">
                        <h3>Yesterday vs Today</h3>
                    </div>
                    
                    <div class="stats-panel">
                        <p>Similarity Score: <span id="yesterdayTodayScore">-</span></p>
                        <p>Difference Percentage: <span id="yesterdayTodayDiffPercentage">-</span></p>
                        <p>Number of Different Regions: <span id="yesterdayTodayNumRegions">-</span></p>
                    </div>

                    <div class="visualizations">
                        <div class="visualization-card">
                            <div class="visualization-label">Intensity Heatmap</div>
                            <img id="yesterdayTodayDiff" alt="Intensity Heatmap" />
                        </div>
                        <div class="visualization-card">
                            <div class="visualization-label">Highlighted Differences</div>
                            <img id="yesterdayTodayMask" alt="Highlighted Differences" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="{% static 'anomalymonitor/js/main.js' %}"></script>
</body>
</html>
