// Select2 Customization for Workorders
// Preload CSS files to ensure they're loaded before DOM content is displayed
(function preloadCriticalCSS() {
    // Create preload links for critical CSS files
    const cssFiles = [
        "https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css",
        "https://cdn.jsdelivr.net/gh/erimicel/select2-tailwindcss-theme/dist/select2-tailwindcss-theme-plain.min.css"
    ];

    // Add a dynamic path for the local CSS file
    const localCssPath = document.querySelector('link[href*="select2-theme.css"]');
    if (localCssPath && localCssPath.href) {
        cssFiles.push(localCssPath.href);
    }

    // Create and append preload links
    cssFiles.forEach(cssFile => {
        // Create preload link
        const preloadLink = document.createElement('link');
        preloadLink.rel = 'preload';
        preloadLink.href = cssFile;
        preloadLink.as = 'style';
        document.head.appendChild(preloadLink);

        // Also create a stylesheet link to force immediate loading
        const styleLink = document.createElement('link');
        styleLink.rel = 'stylesheet';
        styleLink.href = cssFile;
        styleLink.setAttribute('importance', 'high');
        document.head.appendChild(styleLink);
    });

    // Add critical inline styles to prevent FOUC (Flash of Unstyled Content)
    const criticalStyles = `
        .select2-container--tailwindcss-3 {
            cursor: pointer !important;
            height: 38px !important;
            overflow: visible !important;
        }
        .select2-container--tailwindcss-3 .select2-selection--multiple {
            height: 38px !important;
            min-height: 38px !important;
            max-height: 38px !important;
            overflow: visible !important;
            position: relative !important;
            cursor: pointer !important;
            padding: 0 !important;
            border-color: #d1d5db !important;
            display: flex !important;
            align-items: center !important;
        }
        .select2-container--tailwindcss-3 .select2-selection--multiple .select2-selection__rendered {
            display: flex !important;
            flex-wrap: wrap !important;
            overflow: visible !important;
            align-items: center !important;
            height: 100% !important;
            padding-left: 0 !important;
            z-index: auto !important;
        }
        .select2-container--tailwindcss-3:not(.select2-container--open) .select2-selection--multiple .select2-selection__choice {
            display: none !important;
        }
        .select2-container--tailwindcss-3 .select2-selection--multiple .selection-display-text {
            color: #4B5563;
            font-size: 0.875rem;
            line-height: 36px;
            padding-left: 8px;
            white-space: nowrap;
            overflow: visible;
            width: calc(100% - 20px);
            pointer-events: none;
        }
        .select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .selection-display-text {
            display: none !important;
        }
    `;

    // Create and append style element with high priority
    const style = document.createElement('style');
    style.textContent = criticalStyles;
    style.setAttribute('importance', 'high');

    // Insert at the beginning of head for highest priority
    if (document.head.firstChild) {
        document.head.insertBefore(style, document.head.firstChild);
    } else {
        document.head.appendChild(style);
    }
})();

const dropdownsData = [
    { id: "project_name", placeholder: "Project" },
    { id: "service_code", placeholder: "Service" },
    { id: "set_name", placeholder: "Set" },
    { id: "customer", placeholder: "Customer" }
];

// Clean up any existing Select2 instances to prevent duplicates
function cleanupExistingSelect2Instances() {
    // Remove any orphaned Select2 dropdowns from the DOM
    $('.select2-container').remove();

    // Destroy all Select2 instances
    dropdownsData.forEach(({ id }) => {
        const $select = $(`#${id}`);
        try {
            if ($select.hasClass('select2-hidden-accessible')) {
                $select.select2('destroy');
            }
        } catch (e) {
            console.log(`Error destroying Select2 instance for #${id}:`, e);
        }
    });
}

function initializeSelect2Dropdowns() {
    // First clean up any existing instances
    cleanupExistingSelect2Instances();

    dropdownsData.forEach(({ id, placeholder }) => {
        const $select = $(`#${id}`);

        // Ensure there's an empty option for the placeholder
        if ($select.find('option[value=""]').length === 0) {
            $select.prepend('<option value=""></option>');
        }

        // Initialize new Select2 with Tailwind CSS theme
        $select.select2({
            placeholder: placeholder || '', // Use the provided placeholder if available
            allowClear: false, // Disable clear all button
            width: '100%',
            closeOnSelect: false,
            multiple: true,
            minimumResultsForSearch: 0, // Enable search
            dropdownParent: $('body'), // Ensures dropdown renders correctly, not clipped by containers
            theme: 'tailwindcss-3', // Added TailwindCSS theme
            language: {
                noResults: function () {
                    return "No results found";
                }
            },
            // Override the selection template to hide individual selections
            templateSelection: function(data, container) {
                // Return empty string to not display any selection tags
                return '';
            }
        });

        // Apply our custom display after initialization
        updateSelectionDisplay($select);

        // Update display after selection changes
        $select.on('select2:select select2:unselect change', function () {
            updateSelectionDisplay($(this));
        });

        // Focus the search field when dropdown is opened and ensure proper positioning
        $select.on('select2:open', function () {
            setTimeout(function() {
                // Focus the search field
                const $searchField = $('.select2-search__field:visible');
                $searchField.focus();

                // Ensure the search field is properly positioned
                const $container = $select.next('.select2-container');
                const $searchContainer = $container.find('.select2-search--inline');

                // Reset any inline styles that might interfere with positioning
                $searchContainer.css({
                    'position': 'absolute',
                    'left': '0',
                    'top': '0',
                    'width': '100%'
                });

                // Ensure the search field takes the full width (minus dropdown arrow)
                $searchField.css({
                    'width': 'calc(100% - 30px)',
                    'padding-left': '8px'
                });
            }, 10);
        });
        
        // Re-apply our custom display when dropdown closes
        $select.on('select2:close', function() {
            setTimeout(function() {
                updateSelectionDisplay($select);
            }, 10);
        });
    });
}

// Function to display count or placeholder
function updateSelectionDisplay($select) {
    const $container = $select.next('.select2-container');
    const $rendered = $container.find('.select2-selection__rendered');
    const allSelectedValues = $select.val() || [];
    
    // Clear existing custom display if any
    $rendered.find('.selection-display-text').remove();
    
    // Hide all selection items that might be added by Select2
    $rendered.find('.select2-selection__choice').hide();
    
    // Create display text based on selection count
    const displayText = allSelectedValues.length > 0 ? `${allSelectedValues.length} selected` : "All selected";

    // Create a span for the display
    const $displayTextElement = $('<span>')
        .addClass('selection-display-text')
        .text(displayText)
        .css({
            'display': 'inline-block',
            'width': 'calc(100% - 20px)', // Increased width to prevent truncation
            'text-align': 'left',
            'padding-left': '8px',
            'line-height': '36px',
            'overflow': 'visible', // Changed from hidden to visible
            'white-space': 'nowrap',
            'color': '#4B5563',
            'font-size': '0.875rem'
        });

    // Add tooltip if items are selected
    if (allSelectedValues.length > 0) {
        const selectedTexts = allSelectedValues.map(value => {
            const escapedValue = String(value).replace(/'/g, "\\'");
            const $option = $select.find(`option[value='${escapedValue}']`);
            return $option.text().trim();
        });
        
        // Set tooltip on both the display element and its container for better hover coverage
        const tooltipText = selectedTexts.join(', ');
        $displayTextElement.attr('title', tooltipText);
        $container.attr('title', tooltipText);
        $rendered.attr('title', tooltipText);
    } else {
        // Clear any existing tooltips
        $displayTextElement.removeAttr('title');
        $container.removeAttr('title');
        $rendered.removeAttr('title');
    }

    // Append the display text
    $rendered.prepend($displayTextElement);
    
    // Ensure the container has pointer cursor to indicate it's hoverable
    $container.css('cursor', 'pointer');
}

// Initialize as early as possible
function initializeSelect2OnLoad() {
    // Set a flag to prevent multiple initializations
    if (window.select2Initialized) {
        return;
    }

    // Initialize Select2
    initializeSelect2Dropdowns();

    // Apply truncation to ALL initialized select2 elements on page load
    dropdownsData.forEach(({ id }) => {
        const $select = $(`#${id}`);
        if ($select.hasClass('select2-hidden-accessible')) {
            updateSelectionDisplay($select);
        }
    });

    // Set the initialization flag
    window.select2Initialized = true;
}

// Try to initialize as early as possible
(function() {
    // Strategy 1: Try to initialize immediately if jQuery and Select2 are already loaded
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        initializeSelect2OnLoad();
    }

    // Strategy 2: Use DOMContentLoaded event for early initialization
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
            initializeSelect2OnLoad();
        }
    });

    // Strategy 3: Fallback to document ready
    $(document).ready(function() {
        initializeSelect2OnLoad();
    });

    // Strategy 4: Final fallback to window load
    window.addEventListener('load', function() {
        if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
            initializeSelect2OnLoad();
        }
    });
})();