{% load static %}

<div class="flex flex-col p-2 h-full bg-white rounded-lg shadow-lg">
  <div class="overflow-x-auto overflow-y-auto flex-grow">
    <table id="projects-table" class="w-full text-sm border-t border-b border-gray-200 border-collapse">
      <thead>
        <tr class="bg-gray-100">
          <th class="text-left font-semibold p-2 border-b border-gray-200 !w-[13%] !min-w-[13%] !max-w-[13%]">Project
            Name</th>
          <th class="text-left font-semibold p-2 border-b border-gray-200 !w-[10%] !min-w-[10%] !max-w-[10%]">Status
          </th>
          <th class="text-left font-semibold p-2 border-b border-gray-200 !w-[10%] !min-w-[10%] !max-w-[10%]">Set
          </th>
          <th class="text-left font-semibold p-2 border-b border-gray-200 !w-[10%] !min-w-[10%] !max-w-[10%]">Service
          </th>
          <th class="text-left font-semibold p-2 border-b border-gray-200 !w-[5%] !min-w-[5%] !max-w-[5%]">Unit</th>
          <th class="text-right font-semibold p-2 border-b border-gray-200 !w-[5%] !min-w-[5%] !max-w-[5%]">Unit Count
          </th>
          <th class="text-center font-semibold p-2 border-b border-gray-200 !w-[12%] !min-w-[12%] !max-w-[12%]">First
            Promised Date</th>
          <th class="text-center font-semibold p-2 border-b border-gray-200 !w-[12%] !min-w-[12%] !max-w-[12%]">Planned
            Dates</th>
          <th class="text-center font-semibold p-2 border-b border-gray-200 !w-[12%] !min-w-[12%] !max-w-[12%]">Actual
            Dates</th>
          <th class="text-center font-semibold p-2 border-b border-gray-200 !w-[7%] !min-w-[7%] !max-w-[7%]">Start Date
            Variance</th>
          <th class="text-center font-semibold p-2 border-b border-gray-200 !w-[7%] !min-w-[7%] !max-w-[7%]">Duration
            Variance</th>
          <th class="text-center font-semibold p-2 border-b border-gray-200 !w-[7%] !min-w-[7%] !max-w-[7%]">Date
            Changes Count</th>
          <th class="hidden">Project Manager</th>
          <th class="hidden">Region</th>
          <th class="hidden">Client</th>
          <th class="hidden">Project ID</th>
        </tr>
      </thead>
    </table>
  </div>
</div>

<script>
  const projectDataUrl = '{% url "projects:project_data" %}';
</script>

<!-- CSS Files -->
 <link href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css" rel="stylesheet" />
 <link href="{% static 'projects/css/datatable-custom.css' %}" rel="stylesheet" />

<!-- JavaScript Files -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>

<!-- Modular JavaScript files -->
<script type="module" src="{% static 'projects/js/utils.js' %}"></script>
<script type="module" src="{% static 'projects/js/statistics.js' %}"></script>
<script type="module" src="{% static 'projects/js/datatable-config.js' %}"></script>
<script type="module" src="{% static 'projects/js/project-table.js' %}"></script>