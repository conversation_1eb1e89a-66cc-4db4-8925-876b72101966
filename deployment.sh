#!/bin/sh
# set -x

BMAGENTA='\033[0;95m'
BCYAN='\033[0;96m'
NC='\033[0m'

echo "${BMAGENTA}*****************************************"
echo "Setup ssh"
echo "*****************************************${NC}"

# https://docs.gitlab.com/ee/ci/jobs/ssh_keys.html
command -v ssh-agent >/dev/null ||
(apt-get update -y >/dev/null && apt-get install openssh-client -y >/dev/null)

retVal=$?
if [ $retVal -ne 0 ]; then
    exit $retVal
fi

eval $(ssh-agent -s) && \
chmod 400 "$DEPLOY_SSH_PRIV_KEY_FILE" && \
ssh-add "$DEPLOY_SSH_PRIV_KEY_FILE" && \
ssh-add -l -E sha256 && \
mkdir -p ~/.ssh && \
chmod 700 ~/.ssh && \
ssh-keyscan $DEPLOY_HOST >> ~/.ssh/known_hosts && \
chmod 644 ~/.ssh/known_hosts

retVal=$?
if [ $retVal -ne 0 ]; then
    exit $retVal
fi

echo "${BMAGENTA}*****************************************"
echo "Deployment"
echo "*****************************************${NC}"

cat > ./cleanup.sh<< EOF
#!/bin/sh
set -x

echo "***** Dump"
ls -R $DEPLOY_TMP_DIR 2>> /dev/null
ls -R $DEPLOY_WORKING_DIR 2>> /dev/null

sudo rm -rf $DEPLOY_TMP_DIR &&
mkdir -p $DEPLOY_TMP_DIR/nginx
mkdir -p $DEPLOY_TMP_DIR/keys
EOF
ssh -i keypair $DEPLOY_SSH_USER@$DEPLOY_HOST "bash -s" < ./cleanup.sh


echo "${BMAGENTA}*****************************************"
echo "Copy"
echo "*****************************************${NC}"

cat $DEPLOY_ENV_FILE >> .env-prod
echo >> .env-prod
echo "export CI_COMMIT_SHA=$CI_COMMIT_SHA" >> .env-prod
echo "TELEMETRY_DB_HOST=$TELEMETRY_DB_HOST" >> .env-prod
echo "TELEMETRY_DB_PORT=$TELEMETRY_DB_PORT" >> .env-prod
echo "TELEMETRY_DB_NAME=$TELEMETRY_DB_NAME" >> .env-prod
echo "TELEMETRY_DB_USER=$TELEMETRY_DB_USER" >> .env-prod
echo "TELEMETRY_DB_PASSWORD=$TELEMETRY_DB_PASSWORD" >> .env-prod
echo "DATATOOLS_DB_HOST=$DATATOOLS_DB_HOST" >> .env-prod
echo "DATATOOLS_PORT=$DATATOOLS_PORT" >> .env-prod
echo "DATATOOLS_DB_NAME=$DATATOOLS_DB_NAME" >> .env-prod
echo "DATATOOLS_USER=$DATATOOLS_USER" >> .env-prod
echo "DATATOOLS_PASSWORD=$DATATOOLS_PASSWORD" >> .env-prod
echo "NAVIREC_ACCOUNT_ID=$NAVIREC_ACCOUNT_ID" >> .env-prod
echo "NAVIREC_API_KEY=$NAVIREC_API_KEY" >> .env-prod
echo "TOMORROW_IO_API_KEY=$TOMORROW_IO_API_KEY" >> .env-prod
echo "METEOMATICS_USERNAME=$METEOMATICS_USERNAME" >> .env-prod
echo "METEOMATICS_PASSWORD=$METEOMATICS_PASSWORD" >> .env-prod

scp compose-prod.yaml $DEPLOY_SSH_USER@$DEPLOY_HOST:$DEPLOY_TMP_DIR/compose.yaml
scp .env-prod $DEPLOY_SSH_USER@$DEPLOY_HOST:$DEPLOY_TMP_DIR/
scp nginx/datatools.conf.template $DEPLOY_SSH_USER@$DEPLOY_HOST:$DEPLOY_TMP_DIR/nginx/datatools.conf.template
scp $WORKORDER_SERVICE_ACCOUNT $DEPLOY_SSH_USER@$DEPLOY_HOST:$DEPLOY_TMP_DIR/keys/workorder-service-acc.json

echo "${BMAGENTA}*****************************************"
echo "Deploy containers"
echo "*****************************************${NC}"

cat > ./deploy.sh<< EOF
#!/bin/sh
set -x

echo "***** Dump"
ls -R $DEPLOY_TMP_DIR 2>> /dev/null
ls -R $DEPLOY_WORKING_DIR 2>> /dev/null

echo " "
echo "***** Compose down"

sudo rm -f /etc/nginx/sites-enabled/datatools.conf
sudo service nginx reload
systemctl status nginx.service -n 20 -l --no-pager
sudo docker compose -f $DEPLOY_WORKING_DIR/compose.yaml down 2>> /dev/null

echo " "
echo "***** Copying $DEPLOY_TMP_DIR -> $DEPLOY_WORKING_DIR"
sudo rm -fr $DEPLOY_WORKING_DIR &&
sudo mkdir $DEPLOY_WORKING_DIR &&
sudo cp -rT $DEPLOY_TMP_DIR $DEPLOY_WORKING_DIR &&
cd $DEPLOY_WORKING_DIR

echo "
export DEPLOY_DOCKER_IMAGE=$DEPLOY_DOCKER_IMAGE
export DEPLOY_DOMAIN=$DEPLOY_DOMAIN
" | sudo tee .env

echo " "
echo "***** Dump"
ls -R $DEPLOY_TMP_DIR 2>> /dev/null
ls -R $DEPLOY_WORKING_DIR 2>> /dev/null

echo " "
echo "***** Compose"
sudo docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY &&
sudo docker compose down &&
sudo docker compose pull &&
sudo docker compose up -d &&
sudo docker system prune --all --force &&
sudo docker logout $CI_REGISTRY;

sleep 5

sudo docker cp datatools-app:/app/static $DEPLOY_WORKING_DIR/static

export DEPLOY_DOMAIN=$DEPLOY_DOMAIN
export DEPLOY_WORKING_DIR=$DEPLOY_WORKING_DIR
envsubst '\${DEPLOY_DOMAIN},\${DEPLOY_WORKING_DIR}' < $DEPLOY_WORKING_DIR/nginx/datatools.conf.template | sudo tee $DEPLOY_WORKING_DIR/nginx/datatools.conf

sudo cp $DEPLOY_WORKING_DIR/nginx/datatools.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/datatools.conf /etc/nginx/sites-enabled/

sudo service nginx reload
systemctl status nginx.service -n 100 -l --no-pager

EOF
ssh -i keypair $DEPLOY_SSH_USER@$DEPLOY_HOST "bash -s" < ./deploy.sh
