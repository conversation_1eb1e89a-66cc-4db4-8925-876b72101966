<!-- Region Filter -->
<div class="flex items-center space-x-2 w-full sm:w-auto">
    <div x-data="{ 
        open: false, 
        selectedRegion: '{{ selected_region }}',
        select(region) {
            this.selectedRegion = region;
            this.open = false;
            window.location.href = `?region=${region}`;
        }
    }" class="relative flex-1 sm:flex-none">
        <button @click="open = !open" type="button"
            class="px-3 py-2 w-full text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 transition duration-150 ease-in-out sm:w-auto hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            :class="{'ring-2 ring-blue-500 border-blue-500': open}">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <svg class="mr-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z">
                        </path>
                    </svg>
                    <span x-text="selectedRegion === 'All' ? 'Filter by Region' : 'Region: ' + selectedRegion"></span>
                </div>
            </div>
        </button>
        <div x-show="open" @click.outside="open = false"
            class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md divide-y divide-gray-100 ring-1 ring-black ring-opacity-5 shadow-lg"
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95" style="display: none;">
            <div class="py-1">
                <a href="?region=All" @click="select('All')"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    :class="{'bg-blue-50 text-blue-700': selectedRegion === 'All'}">
                    All Regions
                </a>
            </div>
            <div class="py-1">
                {% for region in regions %}
                <a href="?region={{ region }}" @click="select('{{ region }}')"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    :class="{'bg-blue-50 text-blue-700': selectedRegion === '{{ region }}'}">
                    {{ region }}
                </a>
                {% endfor %}
            </div>
        </div>
    </div>
</div>