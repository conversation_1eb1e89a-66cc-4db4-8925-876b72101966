"""
Weather service for fetching 3-day forecasts for robotic sets.
Integrates with Tomorrow.io API and BigQuery for set coordinates.
"""

import os
import requests
import pytz
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
from django.conf import settings
from django.core.cache import cache
from google.cloud import bigquery
from myproject.utils import get_bigquery_client

from timezonefinder import TimezoneFinder


class WeatherServiceError(Exception):
    """Custom exception for weather service errors"""
    pass


class WeatherService:
    """Service class for fetching weather forecasts for robotic sets"""
    
    def __init__(self):
        self.api_key = settings.TOMORROW_IO_API_KEY
        self.base_url = settings.TOMORROW_IO_BASE_URL
        self.timeout = 10  # seconds
        self.max_retries = 3
        self.cache_ttl = 1800  # 30 minutes
        
        if not self.api_key:
            raise WeatherServiceError("TOMORROW_IO_API_KEY not configured")
    
    def get_set_coordinates(self, set_name: str) -> Tuple[float, float]:
        """
        Get the latest coordinates for a set from BigQuery project_field_activity table.

        Args:
            set_name: Name of the robotic set

        Returns:
            Tuple of (latitude, longitude)

        Raises:
            WeatherServiceError: If coordinates cannot be found
        """
        try:
            client = get_bigquery_client()

            # Query to get the latest activity coordinates for the set
            query = """
            SELECT turbine_latitude, turbine_longitude
            FROM `budibase-dev-369406.presentation.project_field_activity`
            WHERE set_name = @set_name
              AND turbine_latitude IS NOT NULL
              AND turbine_longitude IS NOT NULL
            ORDER BY end_datetime DESC, start_datetime DESC
            LIMIT 1
            """

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("set_name", "STRING", set_name)
                ]
            )

            query_job = client.query(query, job_config=job_config)

            # Handle both regular BigQuery results and cached results
            try:
                # Try to get results - this works with both cached and non-cached queries
                results = query_job.result()
                results_list = list(results)
            except Exception as e:
                raise WeatherServiceError(f"Failed to execute query for set {set_name}: {str(e)}")

            if not results_list:
                raise WeatherServiceError(f"No coordinates found for set: {set_name}")

            row = results_list[0]
            latitude = float(row.turbine_latitude)
            longitude = float(row.turbine_longitude)

            return latitude, longitude

        except WeatherServiceError:
            # Re-raise WeatherServiceError without wrapping
            raise
        except Exception as e:
            raise WeatherServiceError(f"Failed to get coordinates for set {set_name}: {str(e)}")
    
    def _get_cache_key(self, set_name: str) -> str:
        """Generate cache key for set weather data"""
        # Replace spaces and other problematic characters for memcached compatibility
        # Memcached doesn't allow spaces, control characters, or certain special characters
        safe_set_name = set_name.replace(' ', '_').replace(':', '-').replace('\n', '').replace('\r', '').replace('\t', '')
        return f"weather_forecast_set:{safe_set_name}"
    
    def _make_request(self, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """
        Make HTTP request to Tomorrow.io API with retry logic.

        Args:
            endpoint: API endpoint
            data: JSON data for POST requests (optional)
            params: Query parameters for GET requests (optional)

        Returns:
            JSON response data
        """
        url = f"{self.base_url}/{endpoint}"
        headers = {
            'apikey': self.api_key,
            'Content-Type': 'application/json'
        }

        for attempt in range(self.max_retries):
            try:
                if data is not None:
                    # POST request with JSON data
                    response = requests.post(url, json=data, headers=headers, timeout=self.timeout)
                else:
                    # GET request with query parameters
                    response = requests.get(url, params=params, headers=headers, timeout=self.timeout)

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:
                    # Rate limited - wait and retry
                    retry_after = int(response.headers.get('Retry-After', 60))
                    if attempt < self.max_retries - 1:
                        time.sleep(retry_after)
                        continue
                else:
                    response.raise_for_status()

            except requests.exceptions.RequestException as e:
                if attempt == self.max_retries - 1:
                    raise WeatherServiceError(f"API request failed after {self.max_retries} attempts: {str(e)}")

        raise WeatherServiceError("Maximum retry attempts exceeded")
    
    def get_3day_forecast(self, set_name: str) -> Dict:
        """
        Get 3-day weather forecast for a robotic set with 1-hour intervals.

        Args:
            set_name: Name of the robotic set

        Returns:
            Dict with normalized weather data including dual timezone
        """
        # Check cache first
        cache_key = self._get_cache_key(set_name)
        cached_data = cache.get(cache_key)
        if cached_data:
            return cached_data

        # Get coordinates for the set
        lat, lon = self.get_set_coordinates(set_name)

        # Calculate start and end times for 3-day forecast
        now = datetime.now(pytz.UTC)
        end_time = now + timedelta(days=3)

        # Prepare API request data for Timelines API (POST request)
        request_data = {
            'location': f"{lat},{lon}",
            'fields': ['temperature', 'precipitationIntensity', 'windSpeed', 'windGust'],
            'timesteps': ['1h'],  # 1-hour intervals
            'units': 'metric',
            'timezone': 'UTC',
            'startTime': now.isoformat(),
            'endTime': end_time.isoformat()
        }

        # Make API request using POST
        data = self._make_request('timelines', data=request_data)

        # Normalize and process the response
        normalized_data = self._normalize_forecast_data(data, lat, lon, set_name)

        # Cache the result (standard TTL since data frequency is reduced)
        cache.set(cache_key, normalized_data, self.cache_ttl)  # 30 minutes

        return normalized_data
    
    def _normalize_forecast_data(self, data: Dict, lat: float, lon: float, set_name: str) -> Dict:
        """
        Normalize API response to consistent format with dual timezone.
        Now handles 1-hour interval data.

        Returns:
            {
                'set_name': str,
                'location': {'lat': float, 'lon': float},
                'hourly': [  # Note: keeping 'hourly' name for frontend compatibility
                    {
                        'time': str (ISO8601),
                        'temperature': float,
                        'precipitationIntensity': float,
                        'windSpeed': float,
                        'localTime': str,
                        'rigaTime': str
                    },
                    ...
                ]
            }
        """
        try:
            # Extract interval data from the response
            timelines = data.get('data', {}).get('timelines', [])

            if not timelines:
                raise WeatherServiceError("No timeline data in API response")

            # Find the 1h timeline
            interval_timeline = None
            for timeline in timelines:
                if timeline.get('timestep') == '1h':
                    interval_timeline = timeline
                    break

            if not interval_timeline:
                raise WeatherServiceError("No 1-hour timeline data in API response")

            intervals = interval_timeline.get('intervals', [])

            if not intervals:
                raise WeatherServiceError("No intervals data in API response")

            # Get timezone for the set location
            location_tz = self._get_timezone_for_location(lat, lon)
            riga_tz = pytz.timezone('Europe/Riga')

            interval_data = []
            now = datetime.now(pytz.UTC)
            end_time = now + timedelta(days=3)  # 3-day forecast

            for interval in intervals:
                interval_time = datetime.fromisoformat(interval['startTime'].replace('Z', '+00:00'))

                # Only include next 3 days
                if interval_time > end_time:
                    break

                values = interval.get('values', {})

                # Convert to local and Riga time
                local_time = interval_time.astimezone(location_tz)
                riga_time = interval_time.astimezone(riga_tz)

                interval_data.append({
                    'time': interval_time.isoformat(),
                    'temperature': values.get('temperature'),
                    'precipitationIntensity': values.get('precipitationIntensity'),
                    'windSpeed': values.get('windSpeed'),
                    'windGust': values.get('windGust'),
                    'localTime': local_time.strftime('%Y-%m-%d %H:%M'),
                    'rigaTime': riga_time.strftime('%Y-%m-%d %H:%M')
                })

            return {
                'set_name': set_name,
                'location': {
                    'lat': lat,
                    'lon': lon,
                    'timezone': str(location_tz)
                },
                'hourly': interval_data  # Keeping 'hourly' name for frontend compatibility
            }

        except Exception as e:
            raise WeatherServiceError(f"Failed to process weather data: {str(e)}")
    
    def _get_timezone_for_location(self, lat: float, lon: float) -> pytz.timezone:
        """
        Get timezone for given coordinates using timezonefinder.

        Args:
            lat: Latitude coordinate
            lon: Longitude coordinate

        Returns:
            pytz.timezone object for the location
        """
        try:
            tf = TimezoneFinder()
            timezone_name = tf.timezone_at(lat=lat, lng=lon)

            if timezone_name:
                return pytz.timezone(timezone_name)
            else:
                return pytz.UTC

        except Exception:
            return pytz.UTC
