// Video Player Functionality for Telemetry Chart
document.addEventListener('DOMContentLoaded', function () {
    // Wait for Plotly chart to be rendered
    setTimeout(function () {
        attachClickHandlers();
    }, 1000);
});

// Global state
let isPlaying = false;
let videos = [];

function attachClickHandlers() {
    var plotElements = document.querySelectorAll('.plotly-graph-div');

    plotElements.forEach(function (plotElement) {
        if (plotElement._clickHandlerAttached) {
            return;
        }

        // Store the handler function for potential cleanup
        plotElement._videoClickHandler = function (data) {
            if (data.points && data.points.length > 0) {
                var point = data.points[0];

                if (point.y === 'Video') {
                    if (point.data && point.data.meta) {
                        showVideoPlayer(point.data.meta);
                    } else if (point.data && point.data.customdata && point.data.customdata[point.pointIndex]) {
                        var hoverText = point.data.customdata[point.pointIndex];
                        var videoData = parseHoverText(hoverText);
                        showVideoPlayer(videoData);
                    } else {
                        showVideoPlayer({
                            blade: 'Unknown',
                            activity_type: 'Unknown',
                            start: 'Unknown',
                            end: 'Unknown',
                            videos: {}
                        });
                    }
                }
            }
        };

        plotElement.on('plotly_click', plotElement._videoClickHandler);
        plotElement._clickHandlerAttached = true;
    });
}

function parseHoverText(hoverText) {
    var lines = hoverText.split('<br>');
    var videoData = {};

    lines.forEach(function (line) {
        if (line.includes('Blade:')) {
            videoData.blade = line.split('</b>')[1].trim();
        } else if (line.includes('Activity:')) {
            videoData.activity_type = line.split('</b>')[1].trim();
        } else if (line.includes('Start:')) {
            videoData.start = line.split('</b>')[1].trim();
        } else if (line.includes('End:')) {
            videoData.end = line.split('</b>')[1].trim();
        }
    });

    return videoData;
}

function setVideoSources(videoData) {
    const mediaUrl = '/media/videos/';
    const videos = videoData.videos || {};

    console.log('Setting video sources:', videos);

    // Clear previous videos first to free memory
    clearVideoSources();

    const hasMainVideo = videos.main && videos.main !== null && videos.main !== '';
    const hasSecondary1 = videos.secondary1 && videos.secondary1 !== null && videos.secondary1 !== '';
    const hasSecondary2 = videos.secondary2 && videos.secondary2 !== null && videos.secondary2 !== '';
    const hasVideos = hasMainVideo || hasSecondary1 || hasSecondary2;

    if (!hasVideos) {
        console.log('No videos available');
        return false;
    }

    // Show loading overlays while videos are loading
    showLoadingOverlays();

    const mainVideo = document.getElementById('main-video');
    const secondaryVideo1 = document.getElementById('secondary-video-1');
    const secondaryVideo2 = document.getElementById('secondary-video-2');

    // Set sources and load videos
    if (mainVideo && videos.main) {
        const mainVideoSource = document.getElementById('main-video-source');
        if (mainVideoSource) {
            mainVideoSource.src = mediaUrl + videos.main;
            mainVideo.load(); // Load new source
            console.log('Set main video source:', mediaUrl + videos.main);
        }
    }
    if (secondaryVideo1 && videos.secondary1) {
        const secondaryVideo1Source = document.getElementById('secondary-video-1-source');
        if (secondaryVideo1Source) {
            secondaryVideo1Source.src = mediaUrl + videos.secondary1;
            secondaryVideo1.load(); // Load new source
            console.log('Set secondary video 1 source:', mediaUrl + videos.secondary1);
        }
    }
    if (secondaryVideo2 && videos.secondary2) {
        const secondaryVideo2Source = document.getElementById('secondary-video-2-source');
        if (secondaryVideo2Source) {
            secondaryVideo2Source.src = mediaUrl + videos.secondary2;
            secondaryVideo2.load(); // Load new source
            console.log('Set secondary video 2 source:', mediaUrl + videos.secondary2);
        }
    }

    return true;
}

function clearVideoSources() {
    const mainVideo = document.getElementById('main-video');
    const secondaryVideo1 = document.getElementById('secondary-video-1');
    const secondaryVideo2 = document.getElementById('secondary-video-2');
    
    // Properly unload videos to free memory
    [mainVideo, secondaryVideo1, secondaryVideo2].forEach(video => {
        if (video) {
            video.pause();
            video.removeAttribute('src');
            video.load(); // Force unload to free memory
        }
    });

    // Clear source elements as backup
    const mainVideoSource = document.getElementById('main-video-source');
    const secondaryVideo1Source = document.getElementById('secondary-video-1-source');
    const secondaryVideo2Source = document.getElementById('secondary-video-2-source');

    if (mainVideoSource) mainVideoSource.src = '';
    if (secondaryVideo1Source) secondaryVideo1Source.src = '';
    if (secondaryVideo2Source) secondaryVideo2Source.src = '';
}

function showVideoPlayer(videoData) {
    var videoSection = document.getElementById('video-player-section');

    if (videoSection) {
        videoSection.style.display = 'block';

        // Update activity details
        updateActivityDetails(videoData);

        // Check if videos are available
        const hasVideos = setVideoSources(videoData);

        if (hasVideos) {
            hideNoVideosMessage();
            showVideoElements();
            initializeVideos();
        } else {
            hideVideoElements();
            showNoVideosMessage();
        }

        // Initialize comment system
        const workorderId = getWorkorderIdFromURL();
        if (workorderId && typeof VideoCommentManager !== 'undefined') {
            VideoCommentManager.initialize(videoData, workorderId);
        }

        videoSection.scrollIntoView({ behavior: 'smooth' });
    }
}

function updateActivityDetails(videoData) {
    const bladeEl = document.getElementById('video-blade');
    const activityEl = document.getElementById('video-activity');
    const startEl = document.getElementById('video-start');
    const endEl = document.getElementById('video-end');

    if (bladeEl) bladeEl.textContent = videoData.blade || '-';
    if (activityEl) activityEl.textContent = videoData.activity_type || '-';
    if (startEl) startEl.textContent = videoData.start || '-';
    if (endEl) endEl.textContent = videoData.end || '-';

    // Update video labels
    updateVideoLabels(videoData.video_labels || {});
}

function updateVideoLabels(videoLabels) {
    const mainLabel = document.getElementById('main-video-label');
    const secondary1Label = document.getElementById('secondary-video-1-label');
    const secondary2Label = document.getElementById('secondary-video-2-label');

    if (mainLabel) {
        mainLabel.textContent = videoLabels.main || 'Main View';
    }
    if (secondary1Label) {
        secondary1Label.textContent = videoLabels.secondary1 || 'Secondary View 1';
    }
    if (secondary2Label) {
        secondary2Label.textContent = videoLabels.secondary2 || 'Secondary View 2';
    }
}

function showVideoElements() {
    // Show video containers
    const videoContainers = document.querySelectorAll('#video-player-section video');
    videoContainers.forEach(video => {
        video.parentElement.style.display = 'block';
    });

    // Show main video container
    const mainContainer = document.querySelector('#video-player-section .flex.gap-4');
    if (mainContainer) {
        mainContainer.parentElement.style.display = 'block';
    }

    // Show timeline controls
    const controlsSection = document.querySelector('#video-player-section .bg-gray-50');
    if (controlsSection) {
        controlsSection.style.display = 'block';
    }

    // Loading overlays will be shown when videos start loading
}

function hideVideoElements() {
    // Hide video containers
    const videoContainers = document.querySelectorAll('#video-player-section video');
    videoContainers.forEach(video => {
        video.parentElement.style.display = 'none';
    });

    // Hide main video container
    const mainContainer = document.querySelector('#video-player-section .flex.gap-4');
    if (mainContainer) {
        mainContainer.parentElement.style.display = 'none';
    }

    // Hide timeline controls
    const controlsSection = document.querySelector('#video-player-section .bg-gray-50');
    if (controlsSection) {
        controlsSection.style.display = 'none';
    }

    // Hide all overlays (both loading and paused) and labels
    hideLoadingOverlays();
    hideVideoLabels();
    const overlays = [
        'main-video-overlay',
        'secondary-video-1-overlay',
        'secondary-video-2-overlay'
    ];
    overlays.forEach(overlayId => {
        const overlay = document.getElementById(overlayId);
        if (overlay) {
            overlay.style.display = 'none';
        }
    });
}

function showNoVideosMessage() {
    let messageDiv = document.getElementById('no-videos-message');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'no-videos-message';
        messageDiv.className = 'px-6 py-4 mb-4 text-center text-amber-800 bg-amber-50 rounded-lg border-2 border-amber-300';
        messageDiv.textContent = 'No videos available for this activity';

        const videoSection = document.getElementById('video-player-section');
        const activityDetails = videoSection.querySelector('.bg-gray-50.rounded-lg.border-l-4');
        if (activityDetails) {
            videoSection.insertBefore(messageDiv, activityDetails.nextSibling);
        }
    }
    messageDiv.style.display = 'block';
}

function hideNoVideosMessage() {
    const messageDiv = document.getElementById('no-videos-message');
    if (messageDiv) {
        messageDiv.style.display = 'none';
    }
}

function initializeVideos() {
    videos = document.querySelectorAll('#video-player-section video');
    isPlaying = false;

    if (videos.length === 0) {
        console.log('ERROR: No video elements found');
        return;
    }

    const mainVideo = videos[0];

    // Remove existing event listeners to prevent duplicates
    mainVideo.removeEventListener('loadedmetadata', handleLoadedMetadata);
    mainVideo.removeEventListener('ended', handleVideoEnded);
    mainVideo.removeEventListener('timeupdate', updateTimeline);

    // Add event listeners to main video
    mainVideo.addEventListener('loadedmetadata', handleLoadedMetadata);
    mainVideo.addEventListener('ended', handleVideoEnded);
    mainVideo.addEventListener('timeupdate', updateTimeline);

    // Add seeking event listeners to all videos for manual seeking sync
    videos.forEach(function (video, index) {
        video.pause();
        // Don't reset currentTime or call load() here - videos are already loaded

        // Remove click listener for play/pause - control is now centralized
        video.removeEventListener('click', togglePlayPause);
    });

    // Don't show paused overlays initially - loading overlays are shown first
}

function handleLoadedMetadata() {
    // Hide loading overlays and show labels
    hideLoadingOverlays();
    showVideoLabels();

    // Initialize paused overlays (but don't show them since we're about to play)
    showVideoOverlays();

    // Initialize timeline with total duration
    updateTimeline();

    // Start playing immediately
    playAllVideos();
}

function handleVideoEnded() {
    isPlaying = false;
    updatePlayPauseButton();
    updateVideoOverlays();
    updateTimeline(); // Ensure timeline shows completion
}

function togglePlayPause() {
    if (isPlaying) {
        pauseAllVideos();
    } else {
        playAllVideos();
    }
}

function playAllVideos() {
    if (videos.length === 0) return;

    isPlaying = true;
    updatePlayPauseButton();
    updateVideoOverlays();

    // Just play all videos at their current positions
    videos.forEach(function (video, index) {
        if (video) {
            video.play().catch(function (error) {
                console.warn(`Failed to play video ${index + 1}:`, error);
            });
        }
    });
}

function pauseAllVideos() {
    isPlaying = false;
    updatePlayPauseButton();
    updateVideoOverlays();
    updateTimeline(); // Update timeline when paused

    videos.forEach(function (video) {
        video.pause();
    });
}

function updatePlayPauseButton() {
    const playIcon = document.getElementById('play-icon');
    const pauseIcon = document.getElementById('pause-icon');
    const button = document.getElementById('play-pause-btn');

    if (isPlaying) {
        if (playIcon) playIcon.classList.add('hidden');
        if (pauseIcon) pauseIcon.classList.remove('hidden');
        if (button) {
            button.classList.remove('bg-blue-500', 'hover:bg-blue-600');
            button.classList.add('bg-red-500', 'hover:bg-red-600');
        }
    } else {
        if (playIcon) playIcon.classList.remove('hidden');
        if (pauseIcon) pauseIcon.classList.add('hidden');
        if (button) {
            button.classList.remove('bg-red-500', 'hover:bg-red-600');
            button.classList.add('bg-blue-500', 'hover:bg-blue-600');
        }
    }
}

function updateVideoOverlays() {
    const overlays = [
        'main-video-overlay',
        'secondary-video-1-overlay',
        'secondary-video-2-overlay'
    ];

    overlays.forEach(overlayId => {
        const overlay = document.getElementById(overlayId);
        if (overlay) {
            if (isPlaying) {
                overlay.style.opacity = '0';
                overlay.style.pointerEvents = 'none';
            } else {
                overlay.style.opacity = '1';
                overlay.style.pointerEvents = 'none'; // Not clickable - just informational
            }
        }
    });
}

function showLoadingOverlays() {
    const loadingOverlays = [
        'main-video-loading',
        'secondary-video-1-loading',
        'secondary-video-2-loading'
    ];

    loadingOverlays.forEach(loadingId => {
        const loading = document.getElementById(loadingId);
        if (loading) {
            loading.style.display = 'flex';
        }
    });

    // Hide paused overlays and video labels during loading
    hideVideoOverlays();
    hideVideoLabels();
}

function hideLoadingOverlays() {
    const loadingOverlays = [
        'main-video-loading',
        'secondary-video-1-loading',
        'secondary-video-2-loading'
    ];

    loadingOverlays.forEach(loadingId => {
        const loading = document.getElementById(loadingId);
        if (loading) {
            loading.style.display = 'none';
        }
    });
}

function hideVideoOverlays() {
    const overlays = [
        'main-video-overlay',
        'secondary-video-1-overlay',
        'secondary-video-2-overlay'
    ];

    overlays.forEach(overlayId => {
        const overlay = document.getElementById(overlayId);
        if (overlay) {
            overlay.style.display = 'none';
        }
    });
}

function showVideoOverlays() {
    const overlays = [
        'main-video-overlay',
        'secondary-video-1-overlay',
        'secondary-video-2-overlay'
    ];

    overlays.forEach(overlayId => {
        const overlay = document.getElementById(overlayId);
        if (overlay) {
            overlay.style.display = 'flex';
        }
    });
}

function hideVideoLabels() {
    const labels = [
        'main-video-label',
        'secondary-video-1-label',
        'secondary-video-2-label'
    ];

    labels.forEach(labelId => {
        const label = document.getElementById(labelId);
        if (label) {
            label.style.display = 'none';
        }
    });
}

function showVideoLabels() {
    const labels = [
        'main-video-label',
        'secondary-video-1-label',
        'secondary-video-2-label'
    ];

    labels.forEach(labelId => {
        const label = document.getElementById(labelId);
        if (label) {
            label.style.display = 'block';
        }
    });
}

function formatTime(seconds) {
    if (isNaN(seconds) || seconds < 0) return '0:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

function updateTimeline() {
    const mainVideo = videos[0];
    if (!mainVideo) return;

    const currentTime = mainVideo.currentTime;
    const duration = mainVideo.duration;

    // Update time displays
    const currentTimeEl = document.getElementById('current-time');
    const totalTimeEl = document.getElementById('total-time');
    const progressEl = document.getElementById('timeline-progress');

    if (currentTimeEl) {
        currentTimeEl.textContent = formatTime(currentTime);
    }

    if (totalTimeEl && duration) {
        totalTimeEl.textContent = formatTime(duration);
    }

    // Update progress bar
    if (progressEl && duration > 0) {
        const progressPercent = (currentTime / duration) * 100;
        progressEl.style.width = `${Math.min(progressPercent, 100)}%`;
    }
}

// Debug function to check video status - can be called from browser console
function debugVideoStatus() {
    console.log('=== VIDEO STATUS DEBUG ===');
    console.log(`Total videos: ${videos.length}`);

    videos.forEach(function (video, index) {
        console.log(`\nVideo ${index + 1}:`, {
            element: video,
            src: video.currentSrc ? video.currentSrc.split('/').pop() : 'No source',
            duration: video.duration || 'Not loaded',
            currentTime: video.currentTime.toFixed(2),
            readyState: video.readyState,
            readyStateText: getReadyStateText(video.readyState),
            paused: video.paused,
            ended: video.ended,
            networkState: video.networkState
        });
    });

    console.log('=== END VIDEO STATUS DEBUG ===');
}

function getReadyStateText(readyState) {
    const states = {
        0: 'HAVE_NOTHING',
        1: 'HAVE_METADATA',
        2: 'HAVE_CURRENT_DATA',
        3: 'HAVE_FUTURE_DATA',
        4: 'HAVE_ENOUGH_DATA'
    };
    return states[readyState] || 'UNKNOWN';
}

// Helper function to extract workorder ID from current URL
function getWorkorderIdFromURL() {
    const path = window.location.pathname;
    const match = path.match(/\/workorder\/(\d+)\//);
    return match ? parseInt(match[1]) : null;
}

// Make debug functions globally available
window.debugVideoStatus = debugVideoStatus;

// Retry attaching handlers if needed
setTimeout(function () {
    if (document.querySelectorAll('.plotly-graph-div').length > 0) {
        attachClickHandlers();
    }
}, 2000);

// Clean up video memory on page unload
window.addEventListener('beforeunload', function() {
    clearVideoSources();
    videos = []; // Clear global video references
});