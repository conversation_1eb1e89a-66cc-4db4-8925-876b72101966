"""
Helper functions for FSM Payroll data processing and BigQuery integration.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional, Any
from collections import defaultdict
import json

from myproject.utils import get_bigquery_client

def get_payroll_query_results(func_name: str = "get_payroll_query_results"):
    """
    Execute the payroll query and return all results.

    Args:
        func_name: Function name for logging

    Returns:
        BigQuery results object or None if error
    """
    client = get_bigquery_client()
    if client is None:
        return None

    try:
        # Simple query to get all data
        query = """
        SELECT
            user_email,
            entry_date,
            SUM(hours) as total_hours,
            SUM(CASE WHEN approved = 1 THEN hours ELSE 0 END) as approved_hours,
            SUM(CASE WHEN approved = 0 THEN hours ELSE 0 END) as rejected_hours,
            SUM(CASE WHEN approved IS NULL THEN hours ELSE 0 END) as pending_hours,
            ARRAY_AGG(DISTINCT project_name IGNORE NULLS) as projects,
            ARRAY_AGG(DISTINCT activity_name IGNORE NULLS) as activities,
            COUNT(*) as entry_count,
            MIN(hours_start) as earliest_start,
            MAX(hours_end) as latest_end,
            STRING_AGG(DISTINCT CASE WHEN comment IS NOT NULL AND comment != '' THEN comment END, '; ' LIMIT 3) as sample_comments,
            STRING_AGG(DISTINCT CASE WHEN approved_by IS NOT NULL AND approved_by != '' THEN approved_by END, ', ' LIMIT 3) as approved_by,
            MAX(approved_at) as approved_at,
            SUM(CASE WHEN units IS NOT NULL THEN units ELSE 0 END) as total_units,
            ARRAY_AGG(STRUCT(
                project_name,
                activity_name,
                CASE WHEN comment IS NOT NULL AND comment != '' THEN comment ELSE NULL END as comment,
                CASE WHEN units IS NOT NULL THEN units ELSE 0 END as units,
                hours_start,
                hours_end
            ) IGNORE NULLS) as project_activities
        FROM `budibase-dev-369406.presentation.aerotask_full_timelog`
        GROUP BY user_email, entry_date
        ORDER BY user_email, entry_date
        """

        query_job = client.query(query)
        results = query_job.result()

        return results

    except Exception as e:
        return None


def process_payroll_results(results, func_name: str = "process_payroll_results") -> Dict[str, Any]:
    """
    Process BigQuery results into structured data for frontend.
    
    Args:
        results: BigQuery results object
        func_name: Function name for logging
        
    Returns:
        Structured data dictionary for frontend consumption
    """
    try:
        # Convert results to list of dictionaries
        data_rows = [dict(row) for row in results]
        
        if not data_rows:
            return {
                'technicians': [],
                'dates': [],
                'data': {},
                'summary': {}
            }
        
        # Extract unique technicians and dates
        technicians = sorted(list(set(row['user_email'] for row in data_rows)))
        dates = sorted(list(set(row['entry_date'] for row in data_rows)))
        
        # Organize data by technician and date
        payroll_data = defaultdict(lambda: defaultdict(dict))
        technician_summaries = {}

        for row in data_rows:
            technician = row['user_email']
            entry_date = row['entry_date']

            # Store daily data
            payroll_data[technician][entry_date] = {
                'total_hours': float(row['total_hours']) if row['total_hours'] else 0,
                'approved_hours': float(row['approved_hours']) if row['approved_hours'] else 0,
                'rejected_hours': float(row['rejected_hours']) if row['rejected_hours'] else 0,
                'pending_hours': float(row['pending_hours']) if row['pending_hours'] else 0,
                'projects': row['projects'] if row['projects'] else [],
                'activities': row['activities'] if row['activities'] else [],
                'entry_count': int(row['entry_count']) if row['entry_count'] else 0,
                'earliest_start': row['earliest_start'],
                'latest_end': row['latest_end'],
                'sample_comments': row['sample_comments'],
                'approved_by': row['approved_by'] if row['approved_by'] else None,
                'approved_at': row['approved_at'] if row['approved_at'] else None,
                'total_units': float(row['total_units']) if row['total_units'] else 0,
                'project_activities': row['project_activities'] if row['project_activities'] else []
            }

        # Calculate technician summaries from daily data
        for technician, tech_data in payroll_data.items():
            total_hours = sum(day['total_hours'] for day in tech_data.values())
            approved_hours = sum(day['approved_hours'] for day in tech_data.values())
            rejected_hours = sum(day['rejected_hours'] for day in tech_data.values())
            pending_hours = sum(day['pending_hours'] for day in tech_data.values())
            total_units = sum(day['total_units'] for day in tech_data.values())
            days_worked = len(tech_data)

            technician_summaries[technician] = {
                'total_hours_period': total_hours,
                'total_approved_period': approved_hours,
                'total_rejected_period': rejected_hours,
                'total_pending_period': pending_hours,
                'total_units_period': total_units,
                'days_worked': days_worked
            }
        
        # Convert data structure to ensure all dates are strings
        processed_data = {}
        for technician, tech_data in payroll_data.items():
            processed_data[technician] = {}
            for entry_date, day_data in tech_data.items():
                # Convert date to string if it's a date object
                date_key = entry_date.isoformat() if hasattr(entry_date, 'isoformat') else str(entry_date)
                processed_data[technician][date_key] = day_data

        return {
            'technicians': technicians,
            'dates': [date.isoformat() if hasattr(date, 'isoformat') else str(date) for date in dates],
            'data': processed_data,
            'technician_summaries': technician_summaries,
            'summary': {
                'total_technicians': len(technicians),
                'total_dates': len(dates),
                'date_range': {
                    'start': dates[0].isoformat() if dates and hasattr(dates[0], 'isoformat') else str(dates[0]) if dates else None,
                    'end': dates[-1].isoformat() if dates and hasattr(dates[-1], 'isoformat') else str(dates[-1]) if dates else None
                }
            }
        }
        
    except Exception as e:
        return {
            'technicians': [],
            'dates': [],
            'data': {},
            'summary': {}
        }


def determine_cell_status(approved_hours: float, rejected_hours: float, pending_hours: float, total_hours: float) -> str:
    """
    Determine the approval status for a cell based on hours breakdown.

    Args:
        approved_hours: Hours that are approved
        rejected_hours: Hours that are rejected
        pending_hours: Hours that are pending
        total_hours: Total hours for the day

    Returns:
        Status string for color coding
    """
    if total_hours == 0:
        return 'no_data'

    # Show approved (green) if any hours are approved
    if approved_hours > 0:
        return 'approved'
    elif rejected_hours == total_hours:
        return 'rejected'
    elif pending_hours == total_hours:
        return 'pending'
    else:
        return 'mixed'
