const SetsFilterManager = {
    tomSelectInstances: {},

    /**
     * Initializes all filter components.
     */
    initialize() {
        this.initializeTomSelect();
        this.loadFiltersFromURL();
        this.setupEventListeners();
        this.setupCustomScrollBehavior();
    },

    /**
     * Initializes Tom Select for all filter dropdowns.
     */
    initializeTomSelect() {
        if (typeof TomSelect === 'undefined') return;

        // Common Tom Select options
        const commonOptions = {
            plugins: ['remove_button'],
            create: false,
            searchField: ['text', 'value'],
            placeholder: 'All selected',
            render: {
                no_results: function() {
                    return '<div class="no-results">No results found</div>';
                }
            }
        };

        // Initialize all filters with Tom Select
        const filterConfigs = [
            { id: 'region', placeholder: 'Select regions...' },
            { id: 'sets', placeholder: 'Select sets...' },
            { id: 'service-codes', placeholder: 'Select service codes...' },
            { id: 'projects', placeholder: 'Select projects...' }
        ];

        filterConfigs.forEach(config => {
            const element = document.getElementById(config.id);
            if (element) {
                this.tomSelectInstances[config.id] = new TomSelect(`#${config.id}`, {
                    ...commonOptions,
                    placeholder: config.placeholder,
                    onChange: () => {
                        // Update URL when filters change
                        setTimeout(() => {
                            UrlStateManager.updateURLWithFilters();
                        }, 100);
                    }
                });
            }
        });
    },



    /**
     * Loads filter values from URL parameters and applies them.
     */
    loadFiltersFromURL() {
        const filters = UrlStateManager.getFiltersFromURL();

        // Set values for each Tom Select instance
        if (this.tomSelectInstances.region) {
            this.tomSelectInstances.region.setValue(filters.regions, true);
        }
        if (this.tomSelectInstances.sets) {
            this.tomSelectInstances.sets.setValue(filters.sets, true);
        }
        if (this.tomSelectInstances['service-codes']) {
            this.tomSelectInstances['service-codes'].setValue(filters.serviceCodes, true);
        }
        if (this.tomSelectInstances.projects) {
            this.tomSelectInstances.projects.setValue(filters.projects, true);
        }


    },

    /**
     * Sets up event listeners for filter changes.
     */
    setupEventListeners() {
        // Tom Select handles change events through the onChange callback
        // No additional event listeners needed
    },

    /**
     * Sets up custom scroll behavior for Tom Select controls to make scrolling less sensitive.
     */
    setupCustomScrollBehavior() {
        // Wait a bit for Tom Select to fully initialize
        setTimeout(() => {
            const tomSelectControls = document.querySelectorAll('.ts-wrapper .ts-control');

            tomSelectControls.forEach(control => {
                control.addEventListener('wheel', (e) => {
                    e.preventDefault();

                    // Reduce scroll sensitivity by dividing the delta
                    const scrollAmount = e.deltaY / 6; // Reduce scroll speed by 6x

                    // Apply smooth scrolling
                    control.scrollBy({
                        top: scrollAmount,
                        behavior: 'smooth'
                    });
                }, { passive: false });
            });
        }, 500);
    }
};
