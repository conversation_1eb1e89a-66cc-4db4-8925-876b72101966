from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from .helper_functions import *
import json


@csrf_exempt
def project_data(request):
    filters_from_client = None
    draw = 1 # Default draw value

    if request.method == "POST":
        draw = int(request.POST.get('draw', 1))
        filter_json_string = request.POST.get('filters')
        if filter_json_string:
            try:
                filters_from_client = json.loads(filter_json_string)
            except json.JSONDecodeError:
                filters_from_client = None # Or log an error
        
        data = fetch_project_data(filters=filters_from_client)
        
        # For server-side processing, recordsTotal is the total count before filtering,
        # and recordsFiltered is the count after filtering.
        records_filtered = len(data)
        
        # For simplicity, using records_filtered for recordsTotal.
        # This means pagination is relative to the current filtered set.
        response_data = {
            'data': data,
            'recordsTotal': records_filtered, 
            'recordsFiltered': records_filtered,
            'draw': draw
        }
    else:
        # Handle GET or other methods
        initial_data = fetch_project_data() # Fetch all data for initial GET
        response_data = {
            'data': initial_data,
            'recordsTotal': len(initial_data),
            'recordsFiltered': len(initial_data),
            'draw': draw
        }

    return JsonResponse(response_data, safe=False)

def project_view(request):
    weekly_metrics = fetch_weekly_metrics()
    monthly_metrics = fetch_monthly_metrics()
    context = {
        'weekly_metrics': weekly_metrics,
        'monthly_metrics': monthly_metrics,
    }
    return render(request, 'projects/project_view.html', context)
