const CommentManager = {
    currentSetName: null,
    currentDate: null,
    currentUserEmail: null,

    /**
     * Opens the comment popup for a specific set and date.
     * @param {string} setName - The name of the set.
     * @param {string} date - The date in YYYY-MM-DD format.
     */
    async openCommentPopup(setName, date) {
        this.currentSetName = setName;
        this.currentDate = date;

        // Show loading popup immediately
        this.showLoadingPopup();

        try {
            // Fetch existing comments
            const response = await fetch(`/setworkload/api/comments/?set_name=${encodeURIComponent(setName)}&date=${date}`);
            const data = await response.json();

            if (data.success) {
                this.currentUserEmail = data.current_user_email;
                // Replace loading popup with actual content
                this.showCommentPopup(data.comments);
            } else {
                this.closeCommentPopup();
                console.error('Error fetching comments:', data.error);
                alert('Error loading comments. Please try again.');
            }
        } catch (error) {
            this.closeCommentPopup();
            console.error('Error fetching comments:', error);
            alert('Error loading comments. Please try again.');
        }
    },

    /**
     * Shows the comment popup with the given comments.
     * @param {Array} comments - Array of comment objects.
     */
    showCommentPopup(comments) {
        // Remove existing popup if any
        this.closeCommentPopup();

        // Create popup overlay
        const overlay = document.createElement('div');
        overlay.id = 'comment-popup-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.onclick = (e) => {
            if (e.target === overlay) {
                this.closeCommentPopup();
            }
        };

        // Create popup container
        const popup = document.createElement('div');
        popup.className = 'bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden';
        
        // Create popup content
        popup.innerHTML = `
            <div class="p-6">
                <!-- Header Section -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-1">
                        <h3 class="text-lg font-semibold text-gray-900">Supervisor comments for ${this.currentSetName}</h3>
                        <button id="close-comment-popup" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        On ${this.formatDate(this.currentDate)}
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="mb-6">
                    <div id="comments-list" class="max-h-60 overflow-y-auto">
                        ${this.renderComments(comments)}
                    </div>
                </div>

                <!-- Add Comment Section -->
                <div class="border-t pt-4">
                    <textarea
                        id="new-comment-text"
                        placeholder="Add a comment..."
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="3"
                    ></textarea>

                    <div class="flex justify-end space-x-3 mt-3">
                        <button
                            id="cancel-comment"
                            class="px-4 py-2 text-blue-600 hover:text-blue-800 font-medium"
                        >
                            Cancel
                        </button>
                        <button
                            id="submit-comment"
                            class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled
                        >
                            Comment
                        </button>
                    </div>
                </div>
            </div>
        `;

        overlay.appendChild(popup);
        document.body.appendChild(overlay);

        // Add event listeners
        this.setupPopupEventListeners();
    },

    /**
     * Renders the comments HTML.
     * @param {Array} comments - Array of comment objects.
     * @returns {string} HTML string for comments.
     */
    renderComments(comments) {
        if (comments.length === 0) {
            return '<div class="text-gray-500 text-sm">No comments yet.</div>';
        }

        return comments.map(comment => `
            <div class="mb-4 pb-4 border-b border-gray-100 last:border-b-0">
                <div class="flex items-start mb-2">
                    <svg class="w-4 h-4 mr-2 text-gray-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <div class="flex flex-col">
                        <span class="text-sm font-medium text-gray-900">${comment.user_email}</span>
                        <span class="text-xs text-gray-500">${this.formatDateTimeWithoutTimezone(comment.created_at)}</span>
                    </div>
                </div>
                <div class="text-sm text-gray-700 ml-6">
                    ${this.escapeHtml(comment.comment_text)}
                </div>
            </div>
        `).join('');
    },

    /**
     * Sets up event listeners for the popup.
     */
    setupPopupEventListeners() {
        const closeBtn = document.getElementById('close-comment-popup');
        const cancelBtn = document.getElementById('cancel-comment');
        const submitBtn = document.getElementById('submit-comment');
        const textarea = document.getElementById('new-comment-text');

        closeBtn.onclick = () => this.closeCommentPopup();
        cancelBtn.onclick = () => this.closeCommentPopup();
        submitBtn.onclick = () => this.submitComment();

        // Enable/disable submit button based on textarea content
        textarea.oninput = () => {
            const hasText = textarea.value.trim().length > 0;
            submitBtn.disabled = !hasText;
            submitBtn.className = hasText 
                ? 'px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium'
                : 'px-4 py-2 bg-gray-500 text-white rounded-lg font-medium opacity-50 cursor-not-allowed';
        };

        // Handle Enter key (Ctrl+Enter to submit)
        textarea.onkeydown = (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                if (!submitBtn.disabled) {
                    this.submitComment();
                }
            }
        };
    },

    /**
     * Submits a new comment.
     */
    async submitComment() {
        const textarea = document.getElementById('new-comment-text');
        const submitBtn = document.getElementById('submit-comment');
        const commentText = textarea.value.trim();

        if (!commentText) {
            return;
        }

        // Disable submit button during submission
        submitBtn.disabled = true;
        submitBtn.textContent = 'Submitting...';

        try {
            const response = await fetch('/setworkload/api/comments/add/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    set_name: this.currentSetName,
                    date: this.currentDate,
                    comment_text: commentText
                })
            });

            const data = await response.json();

            if (data.success) {
                // Refresh the popup with updated comments
                this.openCommentPopup(this.currentSetName, this.currentDate);
                // Update the comment icon in the table
                this.updateCommentIconInTable(this.currentSetName, this.currentDate);
            } else {
                console.error('Error adding comment:', data.error);
                alert('Error adding comment. Please try again.');
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Comment';
            }
        } catch (error) {
            console.error('Error adding comment:', error);
            alert('Error adding comment. Please try again.');
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.textContent = 'Comment';
        }
    },

    /**
     * Closes the comment popup.
     */
    closeCommentPopup() {
        const overlay = document.getElementById('comment-popup-overlay');
        if (overlay) {
            overlay.remove();
        }
    },

    /**
     * Shows a loading popup while data is being fetched
     */
    showLoadingPopup() {
        // Remove existing popup if any
        this.closeCommentPopup();

        // Create popup overlay
        const overlay = document.createElement('div');
        overlay.id = 'comment-popup-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.onclick = (e) => {
            if (e.target === overlay) {
                this.closeCommentPopup();
            }
        };

        // Create popup container
        const popup = document.createElement('div');
        popup.className = 'bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden';

        // Create loading content
        popup.innerHTML = `
            <div class="p-6">
                <!-- Header Section -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-1">
                        <h3 class="text-lg font-semibold text-gray-900">Supervisor comments for ${this.currentSetName}</h3>
                        <button id="close-comment-popup" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        On ${this.formatDate(this.currentDate)}
                    </div>
                </div>

                <!-- Loading Section -->
                <div class="flex flex-col items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                    <div class="text-sm text-gray-500">Loading data...</div>
                </div>
            </div>
        `;

        overlay.appendChild(popup);
        document.body.appendChild(overlay);

        // Add close button event listener
        const closeBtn = document.getElementById('close-comment-popup');
        closeBtn.onclick = () => this.closeCommentPopup();
    },

    /**
     * Formats a date string for display.
     * @param {string} dateStr - Date string in YYYY-MM-DD format.
     * @returns {string} Formatted date string.
     */
    formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        });
    },

    /**
     * Formats a datetime string in the user's timezone without timezone brackets.
     * @param {string} isoDateStr - ISO datetime string from the server.
     * @returns {string} Formatted datetime string without timezone.
     */
    formatDateTimeWithoutTimezone(isoDateStr) {
        const date = new Date(isoDateStr);

        // Get user's timezone
        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // Format the date in user's timezone
        const formattedDate = date.toLocaleDateString('en-GB', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            timeZone: userTimezone
        });

        const formattedTime = date.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
            timeZone: userTimezone
        });

        return `${formattedDate} ${formattedTime}`;
    },

    /**
     * Formats a datetime string in the user's timezone.
     * @param {string} isoDateStr - ISO datetime string from the server.
     * @returns {string} Formatted datetime string with user's timezone.
     */
    formatDateTimeInUserTimezone(isoDateStr) {
        const date = new Date(isoDateStr);

        // Get user's timezone
        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // Format the date in user's timezone
        const formattedDate = date.toLocaleDateString('en-GB', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            timeZone: userTimezone
        });

        const formattedTime = date.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
            timeZone: userTimezone
        });

        // Get timezone abbreviation
        const timezoneAbbr = this.getTimezoneAbbreviation(date, userTimezone);

        return `${formattedDate} ${formattedTime} (${timezoneAbbr})`;
    },

    /**
     * Gets the timezone abbreviation for a given date and timezone.
     * @param {Date} date - The date object.
     * @param {string} timezone - The timezone identifier.
     * @returns {string} Timezone abbreviation.
     */
    getTimezoneAbbreviation(date, timezone) {
        try {
            // Use Intl.DateTimeFormat to get timezone name
            const formatter = new Intl.DateTimeFormat('en', {
                timeZone: timezone,
                timeZoneName: 'short'
            });

            const parts = formatter.formatToParts(date);
            const timeZonePart = parts.find(part => part.type === 'timeZoneName');

            return timeZonePart ? timeZonePart.value : 'Local';
        } catch (error) {
            // Fallback to UTC offset if timezone name fails
            const offset = date.getTimezoneOffset();
            const hours = Math.floor(Math.abs(offset) / 60);
            const minutes = Math.abs(offset) % 60;
            const sign = offset <= 0 ? '+' : '-';
            return `GMT${sign}${hours.toString().padStart(2, '0')}${minutes > 0 ? ':' + minutes.toString().padStart(2, '0') : ''}`;
        }
    },

    /**
     * Escapes HTML characters in a string.
     * @param {string} text - Text to escape.
     * @returns {string} Escaped text.
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * Updates the comment icon in the table after a comment is added.
     * @param {string} setName - The name of the set.
     * @param {string} date - The date in YYYY-MM-DD format.
     */
    async updateCommentIconInTable(setName, date) {
        // Find the comment icon for this set/date combination
        const commentIcons = document.querySelectorAll('.comment-icon');
        for (const icon of commentIcons) {
            if (icon.dataset.setName === setName && icon.dataset.date === date) {
                // Update the icon to show it has comments by checking directly
                this.checkAndUpdateCommentIcon(icon, setName, date);
                break;
            }
        }
    },

    /**
     * Checks if a day has comments and updates the icon appearance accordingly.
     * @param {HTMLElement} commentIcon - The comment icon element.
     * @param {string} setName - The name of the set.
     * @param {string} date - The date in YYYY-MM-DD format.
     */
    async checkAndUpdateCommentIcon(commentIcon, setName, date) {
        try {
            const response = await fetch(`/setworkload/api/comments/?set_name=${encodeURIComponent(setName)}&date=${date}`);
            const data = await response.json();

            if (data.success && data.comments.length > 0) {
                // Add red badge with comment count
                this.addCommentBadge(commentIcon, data.comments.length);
                commentIcon.title = `View/Add Supervisor Comments (${data.comments.length} comment${data.comments.length > 1 ? 's' : ''})`;
            }
        } catch (error) {
            // Silently fail - icon will remain in default state
            console.debug('Could not check comment status for', setName, date);
        }
    },

    /**
     * Adds a red badge with comment count to the comment icon.
     * @param {HTMLElement} commentIcon - The comment icon element.
     * @param {number} commentCount - The number of comments.
     */
    addCommentBadge(commentIcon, commentCount) {
        // Remove existing badge if any
        const existingBadge = commentIcon.parentElement.querySelector('.comment-badge');
        if (existingBadge) {
            existingBadge.remove();
        }

        // Create the badge
        const badge = document.createElement('div');
        badge.className = 'comment-badge absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold z-20';
        badge.textContent = commentCount > 99 ? '99+' : commentCount.toString();

        // Add the badge to the comment icon container
        commentIcon.parentElement.appendChild(badge);
    }
};