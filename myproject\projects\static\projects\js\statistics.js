/**
 * Statistics functionality for project data
 * Includes both calculation functions and UI enhancements
 */

// -----------------------------------------------------
// DATA CALCULATION FUNCTIONS
// -----------------------------------------------------

/**
 * Calculate statistics from project data
 * 
 * @param {Array} array - Array of project data objects
 * @returns {Object} Object containing calculated statistics
 */
export function getStatisticsData(array) {
    let startDateVariation = 0;
    let startDateCounter = 0;

    let endDateVariation = 0;
    let endDateCounter = 0;

    let startedRowCounter = 0;
    let startedOnTimeCounter = 0;

    let dateChangesTotal = 0;
    let dateChangesCounter = 0;

    let completedRowCounter = 0;
    let completedOnTimeCounter = 0;

    array.forEach((el) => {
        if (typeof el.abs_start_date_variance === "number") {
            startDateVariation = startDateVariation + el.abs_start_date_variance;
            startDateCounter += 1;
        }

        if (typeof el.abs_end_date_variance === "number") {
            endDateVariation = endDateVariation + el.abs_end_date_variance;
            endDateCounter += 1;
        }

        if (typeof el.date_changes_count === "number") {
            dateChangesTotal += el.date_changes_count;
            dateChangesCounter += 1;
        }

        if (typeof el.started_on_time === "boolean") {
            startedRowCounter += 1;
            if (el.started_on_time) {
                startedOnTimeCounter += 1;
            }
        }

        if (typeof el.completed_on_time === "boolean") {
            completedRowCounter += 1;
            if (el.completed_on_time) {
                completedOnTimeCounter += 1;
            }
        }
    });

    return {
        avgStartVariance: Math.round(startDateVariation / startDateCounter),
        avgEndVariance: Math.round(endDateVariation / endDateCounter),
        avgDateChanges: Math.round(dateChangesTotal / dateChangesCounter),
        onTimeStartRate: Math.round((startedOnTimeCounter / startedRowCounter) * 100),
        onTimeCompleteRate: Math.round((completedOnTimeCounter / completedRowCounter) * 100),
    };
}

/**
 * Update statistics DOM elements with calculated values
 * 
 * @param {Array} filteredData - Array of filtered project data
 */
export function updateStatistics(filteredData) {
    const stats = getStatisticsData(Object.values(filteredData));

    $("#projects_total").text(filteredData.length);
    $("#projects_avgStartVariance").text(isNaN(stats.avgStartVariance) ? "-" : stats.avgStartVariance);
    $("#projects_avgDateChanges").text(isNaN(stats.avgDateChanges) ? "-" : stats.avgDateChanges);
    $("#projects_avgEndVariance").text(isNaN(stats.avgEndVariance) ? "-" : stats.avgEndVariance);
    $("#projects_onTimeStartRate").text(isNaN(stats.onTimeStartRate) ? "-" : `${stats.onTimeStartRate}%`);
    $("#projects_onTimeCompleteRate").text(isNaN(stats.onTimeCompleteRate) ? "-" : `${stats.onTimeCompleteRate}%`);
    
    // After updating values, apply color coding
    updatePercentageColors();
}

// -----------------------------------------------------
// UI ENHANCEMENT FUNCTIONS
// -----------------------------------------------------

/**
 * Update colors for percentage values based on thresholds
 */
function updatePercentageColors() {
    ['projects_onTimeStartRate', 'projects_onTimeCompleteRate'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            const value = parseInt(element.textContent);
            if (!isNaN(value)) {
                // Remove existing color classes
                element.classList.remove('text-green-600', 'text-yellow-600', 'text-red-600');
                
                // Add appropriate color class
                if (value >= 80) {
                    element.classList.add('text-green-600');
                } else if (value >= 50) {
                    element.classList.add('text-yellow-600');
                } else {
                    element.classList.add('text-red-600');
                }
            }
        }
    });
}

/**
 * Initialize statistics UI enhancements
 * Sets up observers to watch for content changes
 */
export function initializeStatisticsUI() {
    // Observer to watch for content changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.target.id === 'projects_onTimeStartRate' || 
                mutation.target.id === 'projects_onTimeCompleteRate') {
                updatePercentageColors();
            }
        });
    });

    // Start observing the percentage elements
    ['projects_onTimeStartRate', 'projects_onTimeCompleteRate'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            observer.observe(element, { 
                childList: true, 
                characterData: true, 
                subtree: true 
            });
        }
    });
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', initializeStatisticsUI);
