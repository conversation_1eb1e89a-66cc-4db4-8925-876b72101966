<header class="bg-white shadow-sm mb-2">
  <div class="max-w-full mx-auto py-3 px-4 relative">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button id="mobile-menu-button" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
        <div id="nav-content" class="hidden lg:flex lg:items-center lg:space-x-4 absolute lg:static left-0 right-0 top-full lg:top-auto bg-white lg:bg-transparent shadow-lg lg:shadow-none p-4 lg:p-0 z-50 flex-col lg:flex-row w-full lg:w-auto">
          <div class="flex flex-col lg:flex-row items-stretch w-full gap-3">
            <a
              href="{% url 'workorder_view' %}"
              class="{% if 'workorder' in request.resolver_match.url_name or request.resolver_match.url_name == 'home' %}bg-blue-500 text-white hover:shadow hover:bg-blue-600 {% else %} bg-white hover:bg-gray-50 text-gray-700 hover:shadow {% endif %} px-4 py-2 rounded-lg transition-all duration-300 text-center font-medium text-sm w-full lg:w-[120px]"
              >Workorders</a
            >
            <a
              href="{% url 'projects:project_view' %}"
              class="{% if request.resolver_match.url_name == 'project_view' %}bg-blue-500 text-white hover:shadow hover:bg-blue-600 {% else %} bg-white hover:bg-gray-50 text-gray-700 hover:shadow {% endif %} px-5 py-2 rounded-lg transition-all duration-300 text-center font-medium text-sm w-full lg:w-[130px]"
              >Projects</a
            >
            <a
              href="{% url 'setworkload:set_workload_view' %}"
              class="{% if request.resolver_match.url_name == 'set_workload_view' %}bg-blue-500 text-white hover:shadow hover:bg-blue-600 {% else %} bg-white hover:bg-gray-50 text-gray-700 hover:shadow {% endif %} px-5 py-2 rounded-lg transition-all duration-300 text-center font-medium text-sm w-full lg:w-[130px]"
              >Workload</a
            >
            <a
              href="{% url 'sets_monitoring_app:dashboard' %}"
              class="{% if request.resolver_match.url_name == 'dashboard' %}bg-blue-500 text-white hover:shadow hover:bg-blue-600 {% else %} bg-white hover:bg-gray-50 text-gray-700 hover:shadow {% endif %} px-5 py-2 rounded-lg transition-all duration-300 text-center font-medium text-sm w-full lg:w-[130px]"
              >Sets</a
            >
            <a
              href="{% url 'fsm_payroll:payroll_view' %}"
              class="{% if request.resolver_match.url_name == 'payroll_view' %}bg-blue-500 text-white hover:shadow hover:bg-blue-600 {% else %} bg-white hover:bg-gray-50 text-gray-700 hover:shadow {% endif %} px-5 py-2 rounded-lg transition-all duration-300 text-center font-medium text-sm w-full lg:w-[130px]"
              >Payroll</a
            >
          </div>
        </div>
      </div>

      <div class="flex items-center gap-3 ml-auto">
        <div class="w-auto">
          {% block actions %}
          {% include 'page_actions.html' %}
          {% endblock %}
        </div>
      </div>
    </div>
  </div>
</header>

<script>
  document.getElementById('mobile-menu-button').addEventListener('click', function() {
    const navContent = document.getElementById('nav-content');
    navContent.classList.toggle('hidden');
  });
</script>
