{% extends 'base.html' %}
{% load static %}

{% block title %}Set Workload{% endblock %}

{% block extra_head %}
<!-- JavaScript dependencies -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Load in dependency order -->
<script src="{% static 'setworkload/js/metrics/metricsCalculator.js' %}"></script>
<script src="{% static 'setworkload/js/utilities/statusManager.js' %}"></script>
<script src="{% static 'setworkload/js/store.js' %}"></script>

<script src="{% static 'setworkload/js/filters/urlStateManager.js' %}"></script>
<script src="{% static 'setworkload/js/filters/dateRangeController.js' %}"></script>
<script src="{% static 'setworkload/js/filters/dropdownController.js' %}"></script>
<script src="{% static 'setworkload/js/filters/filterManager.js' %}"></script>

<script src="{% static 'setworkload/js/table/cellFormatters.js' %}"></script>
<script src="{% static 'setworkload/js/table/tableEventHandlers.js' %}"></script>
<script src="{% static 'setworkload/js/table/tableStructure.js' %}"></script>
<script src="{% static 'setworkload/js/table/tableManager.js' %}"></script>

<script src="{% static 'setworkload/js/utilities/commentManager.js' %}"></script>
<script src="{% static 'setworkload/js/utilities/reportManager.js' %}"></script>

<script src="{% static 'setworkload/js/main.js' %}"></script>

<!-- Load Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/erimicel/select2-tailwindcss-theme/dist/select2-tailwindcss-theme-plain.min.css">
<link href="{% static 'setworkload/css/select2-theme.css' %}" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="overflow-hidden w-full">
    <div class="overflow-x-auto w-full">
        <!-- Hidden data element for JavaScript initialization -->
        <div id="workload-data" data-workload='{{ workload_data_json|safe }}' class="hidden"></div>
        
        <!-- CSRF token for API requests -->
        {% csrf_token %}

        <!-- Filter section -->
        <div class="p-4 mb-4 bg-white rounded-lg shadow-sm">
            <form id="workload-filter-form" class="w-full">
                {% include "setworkload/filter_container.html" %}
            </form>
        </div>

        <!-- Table section -->
        <div class="bg-white rounded-lg shadow-sm">
            {% include "setworkload/table.html" %}
        </div>
    </div>
</div>
{% endblock %}