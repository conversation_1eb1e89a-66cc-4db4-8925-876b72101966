"""Custom authentication backend extending aeroauth."""

from aeroauth.backends import AuthBackend as AeroAuthBackend
from django.contrib.auth import get_user_model


class CustomAuthBackend(AeroAuthBackend):
    """Custom authentication backend with proper user creation."""

    def authenticate(self, request, **kwargs):
        """Override authenticate to ensure user creation."""
        user = super().authenticate(request, **kwargs)
        return user

    def create_user(self, claims):
        """Create a new user from OIDC claims."""
        email = claims.get('email', '')
        
        User = get_user_model()
        existing_user = User.objects.filter(email=email).first()
        if existing_user:
            return self.update_user(existing_user, claims)

        user = User.objects.create_user(
            username=email,
            email=email,
            first_name=claims.get('given_name', ''),
            last_name=claims.get('family_name', '')
        )
        return user

    def update_user(self, user, claims):
        """Update existing user with new claims."""
        user.first_name = claims.get('given_name', '')
        user.last_name = claims.get('family_name', '')
        user.email = claims.get('email', '')
        user.save()
        return user

    def get_userinfo(self, access_token, id_token, payload):
        """Get user info from OIDC."""
        claims = super().get_userinfo(access_token, id_token, payload)
        return claims 