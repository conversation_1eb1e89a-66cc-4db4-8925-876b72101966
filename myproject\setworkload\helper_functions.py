import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import json
from django.core.serializers.json import DjangoJSONEncoder
from myproject.utils import get_bigquery_client
from .constants import WORKLOAD_QUERY_TEMPLATE, MAX_DATE_RANGE

logger = logging.getLogger(__name__)

def format_set_name(name):
    """Convert underscores to spaces in set names."""
    return name.replace('_', ' ')

def validate_date_range(start_date, end_date):
    """Validate that the date range is within allowed limits."""
    date_range = (end_date - start_date).days
    if date_range > MAX_DATE_RANGE:
        return False, f"Date range too large: {date_range} days, max allowed is {MAX_DATE_RANGE} days"
    return True, None

def build_workload_query(start_date, end_date):
    """Build the SQL query for workload data."""
    return WORKLOAD_QUERY_TEMPLATE.format(
        start_date=start_date,
        end_date=end_date
    )

def get_workload_query_results(start_date, end_date, func_name):
    """Execute the workload query and return results."""
    client = get_bigquery_client()
    if client is None:
        logger.error(f"[{func_name}] Failed to initialize BigQuery client")
        return None

    query = build_workload_query(start_date, end_date)

    query_job = client.query(query)
    results = query_job.result()

    return results

def extract_service_codes_and_kpis(row):
    """Extract service codes and KPIs from a query result row."""
    service_codes = list(row.service_codes) if hasattr(row, 'service_codes') and row.service_codes else []
    project_codes = list(row.project_codes) if hasattr(row, 'project_codes') and row.project_codes else []
    service_code_sources = list(row.service_code_sources) if hasattr(row, 'service_code_sources') and row.service_code_sources else []

    # Process expected units data
    expected_units = 0
    service_kpis = []
    unique_service_codes = set()

    if hasattr(row, 'service_kpis') and row.service_kpis:
        try:
            service_kpis = list(row.service_kpis)

            # Extract all unique service codes
            for kpi in service_kpis:
                if isinstance(kpi, dict):
                    if 'code' in kpi and kpi['code']:
                        unique_service_codes.add(kpi['code'])
                elif hasattr(kpi, 'code') and kpi.code:
                    unique_service_codes.add(kpi.code)

            # If there are service codes, use the first one's expected units
            if service_kpis:
                first_kpi = service_kpis[0]
                if isinstance(first_kpi, dict):
                    if 'expected_units' in first_kpi and first_kpi['expected_units'] is not None:
                        expected_units = float(first_kpi['expected_units'])
                elif hasattr(first_kpi, 'expected_units') and first_kpi.expected_units is not None:
                    expected_units = float(first_kpi.expected_units)

        except Exception as e:
            logger.error(f"Error processing service_kpis: {str(e)}")
            expected_units = 0
            service_kpis = []

    return service_codes, project_codes, service_code_sources, expected_units, service_kpis

def format_service_kpis(service_kpis):
    """Format service KPIs for JSON output."""
    formatted_service_kpis = []
    for kpi in service_kpis:
        if isinstance(kpi, dict):
            code = kpi.get('code', '')
            expected = kpi.get('expected_units', 0)
            expected = float(expected) if expected is not None else 0
            formatted_service_kpis.append({'code': code, 'expected_units': expected})
        else:
            code = kpi.code if hasattr(kpi, 'code') else ''
            expected = kpi.expected_units if hasattr(kpi, 'expected_units') else 0
            expected = float(expected) if expected is not None else 0
            formatted_service_kpis.append({'code': code, 'expected_units': expected})
    return formatted_service_kpis

def process_workload_results(results, func_name):
    """Process workload query results into JSON format."""
    json_data = []

    for row in results:
        formatted_set_name = format_set_name(row.set_name)
        direct_hours = round(row.direct_minutes / 60, 1)
        idle_hours = round(row.idle_minutes / 60, 1)
        total_time_span = round(row.minutes_worked / 60, 1)

        # Extract service codes, project codes, and KPIs
        service_codes, project_codes, service_code_sources, expected_units, service_kpis = extract_service_codes_and_kpis(row)

        # Format service KPIs for JSON
        formatted_service_kpis = format_service_kpis(service_kpis)

        json_data.append({
            'set_name': formatted_set_name,
            'supervisor': row.supervisor_name,
            'date': row.date.isoformat(),
            'direct_hours': direct_hours,
            'idle_hours': idle_hours,
            'units': row.units,
            'expected_units': expected_units,
            'service_kpis': formatted_service_kpis,
            'total_time_span': total_time_span,
            'workorder_ids': row.workorder_ids if hasattr(row, 'workorder_ids') else [],
            'service_codes': service_codes,
            'project_codes': project_codes,
            'service_code_sources': service_code_sources,
            'report_url': row.report_url if hasattr(row, 'report_url') else None,
            'fallback_report_url': row.fallback_report_url if hasattr(row, 'fallback_report_url') else None
        })

    return json_data

def process_workload_view_data(results, start_date, end_date, func_name):
    """Process workload data for the main view."""
    dates = []
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date)
        current_date += timedelta(days=1)

    workload_data = defaultdict(dict)
    set_has_nonzero_values = defaultdict(bool)
    json_data = []

    for row in results:
        formatted_set_name = format_set_name(row.set_name)
        direct_hours = round(row.direct_minutes / 60, 1)
        idle_hours = round(row.idle_minutes / 60, 1)
        total_time_span = round(row.minutes_worked / 60, 1)

        # Extract service codes, project codes, and KPIs
        service_codes, project_codes, service_code_sources, expected_units, service_kpis = extract_service_codes_and_kpis(row)

        # Format service KPIs for JSON
        formatted_service_kpis = format_service_kpis(service_kpis)

        # Store data for template rendering
        workload_data[formatted_set_name][row.date] = {
            'direct_hours': direct_hours,
            'idle_hours': idle_hours,
            'units': row.units,
            'expected_units': expected_units,
            'service_kpis': formatted_service_kpis,
            'total_time_span': total_time_span,
            'workorder_ids': row.workorder_ids if hasattr(row, 'workorder_ids') else [],
            'service_codes': service_codes,
            'project_codes': project_codes,
            'service_code_sources': service_code_sources,
            'report_url': row.report_url if hasattr(row, 'report_url') else None,
            'fallback_report_url': row.fallback_report_url if hasattr(row, 'fallback_report_url') else None
        }

        # Store data for JavaScript
        json_data.append({
            'set_name': formatted_set_name,
            'supervisor': row.supervisor_name,
            'date': row.date.isoformat(),
            'direct_hours': direct_hours,
            'idle_hours': idle_hours,
            'units': row.units,
            'expected_units': expected_units,
            'service_kpis': formatted_service_kpis,
            'total_time_span': total_time_span,
            'workorder_ids': row.workorder_ids if hasattr(row, 'workorder_ids') else [],
            'service_codes': service_codes,
            'project_codes': project_codes,
            'service_code_sources': service_code_sources,
            'report_url': row.report_url if hasattr(row, 'report_url') else None,
            'fallback_report_url': row.fallback_report_url if hasattr(row, 'fallback_report_url') else None
        })

        # Check if this row has any non-zero values
        if direct_hours > 0 or idle_hours > 0 or row.units > 0:
            set_has_nonzero_values[formatted_set_name] = True

    # Filter out sets with all zero values
    filtered_workload_data = {
        set_name: data
        for set_name, data in workload_data.items()
        if set_has_nonzero_values[set_name]
    }

    return filtered_workload_data, dates, json_data