/**
 * Metrics view switching functionality
 * Handles switching between weekly and monthly metrics views
 */

export function switchView(view) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.remove('hidden');

    setTimeout(() => {
        const views = {
            weekly: {
                show: document.getElementById('weeklyMetrics'),
                hide: document.getElementById('monthlyMetrics'),
                activeBtn: document.getElementById('weeklyBtn'),
                inactiveBtn: document.getElementById('monthlyBtn')
            },
            monthly: {
                show: document.getElementById('monthlyMetrics'),
                hide: document.getElementById('weeklyMetrics'),
                activeBtn: document.getElementById('monthlyBtn'),
                inactiveBtn: document.getElementById('weeklyBtn')
            }
        };

        const current = views[view];
        if (!current) return;

        // Hide the old view first
        current.hide.classList.add('hidden');

        // Show the new view
        current.show.classList.remove('hidden');

        // Update button styles
        current.activeBtn.classList.add('bg-blue-500', 'text-white');
        current.activeBtn.classList.remove('bg-white', 'text-gray-700', 'hover:bg-gray-50');
        current.activeBtn.querySelector('span').classList.remove('opacity-0');

        current.inactiveBtn.classList.remove('bg-blue-500', 'text-white');
        current.inactiveBtn.classList.add('bg-white', 'text-gray-700', 'hover:bg-gray-50');
        current.inactiveBtn.querySelector('span').classList.add('opacity-0');

        // Hide loading overlay
        loadingOverlay.classList.add('hidden');
    }, 300); // Short delay to show loading state
}

// Initialize event listeners when document is ready
document.addEventListener('DOMContentLoaded', () => {
    // Add click event listeners to the buttons if they exist
    const weeklyBtn = document.getElementById('weeklyBtn');
    const monthlyBtn = document.getElementById('monthlyBtn');
    
    if (weeklyBtn) {
        weeklyBtn.addEventListener('click', () => switchView('weekly'));
    }
    
    if (monthlyBtn) {
        monthlyBtn.addEventListener('click', () => switchView('monthly'));
    }
}); 