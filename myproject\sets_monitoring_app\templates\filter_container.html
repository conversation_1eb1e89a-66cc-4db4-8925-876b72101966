{% load static %}

<div class="grid grid-cols-2 gap-3 w-full lg:grid-cols-4">
  <!-- Region Filter (multi-select) -->
  <div class="flex flex-col w-full">
    <label for="region" class="text-sm font-medium text-gray-700 mt-2 mb-1">Region</label>
    <select name="region" id="region" class="w-full" multiple>
      {% for region in regions %}
      <option value="{{ region }}" {% if region in selected_regions %}selected{% endif %}>{{ region }}</option>
      {% endfor %}
    </select>
  </div>

  <!-- Sets Filter (multi-select) -->
  <div class="flex relative flex-col w-full">
    <label for="sets" class="text-sm font-medium text-gray-700 mt-2 mb-1">Sets</label>
    <select name="sets" id="sets" class="w-full" multiple>
      {% for set_name in all_sets %}
      <option value="{{ set_name }}" {% if set_name in selected_sets %}selected{% endif %}>{{ set_name }}</option>
      {% endfor %}
    </select>
  </div>

  <!-- Service Code Filter (multi-select) -->
  <div class="flex relative flex-col w-full">
    <label for="service-codes" class="text-sm font-medium text-gray-700 mt-2 mb-1">Service Code</label>
    <select name="service-codes" id="service-codes" class="w-full" multiple>
      {% for service_code in all_service_codes %}
      <option value="{{ service_code }}" {% if service_code in selected_service_codes %}selected{% endif %}>{{ service_code }}</option>
      {% endfor %}
    </select>
  </div>

  <!-- Project Filter (multi-select) -->
  <div class="flex relative flex-col w-full">
    <label for="projects" class="text-sm font-medium text-gray-700 mt-2 mb-1">Project</label>
    <select name="projects" id="projects" class="w-full" multiple>
      {% for project in all_projects %}
      <option value="{{ project }}" {% if project in selected_projects %}selected{% endif %}>{{ project }}</option>
      {% endfor %}
    </select>
  </div>
</div>
