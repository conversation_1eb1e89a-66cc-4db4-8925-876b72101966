"""Test authentication redirects."""
from django.test import TestCase, Client
from django.urls import reverse


class AuthRedirectTests(TestCase):
    """Test cases for authentication redirects and OIDC URL accessibility."""

    def setUp(self):
        """Set up test client."""
        self.client = Client()

    def test_unauthenticated_redirect(self):
        """Test that unauthenticated users are redirected to login."""
        # First request to / - should redirect to /authenticate/
        response = self.client.get(reverse('home'))
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response['Location'], '/authenticate/')

        # Follow to /authenticate/ - should redirect to SSO
        response = self.client.get('/authenticate/')
        self.assertEqual(response.status_code, 302)
        self.assertTrue(
            response['Location'].startswith('https://sso.aerones.com/realms/aerones/protocol/openid-connect/auth'),
            "Should redirect to SSO"
        )