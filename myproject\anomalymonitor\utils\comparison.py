import cv2
import numpy as np
import logging
import base64
from PIL import Image, ImageChops
from .debug import log_info, save_debug_image
from .image_processing import align_images, resize_image
from skimage.metrics import structural_similarity as ssim

logger = logging.getLogger(__name__)

def compare_image_pair(img1_np, img2_np, mask_np=None):
    """Compare a pair of images and return differences"""
    log_info("Starting image comparison")
    
    try:
        # Ensure images are the same size before comparison
        height, width = img1_np.shape[:2]
        img2_np = resize_image(img2_np, width, height)
        
        # Normalize images before alignment to ensure consistent contrast
        img1_norm = cv2.normalize(img1_np, None, 0, 255, cv2.NORM_MINMAX)
        img2_norm = cv2.normalize(img2_np, None, 0, 255, cv2.NORM_MINMAX)
        
        # Align images using ORB with enhanced parameters
        aligned_img2 = align_images(img1_norm.copy(), img2_norm.copy())
        save_debug_image(aligned_img2, 'aligned_image')
        
        # Convert images to grayscale after alignment
        img1_gray = cv2.cvtColor(img1_norm, cv2.COLOR_BGR2GRAY)
        aligned_img2_gray = cv2.cvtColor(aligned_img2, cv2.COLOR_BGR2GRAY)
        
        # Apply histogram equalization to ensure consistent contrast
        img1_gray = cv2.equalizeHist(img1_gray)
        aligned_img2_gray = cv2.equalizeHist(aligned_img2_gray)
        
        # Calculate SSIM score
        ssim_score, _ = ssim(img1_gray, aligned_img2_gray, full=True)
        
        # Convert to PIL Images for ImageChops
        img1_pil = Image.fromarray(img1_gray)
        img2_pil = Image.fromarray(aligned_img2_gray)
        
        # Compare images using ImageChops
        diff = ImageChops.difference(img1_pil, img2_pil)
        bbox = diff.getbbox()
        
        # Create visualization
        diff_visualization = img1_np.copy()
        
        differences = []
        
        # Convert difference to numpy array
        diff_array = np.array(diff)
        
        # Parameters that affect difference detection:
        
        # 1. DIFFERENCE_THRESHOLD: Controls how different a pixel must be to be counted
        # - Higher value = less sensitive, larger differences needed
        # - Lower value = more sensitive, smaller differences detected
        DIFFERENCE_THRESHOLD = 15
        
        # Calculate meaningful difference percentage
        significant_diff_pixels = np.sum(diff_array > DIFFERENCE_THRESHOLD)
        total_pixels = img1_gray.shape[0] * img1_gray.shape[1]
        diff_percentage = (significant_diff_pixels / total_pixels) * 100
        
        # Use SSIM score directly as similarity score
        diff_score = (ssim_score + 1) / 2
        
        # 2. GAUSSIAN_BLUR_KERNEL: Controls how differences are grouped
        # - Larger kernel (e.g., 5,5) = more grouping, larger regions
        # - Smaller kernel (e.g., 3,3) = less grouping, smaller regions
        GAUSSIAN_BLUR_KERNEL = (5, 5)
        
        # 3. BINARY_THRESHOLD: Controls how differences are segmented
        # - Higher value = fewer but larger regions
        # - Lower value = more but smaller regions
        BINARY_THRESHOLD = 15
        
        # Apply additional preprocessing to difference array
        diff_array = cv2.normalize(diff_array, None, 0, 255, cv2.NORM_MINMAX)
        
        # Enhance difference array for visualization
        diff_enhanced = cv2.GaussianBlur(diff_array, GAUSSIAN_BLUR_KERNEL, 0)
        _, diff_thresh = cv2.threshold(diff_enhanced, BINARY_THRESHOLD, 255, cv2.THRESH_BINARY)
        
        if bbox:
            # Find contours in difference image
            contours, _ = cv2.findContours(diff_thresh.astype(np.uint8), 
                                         cv2.RETR_EXTERNAL, 
                                         cv2.CHAIN_APPROX_SIMPLE)
            
            logger.debug(f"Found {len(contours)} initial contours")
            
            # Process each contour
            for contour in contours:
                # 4. MIN_CONTOUR_AREA: Minimum size of a difference region
                # - Higher value = fewer but larger regions
                # - Lower value = more but smaller regions
                MIN_CONTOUR_AREA = 30
                
                area = cv2.contourArea(contour)
                if area > MIN_CONTOUR_AREA:
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 5. PADDING: Controls how much the boxes are expanded
                    # - Higher value = larger boxes
                    # - Lower value = tighter boxes
                    PADDING = 1
                    
                    x = max(0, x - PADDING)
                    y = max(0, y - PADDING)
                    w = min(width - x, w + 2 * PADDING)
                    h = min(height - y, h + 2 * PADDING)
                    
                    # Calculate average difference in the region
                    roi = diff_array[y:y+h, x:x+w]
                    mean_diff = np.mean(roi)
                    logger.debug(f"Contour area: {area}, Mean difference: {mean_diff}")
                    
                    # 6. REGION_DIFFERENCE_THRESHOLD: Controls which regions are considered different
                    # - Higher value = fewer but more significant regions
                    # - Lower value = more but less significant regions
                    REGION_DIFFERENCE_THRESHOLD = 10
                    
                    if mean_diff > REGION_DIFFERENCE_THRESHOLD:
                        differences.append({
                            'x': int(x),
                            'y': int(y),
                            'width': int(w),
                            'height': int(h)
                        })
                        
                        # Draw filled contour with semi-transparency
                        overlay = diff_visualization.copy()
                        cv2.drawContours(overlay, [contour], -1, (255, 191, 0), -1)
                        cv2.addWeighted(overlay, 0.2, diff_visualization, 0.8, 0, diff_visualization)
                        
                        # Draw rectangle with thinner border
                        cv2.rectangle(diff_visualization, (x, y), (x + w, y + h), (255, 0, 0), 1)
            
            logger.debug(f"Found {len(differences)} significant differences")
        
        # Scale the difference array to full range for better visualization
        diff_scaled = cv2.normalize(diff_array, None, 0, 255, cv2.NORM_MINMAX)
        # Apply color mapping to all cases
        diff_map = cv2.applyColorMap(diff_scaled.astype(np.uint8), cv2.COLORMAP_JET)
        
        # Convert visualization to base64
        _, diff_vis_encoded = cv2.imencode('.png', diff_visualization)
        diff_vis_base64 = base64.b64encode(diff_vis_encoded).decode('utf-8')
        
        # Convert difference map to base64
        _, diff_encoded = cv2.imencode('.png', diff_map)
        diff_base64 = base64.b64encode(diff_encoded).decode('utf-8')
        
        log_info("Comparison complete", 
                diff_percentage=diff_percentage,
                similarity_score=diff_score,
                num_differences=len(differences))
        
        return differences, diff_percentage, len(differences), diff_score, diff_base64, diff_vis_base64
        
    except Exception as e:
        logger.error(f"Error in image comparison: {str(e)}", exc_info=True)
        raise
