{% extends 'base.html' %}

{% block title %}Real Time Set Status{% endblock %}

{% block extra_head %}
<style>
[x-cloak] { display: none !important; }
</style>

<!-- jQuery 3.6.1 with SRI -->
<script
  src="https://code.jquery.com/jquery-3.6.1.min.js"
  integrity="sha384-i61gTtaoovXtAbKjo903+O55Jkn2+RtzHtvNez+yI49HAASvznhe9sZyjaSHTau9"
  crossorigin="anonymous">
</script>

<!-- Tom Select 2.4.3 with SRI -->
<link
  href="https://cdn.jsdelivr.net/npm/tom-select@2.4.3/dist/css/tom-select.min.css"
  rel="stylesheet"
  integrity="sha384-EIA8J01pb+PXiVYnDYseaCki0ke5k3Z/QNbhj0nU+Uso0kvSYdFnQB3G6ia7lU+Y"
  crossorigin="anonymous" />
<script
  src="https://cdn.jsdelivr.net/npm/tom-select@2.4.3/dist/js/tom-select.complete.min.js"
  integrity="sha384-A4TlP4zHVPP9oVIQ8pTVaV6MhvBkDx1sRpmEUfAlO5e7xpodrdr6uNmUV7alXAWW"
  crossorigin="anonymous">
</script>
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>

{% load static %}
<script src="{% static 'sets_monitoring_app/js/past-status-tooltips.js' %}"></script>
<script src="{% static 'sets_monitoring_app/js/local-time.js' %}"></script>
<script src="{% static 'sets_monitoring_app/js/weather-chart.js' %}"></script>
<script src="{% static 'sets_monitoring_app/js/urlStateManager.js' %}"></script>
<script src="{% static 'sets_monitoring_app/js/setsFilterManager.js' %}"></script>
<script src="{% static 'sets_monitoring_app/js/main.js' %}"></script>
<link href="{% static 'sets_monitoring_app/css/tomselect-theme.css' %}" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="container px-4 pb-20 mx-auto sm:px-6 lg:px-8" data-current-time-utc="{{ current_time_utc }}">
    <div class="flex flex-col items-center gap-8 max-w-6xl mx-auto">
        <!-- Header section -->
        <div class="w-full">
            {% include 'dashboard_header.html' %}
        </div>

        <!-- Filter section -->
        <div class="w-full">
            <div class="p-4 bg-white shadow-sm rounded-lg">
                <form id="sets-filter-form" class="w-full">
                    {% include "filter_container.html" %}
                </form>
            </div>
        </div>
        <!-- Red Status Sets -->
        {% if grouped_sets.red %}
        <div class="w-full">
            <div class="flex flex-col gap-6">
                {% for set in grouped_sets.red %}
                {% with status_color="bg-red-50 border-red-500" %}
                {% include 'set_card.html' %}
                {% endwith %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Orange Status Sets -->
        {% if grouped_sets.orange %}
        <div class="w-full">
            <div class="flex flex-col gap-6">
                {% for set in grouped_sets.orange %}
                {% with status_color="bg-orange-50 border-orange-500" %}
                {% include 'set_card.html' %}
                {% endwith %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Green Status Sets -->
        {% if grouped_sets.green %}
        <div class="w-full">
            <div class="flex flex-col gap-6">
                {% for set in grouped_sets.green %}
                {% with status_color="bg-green-50 border-green-500" %}
                {% include 'set_card.html' %}
                {% endwith %}
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% if not grouped_sets.red and not grouped_sets.orange and not grouped_sets.green %}
        <div class="col-span-full p-6 text-center text-lg">
            <p class="text-gray-500">No active sets found in this region.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}