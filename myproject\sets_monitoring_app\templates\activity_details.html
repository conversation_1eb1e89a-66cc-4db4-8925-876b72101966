<!-- Activity Details Popup -->
<div class="flex items-center">
    <svg class="mr-2 w-4 h-4 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <div class="relative cursor-pointer group">
        <span class="truncate">Last activity: {{ set.activity_time }}
            {{set.timezone_city}} ({{set.time_since_activity}})</span>
        {% if set.activity_details %}
        <div class="hidden group-hover:block absolute left-0 top-full mt-2 p-3 bg-white rounded-lg shadow-lg border border-gray-200 z-[100] w-72"
            style="min-width: 18rem;">
            <div class="space-y-2 text-sm">
                <div class="font-medium text-gray-900">{{ set.activity_details.name }}</div>
                <div class="text-gray-600">Type: {{ set.activity_details.type }}</div>
                <div class="text-gray-600">Start: {{ set.activity_details.start }}</div>
                <div class="text-gray-600">End: {{ set.activity_details.end }}</div>
            </div>
        </div>
        {% endif %}
    </div>
</div>