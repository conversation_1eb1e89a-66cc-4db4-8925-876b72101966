from django.db import models
from django.utils import timezone

class VideoComment(models.Model):
    """
    Model for storing user comments on video activities.
    """
    user_email = models.EmailField(max_length=255, help_text="Email of the user who added the comment")
    workorder_id = models.IntegerField(help_text="ID of the workorder this comment relates to")
    blade = models.CharField(max_length=10, help_text="Blade identifier (A, B, C)")
    activity_type = models.CharField(max_length=50, help_text="Type of activity (Filler, Grinding, Cleaning, LEP)")
    start_datetime = models.DateTimeField(help_text="Start datetime of the activity")
    comment_text = models.TextField(help_text="The actual comment text")
    created_at = models.DateTimeField(default=timezone.now, help_text="When the comment was created")

    class Meta:
        db_table = 'video_comments'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['workorder_id', 'blade', 'activity_type', 'start_datetime']),
            models.Index(fields=['user_email']),
            models.Index(fields=['created_at']),
        ]
        app_label = 'workorders'

    def __str__(self):
        return f"Comment by {self.user_email} on {self.activity_type} (Blade {self.blade}) for workorder {self.workorder_id}"
