/* Add pointer cursor to date inputs */
input[type="date"].form-input {
    cursor: pointer !important;
}

.select2-container--tailwindcss-3.select2-container--open .select2-dropdown {
    overflow-y: hidden !important; /* Fix for double scrollbar in dropdown */
}

.select2-container--tailwindcss-3 .select2-results__option {
    cursor: pointer !important; /* Change cursor to pointer for all dropdown options */
}

.select2-container--tailwindcss-3 .select2-results__option--highlighted {
    background-color: #3B82F6 !important; /* Tailwind blue-500 */
    color: white !important;
}

.select2-container--tailwindcss-3 .select2-results__option--selected:after {
    color: #3B82F6 / var(--tw-text-opacity, 1);
}

.select2-container--tailwindcss-3 .select2-selection--multiple {
    max-height: 2.5rem !important; /* Adjust to match your desired input height (e.g., <PERSON><PERSON><PERSON>'s h-10) */
    overflow-y: hidden !important; /* Hide vertical overflow for the main input box */
    cursor: pointer !important; /* Change cursor to pointer on hover */
    position: relative !important; /* For dropdown arrow positioning */
    display: flex !important;
    align-items: center !important; /* Center content vertically */
    height: 38px !important; /* Fixed height */
}

/* Add dropdown arrow */
.select2-container--tailwindcss-3 .select2-selection--multiple::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2364748b'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    pointer-events: none;
    z-index: 1;
}

/* Rotate arrow when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple::after {
    transform: translateY(-50%) rotate(180deg);
}

.select2-container--tailwindcss-3 .select2-selection--multiple .select2-selection__rendered {
    display: flex !important;
    align-items: center !important; /* Vertically center the items/pills */
    max-width: 100% !important; /* Ensure it doesn't exceed container width */
    padding-right: 30px !important; /* Make room for the dropdown arrow */
}

/* Modified tooltip-related styles - changed to pointer cursor and removed dotted line */
.select2-container--tailwindcss-3 .select2-selection__placeholder[data-tooltip] {
    cursor: pointer !important; /* Show pointer cursor instead of help */
    display: inline-block !important; /* Required for proper display */
}

/* Add gray text color for all placeholder text */
.select2-container--tailwindcss-3 .select2-selection__placeholder {
    color: #6B7280 !important; /* Tailwind gray-500 color */
}

.select2-container--tailwindcss-3.select2-container--focus .select2-selection--multiple {
    border-color: #3B82F6 !important;
}

/* Show search box in the multiple select when dropdown is open */
.select2-container--tailwindcss-3 .select2-selection--multiple .select2-search--inline {
    display: none !important; /* Hidden by default */
}

/* Show search field when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-search--inline {
    display: block !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 10 !important;
}

/* Hide the selection text when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection__placeholder,
.select2-container--tailwindcss-3.select2-container--open .select2-selection__rendered-text,
.select2-container--tailwindcss-3.select2-container--open .select2-selection__rendered span {
    display: none !important;
}

/* Hide the default placeholder when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-selection__rendered .select2-selection__placeholder {
    display: none !important;
}

/* Style the search field */
.select2-container--tailwindcss-3 .select2-search__field {
    width: calc(100% - 30px) !important; /* Account for dropdown arrow */
    padding-left: 8px !important;
    margin-top: 0 !important;
    height: 36px !important; /* Match the height of the select box */
    display: block !important;
    line-height: 32px !important; /* Adjusted line height for better vertical centering */
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    font-size: 14px !important; /* Match the font size of the placeholder text */
}

/* Ensure search field is visible and accessible when dropdown is open */
.select2-container--open .select2-search--inline,
.select2-container--open .select2-search__field {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Force the search field to be visible and properly positioned */
.select2-container--open .select2-search__field {
    position: relative !important;
    z-index: 100 !important;
    margin: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    vertical-align: middle !important;
}

/* Ensure search container is properly positioned when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-search--inline {
    position: absolute !important;
    left: 0 !important;
    top: 50% !important; /* Position at 50% from top */
    transform: translateY(-50%) !important; /* Move up by half its height */
    width: 100% !important;
    height: 36px !important; /* Fixed height */
    z-index: 10 !important;
    display: flex !important;
    align-items: center !important; /* Center vertically */
    padding: 0 !important; /* Remove any padding */
    margin: 0 !important; /* Remove any margin */
}

/* Make sure the search field is visible and properly positioned */
.select2-container--tailwindcss-3.select2-container--open .select2-search__field {
    display: block !important;
    width: calc(100% - 30px) !important;
    padding-left: 8px !important;
    margin: 0 !important;
    height: 36px !important; /* Slightly reduced height */
    line-height: 32px !important; /* Slightly reduced line height for better vertical centering */
    box-sizing: border-box !important;
    position: absolute !important;
    top: 3px !important; /* Position slightly from top - adjusted to move down more */
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}