services:

  app:
    container_name: app
    build:
      context: ./myproject
      dockerfile: Dockerfile-app
    env_file:
      - .env-dev
    ports:
      - 8000:8000
    volumes:
      - ./keys:/app/keys
      - /app/static
      - /app/theme/static
    develop:
      watch:
        - action: sync
          path: ./myproject
          target: /app
    restart: always
    command: >
        /bin/sh -c "
        python manage.py collectstatic --noinput --clear &&
        python manage.py migrate &&
        python manage.py runserver 0.0.0.0:8000"

  tailwind:
    container_name: tailwind
    build:
      context: ./myproject
      dockerfile: Dockerfile-tailwind
    volumes_from:
      - app
    develop:
      watch:
        - action: sync
          path: ./myproject
          target: /app
    tty: true
    restart: always

  nginx:
    container_name: nginx
    ports:
      - 80:80
      - 443:443
    image: nginx:1.27.2-alpine-slim
    volumes_from:
      - app:ro
    volumes:
      - ./nginx/nginx-local.conf:/etc/nginx/templates/default.conf.template:ro
      - ./certs:/etc/letsencrypt/live:ro
    restart: always
    depends_on:
      - tailwind
      - app
