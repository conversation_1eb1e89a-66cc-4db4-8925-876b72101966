/* TomSelect Custom Theme - Performance Tab Style */

/* Style like performance tab filters */
#gps-vehicle-filter + .ts-wrapper .ts-control {
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
    background: white !important;
    padding: 0.5rem 2.5rem 0.5rem 0.75rem !important; /* Extra right padding for arrow */
    min-height: 2.5rem !important;
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
    cursor: pointer !important;
    width: 200px !important;
    position: relative !important;
}

/* Style the input */
#gps-vehicle-filter + .ts-wrapper .ts-control .ts-control-input {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 0.875rem !important;
    color: #374151 !important;
    outline: none !important;
    cursor: pointer !important;
}

/* Placeholder styling */
#gps-vehicle-filter + .ts-wrapper .ts-control .ts-control-input::placeholder {
    color: #6b7280 !important;
    font-size: 0.875rem !important;
}

/* Focus styling like performance filters */
#gps-vehicle-filter + .ts-wrapper .ts-control:focus-within {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 1px #3b82f6 !important;
    outline: none !important;
}

/* Dropdown styling */
#gps-vehicle-filter + .ts-wrapper .ts-dropdown {
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    background: white !important;
    margin-top: 0.25rem !important;
}

/* Dropdown options */
#gps-vehicle-filter + .ts-wrapper .ts-dropdown .option {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
    color: #374151 !important;
    cursor: pointer !important;
}

#gps-vehicle-filter + .ts-wrapper .ts-dropdown .option:hover,
#gps-vehicle-filter + .ts-wrapper .ts-dropdown .option.active {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
}

/* Hide the original select element */
#gps-vehicle-filter {
    display: none !important;
}

/* Custom arrow positioning - no animation */
.gps-filter-container {
    position: relative !important;
    display: inline-block !important;
    width: 200px !important;
    height: 2.5rem !important; /* Fixed height to prevent layout shift */
}

/* Arrow positioned inside the TomSelect control */
.ts-wrapper .gps-filter-arrow {
    position: absolute !important;
    right: 0.75rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    pointer-events: none !important;
    z-index: 100 !important;
    color: #9ca3af !important;
    width: 1rem !important;
    height: 1rem !important;
}

/* Loading state styling to match button */
.gps-loading-state {
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
    background: white !important;
    padding: 0.5rem 0.75rem !important; /* No extra right padding during loading */
    height: 2.5rem !important;
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    color: #6b7280 !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
}

/* TomSelect wrapper positioning */
.gps-filter-container > div:last-child {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
}

/* Remove any TomSelect default arrows */
#gps-vehicle-filter + .ts-wrapper .ts-control::after,
#gps-vehicle-filter + .ts-wrapper .ts-control::before {
    display: none !important;
}

/* Ensure clean integration */
#gps-vehicle-filter + .ts-wrapper {
    display: inline-block !important;
    position: relative !important;
}

/* Make sure our custom arrow is always visible */
.ts-control .gps-filter-arrow {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
