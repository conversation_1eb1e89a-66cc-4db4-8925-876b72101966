<div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 flex-grow {% if metadata %}selected-workorder-detail{% endif %}">
    {% if metadata %}
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900 tracking-wide">Workorder Metadata</h2>
        </div>
        
        <div class="overflow-hidden">
            <table class="w-full border-collapse !border-t-2 !border-b-2 !border-gray-300">
                <tbody class="bg-white divide-y divide-gray-100">
                    {% for key, value in metadata.items %}
                        <tr>
                            <td class="px-6 py-3 align-top font-medium text-gray-700 border-b border-gray-50 {% if key == 'Daily Reports' %}pt-4{% endif %}">
                                {{ key|title }}
                            </td>
                            <td class="px-6 py-3 text-gray-900 border-b border-gray-50 {% if key == 'Daily Reports' %}pt-4{% endif %}">
                                {{ value|safe }}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="flex items-center justify-center h-64">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <p class="text-gray-600 font-medium">No metadata available</p>
                <p class="text-gray-500 text-sm mt-1">Please select a workorder to view details</p>
            </div>
        </div>
    {% endif %}
</div>