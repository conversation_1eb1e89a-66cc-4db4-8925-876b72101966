<!-- Project Details Popup -->
<div class="flex items-center">
    <div class="relative cursor-pointer group">
        <p class="text-sm text-gray-500 truncate">
            <span class="font-semibold">Project: {{ set.project_code }}</span>
            {% if set.service_code %}
            <span class="ml-1 text-gray-400">({{ set.service_code }})</span>
            {% endif %}
        </p>
        {% if set.project_details %}
        <div class="hidden group-hover:block absolute left-0 top-full mt-2 p-3 bg-white rounded-lg shadow-lg border border-gray-200 z-[100] w-72"
            style="min-width: 18rem;">
            <div class="space-y-2 text-sm">
                <div class="text-gray-600">Start: {{ set.project_details.start }}</div>
                <div class="text-gray-600">End: {{ set.project_details.end }}</div>
            </div>
        </div>
        {% endif %}
    </div>
</div> 