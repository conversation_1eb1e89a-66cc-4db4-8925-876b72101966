const TableStructure = {
    /**
     * Updates the table header row (thead) to display the correct date columns
     * based on the provided array of dates.
     * @param {string[]} dates - An array of date strings (YYYY-MM-DD).
     */
    updateTableHeader(dates) {
        const thead = document.querySelector('table thead');
        if (!thead) {
            return;
        }
        const dataRow = thead.lastElementChild;
        const setColumn = dataRow.firstElementChild.cloneNode(true);
        setColumn.className = 'sticky top-0 left-0 z-30 bg-gray-50 border-b border-r px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[200px] min-w-[200px] max-w-[200px] truncate';
        const metricsColumn = dataRow.lastElementChild.cloneNode(true);
        metricsColumn.className = 'sticky top-0 right-0 z-30 bg-gray-50 border-b border-l px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[200px] min-w-[200px]';

        dataRow.innerHTML = '';
        dataRow.appendChild(setColumn);

        // Calculate daily totals for vWTG
        const dailyTotals = {};
        if (typeof workloadStore !== 'undefined' && workloadStore.filteredData) {
            workloadStore.filteredData.forEach(item => {
                if (!dailyTotals[item.date]) {
                    dailyTotals[item.date] = 0;
                }
                dailyTotals[item.date] += (item.units || 0);
            });
        }

        dates.forEach(date => {
            const th = document.createElement('th');
            th.className = 'sticky top-0 z-20 bg-gray-50 border-b border-r px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[115px] min-w-[115px] max-w-[115px] overflow-hidden';
            const dateObj = new Date(date);
            
            // Get day name (short format: Mon, Tue, etc.)
            const dayName = dateObj.toLocaleDateString('en-US', { weekday: 'short' });
            const dateStr = dateObj.toLocaleDateString('en-GB', { day: '2-digit', month: 'short' });
            
            // Get daily vWTG total and format to max 2 decimal places
            const dailyTotal = parseFloat((dailyTotals[date] || 0).toFixed(2));
            
            // Format date for tooltip
            const tooltipDate = dateObj.toLocaleDateString('en-GB', { 
                weekday: 'long', 
                day: 'numeric', 
                month: 'long', 
                year: 'numeric' 
            });
            
            // Combine day name with date and vWTG total on the right
            th.innerHTML = `
                <div class="flex flex-col leading-tight">
                    <span class="text-[10px] font-normal text-gray-400">${dayName}</span>
                    <div class="flex justify-between items-center">
                        <span>${dateStr}</span>
                        <span class="text-[10px] font-medium text-blue-600 cursor-help" 
                              title="Total vWTG completed for all sets on ${tooltipDate}">W: ${dailyTotal}</span>
                    </div>
                </div>
            `;
            
            dataRow.appendChild(th);
        });
        dataRow.appendChild(metricsColumn);
    },

    /**
     * Creates a table row (<tr>) for a given set, including its name,
     * daily activity data, and summary metrics.
     * @param {string} setName - The name of the set.
     * @param {object} data - The data object for the set, containing `days` and `metrics`.
     * @param {string[]} dates - An array of date strings (YYYY-MM-DD) for which to create cells.
     * @param {Object} commentCounts - Comment counts for updating comment icons
     * @returns {HTMLTableRowElement} The created table row element.
     */
    createTableRow(setName, data, dates, commentCounts) {
        const tr = document.createElement('tr');
        tr.className = 'relative';

        // Create set name cell
        const setNameCell = this.createSetNameCell(setName, data);
        tr.appendChild(setNameCell);

        // Create day cells
        dates.forEach(date => {
            const dayCell = this.createDayCell(setName, date, data, commentCounts);
            tr.appendChild(dayCell);
        });

        // Create metrics cell
        const metricsCell = this.createMetricsCell(data.metrics);
        tr.appendChild(metricsCell);

        return tr;
    },

    /**
     * Creates the set name cell with service codes.
     * @param {string} setName - The name of the set
     * @param {object} data - The data object for the set
     * @returns {HTMLTableCellElement} The set name cell
     */
    createSetNameCell(setName, data) {
        const setNameCell = document.createElement('td');
        setNameCell.className = 'sticky left-0 z-20 bg-white border-b border-r px-3 py-2 text-sm font-medium text-gray-900 w-[200px] min-w-[200px] max-w-[200px]';
        
        const setNameDiv = document.createElement('div');
        setNameDiv.className = 'flex flex-col gap-2 items-start sm:flex-row sm:items-center';
        
        const setNameSpan = document.createElement('span');
        setNameSpan.className = 'max-w-full truncate';
        setNameSpan.textContent = setName;

        const startDate = workloadStore.activeFilters.dateRange.start;
        const endDate = workloadStore.activeFilters.dateRange.end;
        const formattedStartDate = startDate ? CellFormatters.formatDate(startDate) : 'N/A';
        const formattedEndDate = endDate ? CellFormatters.formatDate(endDate) : 'N/A';
        const dateRangeText = `${formattedStartDate} - ${formattedEndDate}`;

        const allServiceCodes = new Set();
        Object.keys(data.days || {}).forEach(dateKey => {
            const day = data.days[dateKey];
            if (day && day.service_codes && Array.isArray(day.service_codes)) {
                day.service_codes.forEach((code) => {
                    let source = "unknown";
                    if (day.service_code_sources && Array.isArray(day.service_code_sources)) {
                        source = day.service_code_sources.includes('workorder') ? 'workorder' :
                                 (day.service_code_sources.includes('previous_workorder') ? 'previous_workorder' : 'unknown');
                    }
                    if (source === 'workorder' || source === 'previous_workorder') allServiceCodes.add(code);
                });
            }
        });
        
        const serviceCodesList = Array.from(allServiceCodes);
        const serviceCodesText = serviceCodesList.length > 0 ? `\nService Codes: ${serviceCodesList.join(', ')}` : '';
        setNameSpan.title = `${setName}\nFrom: ${dateRangeText}${serviceCodesText}`;
        setNameDiv.appendChild(setNameSpan);

        if (allServiceCodes.size > 0) {
            const serviceCodesContainer = document.createElement('div');
            serviceCodesContainer.className = 'flex flex-wrap gap-1 max-w-full sm:ml-3';
            serviceCodesList.forEach(code => {
                const codeSpan = document.createElement('span');
                codeSpan.className = 'px-2 py-0.5 text-xs font-medium text-blue-800 whitespace-nowrap bg-blue-100 rounded cursor-default';
                codeSpan.textContent = code;
                serviceCodesContainer.appendChild(codeSpan);
            });
            setNameDiv.appendChild(serviceCodesContainer);
        }
        
        setNameCell.appendChild(setNameDiv);
        return setNameCell;
    },

    /**
     * Creates a day cell with activity data and icons.
     * @param {string} setName - The name of the set
     * @param {string} date - The date for this cell
     * @param {object} data - The data object for the set
     * @param {Object} commentCounts - Comment counts for updating comment icons
     * @returns {HTMLTableCellElement} The day cell
     */
    createDayCell(setName, date, data, commentCounts) {
        const dayData = data.days[date] || {
            direct_hours: 0, idle_hours: 0, units: 0, expected_units: 0,
            total_time_span: 0, workorder_ids: [], project_codes: []
        };

        const td = document.createElement('td');
        const hasDirectHours = dayData.direct_hours > 0;
        const hasUnits = dayData.units > 0;
        const hasActivity = hasDirectHours;
        const hasWorkorders = dayData.workorder_ids && dayData.workorder_ids.length > 0;

        // Set background color based on activity and custom status
        let bgColorClass = '';
        const customStatus = StatusManager.getStatusForCell(setName, date);
        
        if (!hasActivity && !hasUnits) {
            // Check for custom status first
            if (customStatus) {
                bgColorClass = StatusManager.getColorClass(customStatus.status_code);
            } else {
                bgColorClass = 'bg-red-50';
            }
        } else if ((dayData.expected_units > 0 && dayData.units >= dayData.expected_units) || (dayData.expected_units === 0 && hasUnits)) {
            bgColorClass = 'bg-green-50';
        } else {
            bgColorClass = 'bg-yellow-50';
        }

        const cursorClass = hasWorkorders ? 'cursor-pointer hover:shadow-md' : '';
        const transitionClass = 'transition-all duration-150';
        td.className = `border-b border-r px-2 py-2 text-sm whitespace-nowrap relative w-[115px] min-w-[115px] max-w-[115px] overflow-hidden ${bgColorClass} ${cursorClass} ${transitionClass}`;

        // Create tooltip
        const tooltip = this.createTooltip(setName, date, dayData, hasWorkorders);
        td.title = tooltip;

        // Create cell content
        const projectCodes = dayData.project_codes && Array.isArray(dayData.project_codes) ? [...new Set(dayData.project_codes)].filter(Boolean) : [];
        
        td.innerHTML = `
            <div class="relative h-full flex flex-col min-h-[100px]">
                <div class="flex flex-col gap-0.5 mt-0 flex-grow">
                    <span>D: ${dayData.direct_hours}</span>
                    <span>I: ${dayData.idle_hours}</span>
                    <span>W: ${dayData.units}/${CellFormatters.formatDecimal(dayData.expected_units)}</span>
                    <span>TTS: ${dayData.total_time_span}</span>
                    ${projectCodes.length > 0 ? `<div class="mt-1 text-xs text-left text-black">${projectCodes.map(code => `<div class="truncate">${code}</div>`).join('')}</div>` : `<div class="mt-1 text-xs text-left text-black" style="height: 16px;"></div>`}
                </div>
                <div class="icons-top-right-container absolute top-1 right-1 flex flex-col space-y-1 z-0">
                    <!-- Icons will be appended here by JS -->
                </div>
            </div>
        `;

        // Add report icon
        const iconsContainer = td.querySelector('.icons-top-right-container');
        ReportManager.addReportIconToCell(iconsContainer, dayData.report_url, hasActivity, hasUnits, hasWorkorders);

        // Add comment icon
        this.addCommentIconToCell(iconsContainer, setName, date, commentCounts);

        // Add status dropdown for no-activity cells
        if (!hasActivity && !hasUnits) {
            StatusManager.addDropdownButton(td, setName, date);
        }

        // Add data attributes for identification
        td.dataset.setName = setName;
        td.dataset.date = date;

        // Add workorder click handling if needed
        if (hasWorkorders) {
            td.dataset.workorderIds = JSON.stringify(dayData.workorder_ids);
            td.dataset.projectCodes = JSON.stringify(projectCodes);
            
            // Remove any existing click handler first to prevent accumulation
            if (td._clickHandler) {
                td.removeEventListener('click', td._clickHandler);
            }
            
            // Store handler reference for cleanup
            td._clickHandler = TableEventHandlers.handleDayCubeClick.bind(TableEventHandlers);
            td.addEventListener('click', td._clickHandler);
        }

        return td;
    },

    /**
     * Creates tooltip text for a day cell.
     * @param {string} setName - The name of the set
     * @param {string} date - The date
     * @param {object} dayData - The day's data
     * @param {boolean} hasWorkorders - Whether the day has workorders
     * @returns {string} Tooltip text
     */
    createTooltip(setName, date, dayData, hasWorkorders) {
        const projectCodes = dayData.project_codes && Array.isArray(dayData.project_codes) ? [...new Set(dayData.project_codes)].filter(Boolean) : [];
        const serviceCodesWithSources = [];
        const fromPreviousDay = [];
        
        if (dayData.service_codes && Array.isArray(dayData.service_codes)) {
            dayData.service_codes.forEach((code) => {
                let source = "unknown";
                if (dayData.service_code_sources && Array.isArray(dayData.service_code_sources)) {
                    source = dayData.service_code_sources.includes('workorder') ? 'workorder' :
                           (dayData.service_code_sources.includes('previous_workorder') ? 'previous_workorder' : 'unknown');
                }
                if (source === 'workorder') {
                    serviceCodesWithSources.push(code);
                } else if (source === 'previous_workorder') {
                    serviceCodesWithSources.push(code);
                    fromPreviousDay.push(code);
                }
            });
        }

        const formattedDateTooltip = CellFormatters.formatDateForTooltip(new Date(date));
        const hasPreviousDayServiceCodes = fromPreviousDay.length > 0;
        const projectCodesTextTooltip = projectCodes.length > 0 ? `\nProject codes: ${projectCodes.join(', ')}` : '';
        
        let serviceCodesTextTooltip = '';
        if (serviceCodesWithSources.length > 0) {
            if (hasPreviousDayServiceCodes) {
                serviceCodesTextTooltip = `\nService codes: ${serviceCodesWithSources.join(', ')} (from previous workorder)`;
            } else {
                serviceCodesTextTooltip = `\nService codes: ${serviceCodesWithSources.join(', ')}`;
            }
        }

        let reportStatusText = dayData.report_url ? '\nDaily Report: Available (click icon to view)' : '';
        
        if (hasWorkorders) {
            return `Click to view workorders for ${setName} on ${formattedDateTooltip}${serviceCodesTextTooltip}${projectCodesTextTooltip}${reportStatusText}`;
        } else if (hasPreviousDayServiceCodes) {
            return `No workorders for ${setName} on ${formattedDateTooltip}${serviceCodesTextTooltip}${projectCodesTextTooltip}${reportStatusText}`;
        } else if (!dayData.direct_hours && !dayData.units && serviceCodesWithSources.length === 0 && projectCodes.length === 0) {
            return `No activity for ${setName} on ${formattedDateTooltip}`;
        } else {
            return `Click to view workorders for ${setName} on ${formattedDateTooltip}${serviceCodesTextTooltip}${projectCodesTextTooltip}${reportStatusText}`;
        }
    },

    /**
     * Adds a comment icon to a table cell.
     * @param {HTMLElement} container - The container element
     * @param {string} setName - The name of the set
     * @param {string} date - The date
     * @param {Object} commentCounts - Comment counts for updating icons
     */
    addCommentIconToCell(container, setName, date, commentCounts) {
        const commentIconContainer = document.createElement('div');
        commentIconContainer.className = `relative`;
        
        const commentIcon = document.createElement('button');
        commentIcon.className = `flex items-center justify-center w-7 h-7 bg-white hover:bg-gray-50 text-gray-600 border border-gray-300 rounded-full shadow-md transition-all duration-150 hover:scale-110 comment-icon`;
        commentIcon.title = "View/Add Supervisor Comments";
        commentIcon.dataset.setName = setName;
        commentIcon.dataset.date = date;
        commentIcon.onclick = (e) => {
            e.stopPropagation();
            TableEventHandlers.handleCommentIconClick(setName, date);
        };
        commentIcon.innerHTML = `<svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" /></svg>`;
        
        commentIconContainer.appendChild(commentIcon);
        container.appendChild(commentIconContainer);

        // Update comment icon based on comment counts
        const key = `${setName}_${date}`;
        const commentCount = commentCounts[key];
        if (commentCount && commentCount > 0) {
            CommentManager.addCommentBadge(commentIcon, commentCount);
            commentIcon.title = `View/Add Supervisor Comments (${commentCount} comment${commentCount > 1 ? 's' : ''})`;
        }
    },

    /**
     * Creates the metrics cell for a table row.
     * @param {object} metrics - The metrics object
     * @returns {HTMLTableCellElement} The metrics cell
     */
    createMetricsCell(metrics) {
        const metricsData = metrics || { 
            totalVWTG: 0, 
            avgVWTGPerDay: 0, 
            avgHoursPerVWTG: 0,
            percentageExpectedDone: 0
        };
        const metricsCell = document.createElement('td');
        metricsCell.className = 'sticky right-0 z-20 bg-white border-b border-l px-3 py-2 text-sm w-[200px] min-w-[200px]';
        
        metricsCell.innerHTML = `
            <div class="grid grid-cols-2 gap-y-0.5 gap-x-2 text-xs">
                <span class="text-gray-500">Total vWTG:</span>
                <span class="tabular-nums text-right text-gray-900">${CellFormatters.formatDecimal(metricsData.totalVWTG)}</span>
                <span class="text-gray-500">AVG vWTG:</span>
                <span class="tabular-nums text-right text-gray-900">${CellFormatters.formatDecimal(metricsData.avgVWTGPerDay)}</span>
                <span class="text-gray-500">Hours per vWTG:</span>
                <span class="tabular-nums text-right text-gray-900">${CellFormatters.formatDecimal(metricsData.avgHoursPerVWTG)}</span>
                <span class="text-gray-500" title="Actual-to-Potential ratio">ATP ratio:</span>
                <span class="tabular-nums text-right text-gray-900">${CellFormatters.formatDecimal(metricsData.percentageExpectedDone)}%</span>
            </div>
        `;
        return metricsCell;
    },


};