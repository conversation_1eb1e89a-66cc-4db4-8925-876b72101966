"""
Django settings for myproject project.

Generated by 'django-admin startproject' using Django 5.0.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
import os


BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'django-insecure-default-dev-key-change-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

ALLOWED_HOSTS = [
    'datatools.aerones.com',
    'dev.datatools.aerones.com',
    'sso.aerones.com',
    '*************',
    '************'
]

#AUTHENTICATION_BACKENDS = ('aeroauth.backends.AuthBackend',)
AUTHENTICATION_BACKENDS = ('myproject.auth.CustomAuthBackend',)
AEROAUTH_CLIENT_ID = os.environ.get('AEROAUTH_CLIENT_ID', 'demo')
AEROAUTH_CLIENT_SECRET = os.environ.get('AEROAUTH_CLIENT_SECRET', '6JXoBgHKoyfWRJmChMwi7KTZJgCDqkom')
AEROAUTH_CREATE_USER = True
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = os.environ.get('LOGOUT_REDIRECT_URL', 'http://127.0.0.1:8000/')

CORS_ALLOWED_ORIGINS = [f'https://{x}' for x in ALLOWED_HOSTS]

CSRF_TRUSTED_ORIGINS = list(CORS_ALLOWED_ORIGINS)

CSRF_COOKIE_SECURE = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True

CSP_DEFAULT_SRC = ("'self'", "https://www.googletagmanager.com")
CSP_SCRIPT_SRC = ("'self'", "https://www.googletagmanager.com")

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    'workorders.apps.WorkordersConfig',
    'projects.apps.ProjectsConfig',
    'anomalymonitor',
    'setworkload.apps.SetworkloadConfig',
    'fsm_payroll.apps.FsmPayrollConfig',
    "tailwind",
    "corsheaders",
    "theme",
    "aeroauth",
    "sets_monitoring_app",
    "querycache",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "myproject.middleware.AuthenticationMiddleware"
]

ROOT_URLCONF = "myproject.urls"

TAILWIND_APP_NAME = "theme"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        'DIRS': [BASE_DIR / 'templates'],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                'django.template.context_processors.csrf',
                'django.template.context_processors.media',
                'myproject.utils.context_processor',
            ],
        },
    },
]

WSGI_APPLICATION = "myproject.wsgi.application"



# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

# Default to SQLite for Django's internal data
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DATATOOLS_DB_NAME'),
        'USER': os.environ.get('DATATOOLS_USER'),
        'PASSWORD': os.environ.get('DATATOOLS_PASSWORD'),
        'HOST': os.environ.get('DATATOOLS_DB_HOST'),
        'PORT': os.environ.get('DATATOOLS_PORT'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_ROOT = BASE_DIR / 'static'
STATIC_URL = 'static/'

# Static files storage configuration for cache busting (Django 4.2+)
STORAGES = {
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.ManifestStaticFilesStorage",
    }
}

# Static files finders (explicit configuration for clarity)
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Media files (User uploaded files)
MEDIA_ROOT = BASE_DIR / 'media'
MEDIA_URL = '/media/'

# File upload settings
FILE_UPLOAD_PERMISSIONS = 0o644
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB


# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Tomorrow.io API configuration for weather forecasts
TOMORROW_IO_API_KEY = os.environ.get('TOMORROW_IO_API_KEY', '')
TOMORROW_IO_BASE_URL = 'https://api.tomorrow.io/v4'

# Meteomatics API configuration for weather history
METEOMATICS_USERNAME = os.environ.get('METEOMATICS_USERNAME', '')
METEOMATICS_PASSWORD = os.environ.get('METEOMATICS_PASSWORD', '')
METEOMATICS_BASE_URL = 'https://api.meteomatics.com'

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'myproject.auth': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
