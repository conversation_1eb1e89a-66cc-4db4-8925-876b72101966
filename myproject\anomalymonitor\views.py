from django.views.generic import TemplateView
from django.http import JsonResponse
from django.views.decorators.csrf import ensure_csrf_cookie
from django.utils.decorators import method_decorator
import logging
from .utils.image_processing import load_image_from_file, validate_image_dimensions, resize_image
from .utils.comparison import compare_image_pair

logger = logging.getLogger(__name__)

@method_decorator(ensure_csrf_cookie, name='dispatch')
class AnomalyMonitorView(TemplateView):
    template_name = 'anomalymonitor/index.html'

@method_decorator(ensure_csrf_cookie, name='dispatch')
class CompareImagesSetView(TemplateView):
    def post(self, request, *args, **kwargs):
        try:
            logger.debug("Starting image comparison request")
            
            # Validate input
            required_images = ['original_image', 'yesterday_image', 'today_image']
            for img_key in required_images:
                if img_key not in request.FILES:
                    return JsonResponse({
                        'success': False,
                        'error': f'Missing required image: {img_key}'
                    }, status=400)
                
                if not request.FILES[img_key].content_type.startswith('image/'):
                    return JsonResponse({
                        'success': False,
                        'error': f'Invalid file type for {img_key}. Must be an image.'
                    }, status=400)

            # Load and process images
            images = {}
            try:
                for img_key in required_images:
                    images[img_key] = load_image_from_file(request.FILES[img_key])
                    if images[img_key] is None:
                        return JsonResponse({
                            'success': False,
                            'error': f'Could not load image: {img_key}'
                        }, status=400)
            except Exception as e:
                logger.error(f"Error loading images: {str(e)}", exc_info=True)
                return JsonResponse({
                    'success': False,
                    'error': f'Error loading images: {str(e)}'
                }, status=400)

            # Get target dimensions
            try:
                target_shape = validate_image_dimensions(images)
                logger.debug(f"Target dimensions determined: {target_shape}")
                
                # Resize images to target dimensions if needed
                for key in images:
                    if images[key].shape[:2] != target_shape:
                        images[key] = resize_image(images[key], target_shape[1], target_shape[0])
                
            except Exception as e:
                logger.error(f"Error processing image dimensions: {str(e)}", exc_info=True)
                return JsonResponse({
                    'success': False,
                    'error': str(e)
                }, status=400)

            # Compare Original vs Today
            try:
                logger.debug("Comparing Original vs Today")
                (original_today_differences, original_today_percentage, original_today_num,
                 original_today_score, original_today_diff, original_today_mask) = compare_image_pair(
                    images['original_image'],
                    images['today_image']
                )

                # Compare Yesterday vs Today
                logger.debug("Comparing Yesterday vs Today")
                (yesterday_today_differences, yesterday_today_percentage, yesterday_today_num,
                 yesterday_today_score, yesterday_today_diff, yesterday_today_mask) = compare_image_pair(
                    images['yesterday_image'],
                    images['today_image']
                )

                response_data = {
                    'success': True,
                    'original_today_differences': original_today_differences,
                    'original_today_percentage': original_today_percentage,
                    'original_today_num_differences': original_today_num,
                    'original_today_score': original_today_score,
                    'original_today_diff': original_today_diff,
                    'original_today_mask': original_today_mask,
                    'yesterday_today_differences': yesterday_today_differences,
                    'yesterday_today_percentage': yesterday_today_percentage,
                    'yesterday_today_num_differences': yesterday_today_num,
                    'yesterday_today_score': yesterday_today_score,
                    'yesterday_today_diff': yesterday_today_diff,
                    'yesterday_today_mask': yesterday_today_mask
                }
                
                return JsonResponse(response_data)
                
            except Exception as e:
                logger.error(f"Error in comparison: {str(e)}", exc_info=True)
                return JsonResponse({
                    'success': False,
                    'error': f'Error comparing images: {str(e)}'
                }, status=500)
            
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}", exc_info=True)
            return JsonResponse({
                'success': False,
                'error': 'An unexpected error occurred'
            }, status=500)
