{% load static %}

<div id="workorder-summary-container" class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 h-full flex flex-col">
    <!-- Loading State -->
    <div id="summary-loading" class="hidden text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading summary data...</p>
    </div>

    <!-- Error State -->
    <div id="summary-error" class="hidden text-center py-8">
        <p class="text-red-600">Error loading summary data. Please try again.</p>
    </div>

    <!-- Summary Content -->
    <div id="summary-content" class="hidden flex-grow flex flex-col">
        <!-- Workorders Table -->
        <div class="flex-grow overflow-hidden">
            <div class="overflow-x-auto rounded-lg border border-gray-200">
                <table class="min-w-full divide-y divide-gray-200 text-sm min-w-[900px]">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                                Direct Hours
                            </th>
                            <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                                Delay Hours
                            </th>
                            <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[90px]">
                                Blade A
                            </th>
                            <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[90px]">
                                Blade B
                            </th>
                            <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[90px]">
                                Blade C
                            </th>
                            <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                                Common
                            </th>
                            <th scope="col" class="sticky right-0 z-20 bg-gray-50 border-l border-gray-200 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[220px] sm:w-[350px] min-w-[220px] sm:min-w-[350px]">
                                Metrics
                            </th>
                        </tr>
                    </thead>
                    <tbody id="summary-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Empty State -->
    <div id="summary-empty" class="text-center py-8">
        <p class="text-gray-600">No workorders found matching the current filters.</p>
    </div>
</div>

<script>
// Define the URL for the workorder summary data endpoint
var workorderSummaryDataUrl = '{% url "workorder_summary_data" %}';
</script>
<script src="{% static 'workorders/js/workorder-summary.js' %}"></script> 