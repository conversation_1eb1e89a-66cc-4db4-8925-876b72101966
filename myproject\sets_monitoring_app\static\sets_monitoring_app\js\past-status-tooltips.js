// Create tooltip element
const tooltip = document.createElement('div');
tooltip.className = 'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded pointer-events-none';
tooltip.style.display = 'none';
document.body.appendChild(tooltip);

// Track current target for touch events
let currentTarget = null;
let popperInstance = null;

function showTooltip(target, message) {
    tooltip.textContent = message;
    tooltip.style.display = 'block';

    // Destroy previous instance if exists
    if (popperInstance) {
        popperInstance.destroy();
    }

    // Create new popper instance
    popperInstance = Popper.createPopper(target, tooltip, {
        placement: 'top',
        modifiers: [
            {
                name: 'offset',
                options: {
                    offset: [0, 8],
                },
            },
            {
                name: 'flip',
                options: {
                    fallbackPlacements: ['bottom', 'right', 'left'],
                },
            },
            {
                name: 'preventOverflow',
                options: {
                    boundary: document.body,
                },
            },
        ],
    });
}

function hideTooltip() {
    tooltip.style.display = 'none';
    currentTarget = null;
    if (popperInstance) {
        popperInstance.destroy();
        popperInstance = null;
    }
}

// Initialize tooltips when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const statusCubes = document.querySelectorAll('.status-cube');
    
    statusCubes.forEach(cube => {
        // Desktop events
        cube.addEventListener('mouseenter', () => {
            showTooltip(cube, cube.getAttribute('data-status'));
        });
        
        cube.addEventListener('mouseleave', hideTooltip);
        
        // Mobile events
        cube.addEventListener('touchstart', (e) => {
            e.preventDefault();
            
            // Hide if clicking same cube
            if (currentTarget === cube) {
                hideTooltip();
                return;
            }
            
            // Hide previous if exists
            if (currentTarget) {
                hideTooltip();
            }
            
            // Show new tooltip
            currentTarget = cube;
            showTooltip(cube, cube.getAttribute('data-status'));
        });
    });
    
    // Hide when touching outside
    document.addEventListener('touchstart', (e) => {
        if (!e.target.closest('.status-cube')) {
            hideTooltip();
        }
    });
}); 