<!-- Unified Activity Performance Component -->
{% if performance_summary %}
<div>
    <!-- Performance Summary Section -->
    <div class="mb-6">
        <!-- Performance Overview -->
        <div class="flex items-center gap-4 mb-4">
            <!-- Segmented Progress Bar -->
            <div class="flex-1">
                <div class="flex items-center justify-end text-xs text-gray-600 mb-1">
                    <span id="onTimePercentage">{{ performance_summary.on_time_percentage }}% On Time</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4 flex overflow-hidden">
                    <!-- On Time Segment -->
                    {% if performance_summary.on_time_count > 0 %}
                    <div class="bg-green-500 h-4 cursor-help transition-all duration-300 hover:bg-green-600"
                         style="width: {{ performance_summary.on_time_percentage }}%"
                         title="On Time: {{ performance_summary.on_time_count }} activities ({{ performance_summary.on_time_percentage }}%)">
                    </div>
                    {% endif %}

                    <!-- Over Time Segment -->
                    {% if performance_summary.over_time_count > 0 %}
                    <div class="bg-red-500 h-4 cursor-help transition-all duration-300 hover:bg-red-600"
                         style="width: {{ performance_summary.over_time_percentage }}%"
                         title="Over Time: {{ performance_summary.over_time_count }} activities ({{ performance_summary.over_time_percentage }}%)">
                    </div>
                    {% endif %}

                    <!-- No Limit Segment -->
                    {% if performance_summary.no_limit_count > 0 %}
                    <div class="bg-gray-400 h-4 cursor-help transition-all duration-300 hover:bg-gray-500"
                         style="width: {{ performance_summary.no_limit_percentage }}%"
                         title="No Limit: {{ performance_summary.no_limit_count }} activities ({{ performance_summary.no_limit_percentage }}%)">
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Details Table -->
    {% if performance_data %}
    <div class="mt-4 pt-4 border-t border-gray-200">
        <!-- Filters and Count -->
        <div class="flex flex-col gap-3 mb-3">
            <!-- Large Screen Layout (2xl and up) -->
            <div class="hidden 2xl:flex 2xl:items-center 2xl:justify-between gap-3">
                <!-- Compact Filters -->
                <div class="flex items-center gap-3">
                    <!-- Status Filter -->
                    <select id="statusFilter" class="min-w-[160px] px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 cursor-pointer">
                        <option value="all" selected>All Status</option>
                        <option value="over">Over Time</option>
                        <option value="on_time">On Time</option>
                        <option value="no_limit">No Limit</option>
                        <option value="exclude_no_limit">Exclude No Limit</option>
                    </select>

                    <!-- Blade Filter -->
                    <select id="bladeFilter" class="min-w-[120px] px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 cursor-pointer">
                        <option value="all">All Blades</option>
                        <option value="A">Blade A</option>
                        <option value="B">Blade B</option>
                        <option value="C">Blade C</option>
                        <option value="Common">Common</option>
                    </select>

                    <!-- Sort By Filter -->
                    <select id="sortByFilter" class="min-w-[180px] px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 cursor-pointer">
                        <option value="variance_desc" selected>Variance Descending</option>
                        <option value="variance_asc">Variance Ascending</option>
                        <option value="time">Time (Start)</option>
                    </select>

                    <!-- Hide Delay Activities Checkbox -->
                    <label class="flex items-center gap-2 text-sm text-gray-700 cursor-pointer whitespace-nowrap">
                        <input type="checkbox" id="hideDelayActivities" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer">
                        <span>Hide Delay Activities</span>
                    </label>
                </div>

                <!-- Activity Count -->
                <div class="text-sm text-gray-500">
                    <span id="visibleCount">{{ performance_data|length }}</span> of {{ performance_data|length }} activities
                </div>
            </div>

            <!-- Small Screen Layout (below 2xl) -->
            <div class="2xl:hidden">
                <!-- Filter Buttons Row - Full Width -->
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 mb-3">
                    <!-- Status Filter -->
                    <select id="statusFilter" class="w-full px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 cursor-pointer">
                        <option value="all" selected>All Status</option>
                        <option value="over">Over Time</option>
                        <option value="on_time">On Time</option>
                        <option value="no_limit">No Limit</option>
                        <option value="exclude_no_limit">Exclude No Limit</option>
                    </select>

                    <!-- Blade Filter -->
                    <select id="bladeFilter" class="w-full px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 cursor-pointer">
                        <option value="all">All Blades</option>
                        <option value="A">Blade A</option>
                        <option value="B">Blade B</option>
                        <option value="C">Blade C</option>
                        <option value="Common">Common</option>
                    </select>

                    <!-- Sort By Filter -->
                    <select id="sortByFilter" class="w-full px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 cursor-pointer">
                        <option value="variance_desc" selected>Variance Descending</option>
                        <option value="variance_asc">Variance Ascending</option>
                        <option value="time">Time (Start)</option>
                    </select>
                </div>

                <!-- Checkbox and Count Row -->
                <div class="flex items-center justify-between">
                    <!-- Hide Delay Activities Checkbox - Left Side -->
                    <label class="flex items-center gap-2 text-sm text-gray-700 cursor-pointer">
                        <input type="checkbox" id="hideDelayActivities" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer">
                        <span>Hide Delay Activities</span>
                    </label>

                    <!-- Activity Count - Right Side -->
                    <div class="text-sm text-gray-500">
                        <span id="visibleCount">{{ performance_data|length }}</span> of {{ performance_data|length }} activities
                    </div>
                </div>
            </div>
        </div>

        <!-- Compact Table Container -->
        <div class="overflow-x-auto rounded-lg border border-gray-200">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Activity
                        </th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Blade
                        </th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Time
                        </th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Variance
                        </th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                            Comment
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="performanceTableBody">
                    {% for activity in performance_data %}
                    <tr class="hover:bg-gray-50 performance-row transition-colors duration-150"
                        data-status="{{ activity.status }}"
                        data-variance="{{ activity.variance_min }}"
                        data-blade="{{ activity.blade_key }}"
                        data-actual-duration="{{ activity.actual_duration_min }}"
                        data-expected-duration="{{ activity.expected_duration_min }}"
                        data-start-datetime="{{ activity.start_datetime|date:'c' }}"
                        data-over-time="{{ activity.is_over_time|yesno:'true,false' }}"
                        data-activity-type="{{ activity.activity_type }}">

                        <!-- Activity Name -->
                        <td class="px-3 py-3 text-sm">
                            <div class="font-medium text-gray-900 truncate max-w-xs cursor-help"
                                 title="{{ activity.activity_name }}&#10;Start: {{ activity.start_datetime|date:'M d, Y H:i' }}&#10;End: {{ activity.end_datetime|date:'M d, Y H:i' }}">
                                {{ activity.activity_name }}
                            </div>
                            <div class="text-xs text-gray-500 mt-0.5">
                                {{ activity.start_datetime|date:'M d, H:i' }} - {{ activity.end_datetime|date:'H:i' }}
                            </div>
                        </td>

                        <!-- Blade -->
                        <td class="px-3 py-3 text-sm">
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                {{ activity.blade_key }}
                            </span>
                        </td>

                        <!-- Combined Time -->
                        <td class="px-3 py-3 text-sm font-mono whitespace-nowrap">
                            <div class="flex items-center gap-1">
                                <span class="text-gray-900 font-semibold cursor-help" title="Actual Time">{{ activity.actual_duration_formatted }}</span>
                                <span class="text-gray-400">/</span>
                                {% if activity.expected_duration_formatted != 'N/A' %}
                                <div class="relative inline-flex items-center cursor-help" title="Expected Time&#10;Data is from activity types, not from activities themselves">
                                    <span class="text-gray-500 text-xs">{{ activity.expected_duration_formatted }}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-blue-500 ml-0.5 mb-3">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                                    </svg>
                                </div>
                                {% else %}
                                <span class="text-gray-500 text-xs cursor-help" title="Expected time is not defined for this activity type">N/A</span>
                                {% endif %}
                            </div>
                        </td>

                        <!-- Variance -->
                        <td class="px-3 py-3 text-sm whitespace-nowrap">
                            {% if activity.variance_formatted != 'N/A' %}
                                {% if activity.actual_duration_min == 0 %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 cursor-help" title="No time spent on this activity">
                                        {{ activity.variance_formatted }}
                                    </span>
                                {% elif activity.variance_min > 0 %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 cursor-help" title="Took {{ activity.variance_formatted|slice:'1:' }} longer than expected">
                                        +{{ activity.variance_formatted|slice:"1:" }}
                                    </span>
                                {% elif activity.variance_min < 0 %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 cursor-help" title="Completed {{ activity.variance_formatted|slice:'1:' }} faster than expected">
                                        {{ activity.variance_formatted }}
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 cursor-help" title="Completed exactly on time">
                                        {{ activity.variance_formatted }}
                                    </span>
                                {% endif %}
                            {% else %}
                                <span class="text-gray-400 text-xs">N/A</span>
                            {% endif %}
                        </td>

                        <!-- Status -->
                        <td class="px-3 py-3 text-sm whitespace-nowrap">
                            {% if activity.status == 'over' %}
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 cursor-help" title="Exceeded expected time">
                                    Over
                                </span>
                            {% elif activity.status == 'on_time' %}
                                {% if activity.actual_duration_min == 0 %}
                                    <!-- No status displayed for zero duration activities -->
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 cursor-help" title="Completed within expected time">
                                        On Time
                                    </span>
                                {% endif %}
                            {% else %}
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 cursor-help" title="Expected time is not defined for this activity type">
                                    No Limit
                                </span>
                            {% endif %}
                        </td>

                        <!-- Comment -->
                        <td class="px-3 py-3 text-sm text-gray-700 hidden lg:table-cell">
                            {% if activity.status == 'over' %}
                                {% if activity.overrun_reason and activity.overrun_reason != '-' and activity.overrun_reason != 'No reason provided' and activity.overrun_reason != 'N/A' %}
                                    <!-- Show delay comment if it exists -->
                                    <div class="max-w-xs">
                                        <span class="block text-xs break-words">
                                            {{ activity.overrun_reason }}
                                        </span>
                                    </div>
                                {% elif activity.properties_formatted %}
                                    <!-- Show properties if no delay comment but properties exist -->
                                    <div class="max-w-xs">
                                        <span class="text-xs whitespace-pre-line break-words">
                                            {{ activity.properties_formatted }}
                                        </span>
                                    </div>
                                {% else %}
                                    <!-- Show N/A with tooltip if neither delay comment nor properties exist -->
                                    <span class="text-gray-400 text-xs cursor-help" title="No delay comment or activity properties available">N/A</span>
                                {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Empty State -->
        <div id="emptyState" class="text-center py-12 text-gray-500 hidden">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <p class="text-lg font-medium">No activities match the current filters</p>
            <p class="text-sm">Try adjusting your filter criteria</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- Load Activity Performance JavaScript -->
{% load static %}
<script src="{% static 'workorders/js/activity_performance.js' %}"></script>
{% endif %}
