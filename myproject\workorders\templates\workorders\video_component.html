{% load static %}

<!-- Video Player Section -->
{% if workorder_id == 34320 %}
<div id="video-player-section" class="p-6 bg-white rounded-lg shadow-lg" style="display: none;">
    <!-- Activity Details and Comments Button Container -->
    <div class="flex justify-between items-start mb-4 gap-4">
        <!-- Activity Details -->
        <div class="p-3 bg-gray-50 rounded-lg border-l-4 border-blue-500 flex-1">
            <div class="flex flex-wrap gap-y-2 gap-x-6 items-center text-sm">
                <div class="flex gap-2 items-center">
                    <span class="font-medium text-gray-600">Blade:</span>
                    <span id="video-blade" class="font-semibold text-gray-900">-</span>
                </div>
                <div class="flex gap-2 items-center">
                    <span class="font-medium text-gray-600">Activity:</span>
                    <span id="video-activity" class="font-semibold text-gray-900">-</span>
                </div>
                <div class="flex gap-2 items-center">
                    <span class="font-medium text-gray-600">Start:</span>
                    <span id="video-start" class="font-semibold text-gray-900">-</span>
                </div>
                <div class="flex gap-2 items-center">
                    <span class="font-medium text-gray-600">End:</span>
                    <span id="video-end" class="font-semibold text-gray-900">-</span>
                </div>
            </div>
        </div>
        <!-- Comments Button Container -->
        <div id="video-comments-button-container" class="flex-shrink-0">
            <!-- Comments button will be added here by JavaScript -->
        </div>
    </div>

    <!-- Video Players (Full Width) -->
    <div class="mb-4">
        <!-- Main Video (Large, Top) -->
        <div class="relative mb-4">
            <div id="main-video-label"
                class="absolute top-2 left-2 z-10 px-3 py-1 text-sm font-medium text-white bg-black bg-opacity-75 rounded-md">
                Main View
            </div>
            <video id="main-video" class="mx-auto w-full max-w-4xl h-auto bg-black rounded-lg shadow-md"
                preload="metadata" muted>
                <source id="main-video-source" src="" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <!-- Loading Overlay for Main Video -->
            <div id="main-video-loading"
                class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 rounded-lg">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
            <!-- Paused Overlay for Main Video -->
            <div id="main-video-overlay"
                class="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-60 rounded-lg transition-opacity duration-200"
                style="display: none;">
                <div class="text-center">
                    <div class="text-3xl font-bold text-white mb-2">Paused</div>
                    <div class="text-sm text-gray-300">Click play button to resume</div>
                </div>
            </div>
        </div>

        <!-- Secondary Videos (Bottom, Side by Side) -->
        <div class="flex flex-col gap-4 sm:flex-row">
            <div class="relative w-full sm:w-1/2">
                <div class="relative bg-black rounded-lg shadow-md overflow-hidden" style="aspect-ratio: 16/9;">
                    <div id="secondary-video-1-label"
                        class="absolute top-2 left-2 z-10 px-3 py-1 text-sm font-medium text-white bg-black bg-opacity-75 rounded-md">
                        Secondary View 1
                    </div>
                    <video id="secondary-video-1" class="w-full h-full object-cover"
                        preload="metadata" muted>
                        <source id="secondary-video-1-source" src="" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <!-- Loading Overlay for Secondary Video 1 -->
                    <div id="secondary-video-1-loading"
                        class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                    </div>
                    <!-- Paused Overlay for Secondary Video 1 -->
                    <div id="secondary-video-1-overlay"
                        class="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-60 transition-opacity duration-200"
                        style="display: none;">
                        <div class="text-center">
                            <div class="text-xl font-bold text-white mb-1">Paused</div>
                            <div class="text-xs text-gray-300">Click play button to resume</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="relative w-full sm:w-1/2">
                <div class="relative bg-black rounded-lg shadow-md overflow-hidden" style="aspect-ratio: 16/9;">
                    <div id="secondary-video-2-label"
                        class="absolute top-2 left-2 z-10 px-3 py-1 text-sm font-medium text-white bg-black bg-opacity-75 rounded-md">
                        Secondary View 2
                    </div>
                    <video id="secondary-video-2" class="w-full h-full object-cover"
                        preload="metadata" muted>
                        <source id="secondary-video-2-source" src="" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <!-- Loading Overlay for Secondary Video 2 -->
                    <div id="secondary-video-2-loading"
                        class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                    </div>
                    <!-- Paused Overlay for Secondary Video 2 -->
                    <div id="secondary-video-2-overlay"
                        class="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-60 transition-opacity duration-200"
                        style="display: none;">
                        <div class="text-center">
                            <div class="text-xl font-bold text-white mb-1">Paused</div>
                            <div class="text-xs text-gray-300">Click play button to resume</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Timeline Controls -->
    <div class="p-4 bg-gray-50 rounded-lg border-t">
        <div class="flex gap-4 items-center">
            <!-- Play/Pause Button -->
            <button id="play-pause-btn" onclick="togglePlayPause()"
                class="flex-shrink-0 flex gap-1 items-center px-3 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg transition-colors hover:bg-blue-600">
                <svg id="play-icon" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                        d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z" />
                </svg>
                <svg id="pause-icon" class="hidden w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                        d="M5.75 3a.75.75 0 00-.75.75v12.5c0 .414.336.75.75.75h1.5a.75.75 0 00.75-.75V3.75A.75.75 0 007.25 3h-1.5zM12.75 3a.75.75 0 00-.75.75v12.5c0 .414.336.75.75.75h1.5a.75.75 0 00.75-.75V3.75a.75.75 0 00-.75-.75h-1.5z" />
                </svg>
            </button>

            <!-- Current Time -->
            <span id="current-time" class="flex-shrink-0 text-sm font-medium text-gray-700 min-w-[3rem]">0:00</span>

            <!-- Timeline Progress Bar -->
            <div class="flex-grow relative h-2 bg-gray-300 rounded-full overflow-hidden">
                <div id="timeline-progress"
                    class="absolute top-0 left-0 h-full bg-blue-500 rounded-full transition-all duration-100 ease-linear"
                    style="width: 0%"></div>
            </div>

            <!-- Total Time -->
            <span id="total-time" class="flex-shrink-0 text-sm font-medium text-gray-700 min-w-[3rem]">0:00</span>
        </div>
    </div>
</div>
{% endif %}