from django.contrib import admin
from .models import WorkloadComment, WorkloadStatusType, WorkloadStatusChange

@admin.register(WorkloadComment)
class WorkloadCommentAdmin(admin.ModelAdmin):
    list_display = ['user_email', 'set_name', 'date', 'comment_text', 'created_at']
    list_filter = ['date', 'user_email', 'created_at']
    search_fields = ['set_name', 'user_email', 'comment_text']
    ordering = ['-created_at']

@admin.register(WorkloadStatusType)
class WorkloadStatusTypeAdmin(admin.ModelAdmin):
    list_display = ['status_code', 'status_name', 'color_class', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['status_code', 'status_name', 'description']
    ordering = ['status_name']

@admin.register(WorkloadStatusChange)
class WorkloadStatusChangeAdmin(admin.ModelAdmin):
    list_display = ['set_name', 'date', 'status_code', 'user_email', 'changed_at']
    list_filter = ['status_code', 'date', 'user_email', 'changed_at']
    search_fields = ['set_name', 'user_email']  # Removed 'notes' field that doesn't exist
    ordering = ['-changed_at']
