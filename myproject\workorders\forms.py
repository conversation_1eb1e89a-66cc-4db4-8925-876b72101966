# workorders/forms.py
from django import forms
from .models import WorkOrder
from datetime import datetime, timedelta

class WorkOrderForm(forms.Form):
    three_months_ago = datetime.now() - timedelta(days=90)
    workorder = forms.ModelChoiceField(
        queryset=WorkOrder.objects.filter(official_completion_date__gte=three_months_ago), 
        label="Select WorkOrder",
        to_field_name="id"
    )
