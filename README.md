
### Running local development environment

Local development environment supports HTTPS through nginx, but there are few things to keep in mind:

- Django plugin `django-browser-reload` doesn't work using nginx right now.

    There are 3 workarounds to this issue:

    - Hit refresh manually (if you use https://localhost)

    - Connect to django container directly: http://localhost:8000
        
        This, of course, will bypass HTTPS. But you will still have working HTTPS and you can validate application's HTTPS support.

    - Not use nginx at all: you just have to comment out nginx configuration in compose file

    **NOTE: Django built-in hot reloading does work! This is only about this plugin specifically!**

- If you choose to keep nginx, generate SSL self-signed certificate.

    **NOTE: For Windows users this has to be done in WSL.**
    > wsl -d Ubuntu

    Generate self-signed certificates
    > ./selfsigned_certs.sh


#### How to run development environment:

> docker compose up --watch --build

This will start containers (rebuild if needed) and watch source tree files for modifications for hot-reloading.


### Gitlab CI builds and deployment

Right now there are 2 deployment instances:

- https://datatools.aerones.com

    `Production` environment.

    Every commit to `main` branch will trigger new container build and deployment to this instance

- https://dev.datatools.aerones.com

    `Development` environment.

    Every commit to `dev` branch will trigger new container build and deployment to this instance

#### Deployment notes
There is a crontab entry for each machine/environment:
```
*/30 * * * * curl -X POST https://dev.datatools.aerones.com/refresh-cache/
```
and respectively for production:
```
*/30 * * * * curl -X POST https://datatools.aerones.com/refresh-cache/
```
This is needed so that caches are updated.
