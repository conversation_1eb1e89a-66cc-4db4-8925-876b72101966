{% load static %}

<script>
  // Define the URL for the workorder data endpoint
  var workorderDataUrl = '{% url "workorder_data" %}';
</script>
<script src="{% static 'workorders/js/workorder-utils.js' %}"></script>
<script src="{% static 'workorders/js/workorder-pagination.js' %}"></script>
<script src="{% static 'workorders/js/workorder-auto-open.js' %}"></script>
<script src="{% static 'workorders/js/workorder-reset.js' %}"></script>
<script src="{% static 'workorders/js/select2-customization.js' %}"></script>
<script src="{% static 'workorders/js/workorder-list.js' %}"></script>



<div class="flex flex-col p-4 bg-white rounded-xl shadow-sm border border-gray-100 w-full max-w-full h-full">
  <div class="flex flex-col w-full max-w-full">
    <div class="overflow-x-auto rounded-lg border border-gray-200">
      <table id="workorders-table" class="min-w-full divide-y divide-gray-200 text-sm">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-1/3">
              Name
            </th>
            <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-1/3">
              Set / Service
            </th>
            <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap w-1/3">
              Start / End
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <!-- Table rows will be populated by JavaScript -->
        </tbody>
      </table>
    </div>
    <!-- Pagination will be inserted here by JavaScript -->
  </div>
</div>