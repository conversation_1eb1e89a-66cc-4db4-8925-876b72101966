/**
 * Utility functions for the workorders app
 */
var WorkorderUtils = (function() {
  // Update a URL parameter
  function updateURLParameter(url, param, value) {
    var regex = new RegExp("([?&])" + param + "=.*?(&|$)", "i");
    var separator = url.indexOf("?") !== -1 ? "&" : "?";
    if (url.match(regex)) {
      return url.replace(regex, "$1" + param + "=" + value + "$2");
    } else {
      return url + separator + param + "=" + value;
    }
  }
  
  // Public API
  return {
    updateURLParameter: updateURLParameter
  };
})();
