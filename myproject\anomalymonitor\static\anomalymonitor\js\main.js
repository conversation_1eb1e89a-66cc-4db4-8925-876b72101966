// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('[Debug] main.js: DOM Content Loaded');
    
    // Import UIManager and initialize
    import('./modules/ui/UIManager.js')
        .then(module => {
            console.log('[Debug] main.js: UIManager module loaded');
            window.uiManager = new module.UIManager();
        })
        .catch(error => {
            console.error('[Debug] main.js: Failed to load UIManager:', error);
            alert('Failed to initialize the application. Please refresh the page.');
        });
});
