const TableEventHandlers = {
    /**
     * Handles the click event on a "day cube" (a cell representing a single day's activity for a set).
     * Opens the workorders page filtered by the set name and date, and attempts to auto-open
     * the first workorder.
     * @param {Event} event - The click event.
     */
    handleDayCubeClick(event) {
        const td = event.currentTarget;
        const setName = td.dataset.setName;
        const date = td.dataset.date;
        const workorderIds = JSON.parse(td.dataset.workorderIds || '[]');

        if (!setName || !workorderIds.length) {
            return;
        }
        const workordersUrl = new URL('/workorders/', window.location.origin);
        workordersUrl.searchParams.append('set_name', setName);
        if (date) {
            const formattedDate = new Date(date).toISOString().split('T')[0];
            workordersUrl.searchParams.append('activity_date', formattedDate);
        }
        workordersUrl.searchParams.append('direct_to_first', 'true');
        try {
            sessionStorage.setItem('auto_open_first_workorder', 'true');
            sessionStorage.setItem('auto_open_timestamp', Date.now().toString());
        } catch (e) {
        }
        window.open(workordersUrl.toString(), '_blank');
    },

    /**
     * Handles the click event on a comment icon.
     * Opens the comment popup for the specified set and date.
     * @param {string} setName - The name of the set.
     * @param {string} date - The date in YYYY-MM-DD format.
     */
    handleCommentIconClick(setName, date) {
        if (typeof CommentManager !== 'undefined') {
            CommentManager.openCommentPopup(setName, date);
        } else {
            console.error('CommentManager not found');
        }
    }
}; 