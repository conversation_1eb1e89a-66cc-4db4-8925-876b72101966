"""
Utility module for BigQuery operations.
Provides functions to connect to BigQuery and fetch data from presentations.
"""

import os
import json
from google.cloud import bigquery
from google.oauth2 import service_account
import logging
from pathlib import Path
from datetime import datetime
import pytz
from myproject.utils import get_bigquery_client as utils_get_client
from django.db import connection

# Get the base directory of the project
BASE_DIR = Path(__file__).resolve().parent.parent.parent

logger = logging.getLogger(__name__)

def get_project_id_from_credentials(credentials_path):
    """
    Read project ID from the service account key file.
    
    Args:
        credentials_path (str): Path to the service account key file
        
    Returns:
        str: Project ID from the credentials file
    """
    try:
        with open(credentials_path, 'r') as f:
            creds_data = json.load(f)
            return creds_data.get('project_id')
    except Exception as e:
        logger.error(f"Error reading project ID from credentials: {str(e)}")
        raise

# Add this import at the top with other imports


def get_table_schema(table_name):
    """
    Fetches and returns the schema of a specified BigQuery table.
    
    Args:
        table_name (str): Name of the table (e.g., 'project_field_activity')
        
    Returns:
        dict: Dictionary containing table schema information
    """
    try:
        client = utils_get_client()
        if client is None:
            raise ValueError("Failed to create BigQuery client")
            
        # Get the table reference
        table = client.get_table(f"`budibase-dev-369406.presentation.{table_name}`")
        
        # Extract schema information
        schema_info = {
            'table_name': table_name,
            'fields': [
                {
                    'name': field.name,
                    'type': field.field_type,
                    'mode': field.mode,
                    'description': field.description
                }
                for field in table.schema
            ]
        }
        
        return schema_info
        
    except Exception as e:
        logger.error(f"Error fetching schema for table {table_name}: {str(e)}")
        raise

def fetch_robotic_sets_data():
    """
    Fetches robotic sets data from BigQuery...
    """
    try:
        client = utils_get_client()
        if client is None:
            raise ValueError("Failed to create BigQuery client")
            
        # Rest of the function remains the same
        dataset_name = 'presentation'
        
        # Get the last updated time from PostgreSQL cache table for sets queries
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT last_updated 
                FROM querycache_bigquerycache 
                WHERE query LIKE %s AND query LIKE %s 
                ORDER BY last_updated DESC 
                LIMIT 1
            """, ['%CurrentActiveStatus%', '%fact_set_status_days%'])
            result = cursor.fetchone()
            last_sync_time = result[0] if result else datetime.now(pytz.UTC)

        query = f"""
        WITH CurrentActiveStatus AS (
            -- Get only currently active sets
            SELECT DISTINCT
                fsd.set_name as original_set_name,
                CASE 
                    WHEN REGEXP_CONTAINS(fsd.set_name, r'^IIN[0-9]+\\s+set$') 
                    THEN REGEXP_REPLACE(fsd.set_name, r'\\s+set$', '')
                    WHEN REGEXP_CONTAINS(fsd.set_name, r'^VIN.*\\s+set$')
                    THEN REGEXP_REPLACE(REGEXP_REPLACE(fsd.set_name, r'\\s+set$', ''), r'(\\d+)', r'-DR#\\1')
                    ELSE CONCAT('Team ', REGEXP_REPLACE(fsd.set_name, r'\\s+set$', ''))
                END as base_set_name
            FROM `{dataset_name}.fact_set_status_days` fsd
            JOIN `{dataset_name}.dim_wrike_set_status` ws ON fsd.status_id = ws.id
            WHERE ws.set_status = 'Active Project'
            AND fsd.date_key = (
                SELECT MAX(date_key)
                FROM `{dataset_name}.fact_set_status_days`
            )
        ),
        LatestActivity AS (
            -- Get all activities for currently active sets
            SELECT 
                cas.original_set_name,
                cas.base_set_name,
                pfa.set_name as display_name,
                pfa.project_name,
                pfa.country,
                pfa.timezone_name,
                pfa.end_datetime as last_activity,
                pfa.start_datetime,
                pfa.activity_name,
                pfa.activity_type,
                ROW_NUMBER() OVER (PARTITION BY cas.base_set_name ORDER BY pfa.end_datetime DESC, pfa.start_datetime DESC, pfa.activity_name) as activity_rank
            FROM CurrentActiveStatus cas
            JOIN `{dataset_name}.project_field_activity` pfa 
                ON cas.base_set_name = pfa.set_name
        ),
        LatestServiceCode AS (
            SELECT 
                set_name,
                service_code,
                ROW_NUMBER() OVER (PARTITION BY set_name ORDER BY end_datetime DESC, start_datetime DESC, activity_name) as service_rank
            FROM `{dataset_name}.project_field_activity`
            WHERE service_code IS NOT NULL
        ),
        CurrentDayMinutesWorked AS (
            SELECT
                set_name,
                MAX(minutes_worked) as minutes_worked,
                date
            FROM `{dataset_name}.daily_set_workday_length`
            WHERE date = CURRENT_DATE()
            GROUP BY set_name, date
        ),
        ProjectDates AS (
            -- Get project start and end dates
            SELECT
                project_name,
                MIN(first_activity_date) as project_start_date,
                MAX(last_activity_date) as project_end_date
            FROM `{dataset_name}.project_planned_vs_actual`
            GROUP BY project_name
        )
        SELECT DISTINCT
            la.display_name as set_name,
            la.project_name,
            la.last_activity,
            la.start_datetime,
            la.activity_name,
            la.activity_type,
            la.country,
            la.timezone_name as activity_timezone,
            cdmw.minutes_worked as current_day_minutes_worked,
            lsc.service_code,
            pd.project_start_date,
            pd.project_end_date
        FROM LatestActivity la
        LEFT JOIN CurrentDayMinutesWorked cdmw
            ON la.display_name = cdmw.set_name
        LEFT JOIN LatestServiceCode lsc
            ON la.display_name = lsc.set_name
            AND lsc.service_rank = 1
        LEFT JOIN ProjectDates pd
            ON la.project_name = pd.project_name
        WHERE la.activity_rank = 1
        ORDER BY la.last_activity DESC;
        """
        
        query_job = client.query(query)
        results = query_job.result()
        
        sets_data = []
        for row in results:
            # Calculate current local time based on the set's activity timezone
            local_time = datetime.now()  # Default fallback to server time

            # Use the activity timezone (from the latest activity record)
            timezone_to_use = row.activity_timezone

            if timezone_to_use:
                try:
                    # Get current time in the set's activity timezone
                    local_tz = pytz.timezone(timezone_to_use)
                    utc_now = datetime.now(pytz.UTC)
                    local_time = utc_now.astimezone(local_tz)

                    # Make it naive for Django compatibility
                    local_time = local_time.replace(tzinfo=None)

                except Exception as e:
                    logger.warning(f"Failed to convert timezone for set {row.set_name} with timezone {timezone_to_use}: {e}")
                    # Keep the server time as fallback
                    local_time = datetime.now()

            set_data = {
                'set_name': row.set_name,
                'project_name': row.project_name,
                'last_activity': row.last_activity,
                'start_datetime': row.start_datetime,
                'activity_name': row.activity_name,
                'activity_type': row.activity_type,
                'country': row.country,
                'timezone': timezone_to_use,
                'local_time': local_time,
                'minutes_worked': row.current_day_minutes_worked,
                'service_code': row.service_code,
                'project_start_date': row.project_start_date,
                'project_end_date': row.project_end_date
            }
            sets_data.append(set_data)
            
        return {'sets': sets_data, 'last_sync_time': last_sync_time}
        
    except Exception as e:
        logger.error(f"Error fetching robotic sets data: {str(e)}")
        raise

def fetch_sets_activity_past_days():
    """
    Fetches activity data for active sets over the past 6 days.
    Returns a dictionary with set names as keys and their activity data for the past 6 days.
    """
    try:
        client = utils_get_client()
        if client is None:
            raise ValueError("Failed to create BigQuery client")
            
        dataset_name = 'presentation'
        
        query = f"""
        WITH CurrentActiveStatus AS (
            -- Get only currently active sets
            SELECT DISTINCT
                fsd.set_name as original_set_name,
                CASE 
                    WHEN REGEXP_CONTAINS(fsd.set_name, r'^IIN[0-9]+\\s+set$') 
                    THEN REGEXP_REPLACE(fsd.set_name, r'\\s+set$', '')
                    WHEN REGEXP_CONTAINS(fsd.set_name, r'^VIN.*\\s+set$')
                    THEN REGEXP_REPLACE(REGEXP_REPLACE(fsd.set_name, r'\\s+set$', ''), r'(\\d+)', r'-DR#\\1')
                    ELSE CONCAT('Team ', REGEXP_REPLACE(fsd.set_name, r'\\s+set$', ''))
                END as base_set_name
            FROM `{dataset_name}.fact_set_status_days` fsd
            JOIN `{dataset_name}.dim_wrike_set_status` ws ON fsd.status_id = ws.id
            WHERE ws.set_status = 'Active Project'
            AND fsd.date_key = (
                SELECT MAX(date_key)
                FROM `{dataset_name}.fact_set_status_days`
            )
        ),
        PastFiveDaysActivity AS (
            -- Get activities for the past 6 days for active sets
            SELECT 
                cas.base_set_name as set_name,
                pfa.project_name,
                pfa.country,
                pfa.timezone_name,
                DATE(pfa.end_datetime) as activity_date,
                COUNT(DISTINCT pfa.activity_name) as activity_count,
                STRING_AGG(DISTINCT pfa.activity_name, ', ') as activities
            FROM CurrentActiveStatus cas
            JOIN `{dataset_name}.project_field_activity` pfa 
                ON cas.base_set_name = pfa.set_name
            WHERE DATE(pfa.end_datetime) >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 DAY)
            GROUP BY 
                cas.base_set_name,
                pfa.project_name,
                pfa.country,
                pfa.timezone_name,
                DATE(pfa.end_datetime)
        ),
        DailyMinutesWorked AS (
            -- Get minutes worked from daily_set_workday_length
            SELECT
                set_name,
                date,
                minutes_worked
            FROM `{dataset_name}.daily_set_workday_length`
            WHERE date >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 DAY)
        )
        SELECT 
            a.*,
            d.minutes_worked as total_minutes
        FROM PastFiveDaysActivity a
        LEFT JOIN DailyMinutesWorked d
            ON a.set_name = d.set_name
            AND a.activity_date = d.date
        ORDER BY set_name, activity_date DESC;
        """
        
        query_job = client.query(query)
        results = query_job.result()
        
        activity_data = {}
        for row in results:
            if row.set_name not in activity_data:
                activity_data[row.set_name] = {
                    'project_name': row.project_name,
                    'country': row.country,
                    'timezone': row.timezone_name,
                    'daily_activities': []
                }
            
            activity_data[row.set_name]['daily_activities'].append({
                'date': row.activity_date,
                'activity_count': row.activity_count,
                'total_minutes': row.total_minutes,  # This comes from daily_set_workday_length
                'activities': row.activities
            })
            
        return activity_data
        
    except Exception as e:
        logger.error(f"Error fetching sets activity data: {str(e)}")
        raise