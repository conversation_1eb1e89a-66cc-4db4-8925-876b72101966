import pandas as pd
import json
import logging

logger = logging.getLogger(__name__)


def filter_workorder_data(df: pd.DataFrame) -> pd.DataFrame:
    """Filter workorder data to show only PERIODICAL activities of STANDARD or DELAY type."""
    filtered_df = df[
        (df['activity_duration_type'] == 'PERIODICAL') &
        (df['activity_type'].isin(['STANDARD', 'DELAY']))
    ]
    return filtered_df


def process_data(df: pd.DataFrame) -> pd.DataFrame:
    """Process workorder data to handle blade keys, row IDs, and prevent overlapping activities."""
    df = df.copy()  # Create an explicit copy
    df.loc[:, 'blade_key'] = df['blade_key'].fillna('Common')

    # Create a unique row identifier for each activity to prevent overlapping
    # First sort by blade_key to ensure consistent category ordering, then by start_datetime
    # Use the original order from the chart: A at top, then B, C, Common at bottom
    category_order = {'A': 0, 'B': 1, 'C': 2, 'Common': 3}
    df['sort_order'] = df['blade_key'].map(category_order)
    df = df.sort_values(['sort_order', 'start_datetime'])

    # Create a unique row identifier by combining blade_key with a row number
    # Only create new rows when activities actually overlap in time
    df['row_id'] = ''

    # Process categories in the original chart order (A at top, Common at bottom)
    for blade in ['A', 'B', 'C', 'Common']:
        blade_activities = df[df['blade_key'] == blade].copy()
        if not blade_activities.empty:
            # Sort by start time
            blade_activities = blade_activities.sort_values('start_datetime')

            # Initialize row tracking
            row_counter = 1
            row_end_times = {}  # Track the end time of each row

            # Assign row IDs based on time overlap
            for idx, activity in blade_activities.iterrows():
                start_time = activity['start_datetime']
                assigned_row = None

                # Check if this activity can fit in any existing row
                for row_num in range(1, row_counter + 1):
                    row_id = f"{category_order[blade]}_{blade}_{row_num}"
                    # If this row's last activity ends before or at the same time as this activity starts,
                    # we can use this row
                    if row_id in row_end_times and row_end_times[row_id] <= start_time:
                        assigned_row = row_num
                        break

                # If no existing row works, create a new one
                if assigned_row is None:
                    assigned_row = row_counter
                    row_counter += 1

                # Assign the row ID and update the end time for this row
                row_id = f"{category_order[blade]}_{blade}_{assigned_row}"
                df.loc[idx, 'row_id'] = row_id
                row_end_times[row_id] = activity['end_datetime']

    # Create a display label that shows just the blade_key (for y-axis display)
    df['display_label'] = df['blade_key']

    return df


def wrap_text(text, max_length=60):
    """
    Wrap text to prevent tooltip from becoming too wide.
    Inserts <br> tags for line breaks in HTML.
    """
    if not text or len(text) <= max_length:
        return text

    words = text.split(' ')
    lines = []
    current_line = []
    current_length = 0

    for word in words:
        # If adding this word would exceed the max length, start a new line
        if current_length + len(word) + 1 > max_length and current_line:
            lines.append(' '.join(current_line))
            current_line = [word]
            current_length = len(word)
        else:
            current_line.append(word)
            current_length += len(word) + (1 if current_line else 0)

    # Add the last line
    if current_line:
        lines.append(' '.join(current_line))

    return '<br>'.join(lines)


def _extract_value_from_values(values):
    """Extract the actual value from the values field."""
    if values is None:
        return None
    if isinstance(values, list):
        if len(values) > 0:
            # Get the first value from the list
            first_value = values[0]
            if isinstance(first_value, dict):
                # Check if it's an attachment object
                if 'attachment_id' in first_value:
                    return 'image'
                # Check if it's a regular value object
                elif 'value' in first_value:
                    value = first_value['value']
                    # Check if it's an attachment_id and replace with "image"
                    if isinstance(value, str) and 'attachment_id' in str(value).lower():
                        return 'image'
                    return value
                else:
                    return str(first_value)
            else:
                # Check if it's an attachment_id and replace with "image"
                if isinstance(first_value, str) and 'attachment_id' in str(first_value).lower():
                    return 'image'
                return first_value
        return None
    elif isinstance(values, dict):
        # Check if it's an attachment object
        if 'attachment_id' in values:
            return 'image'
        elif 'value' in values:
            value = values['value']
            # Check if it's an attachment_id and replace with "image"
            if isinstance(value, str) and 'attachment_id' in str(value).lower():
                return 'image'
            return value
        else:
            return str(values)
    else:
        # Check if it's an attachment_id and replace with "image"
        if isinstance(values, str) and 'attachment_id' in str(values).lower():
            return 'image'
        return values


def format_custom_fields(custom_fields_json):
    """
    Format custom fields JSON for display in hover popup.
    Only displays 'title' and 'values' fields from the properties.
    Formats as "title - value" for each property with text wrapping.
    Returns 'null' if empty or None, otherwise formats as readable string.
    """
    if not custom_fields_json or custom_fields_json in ['null', 'NULL', '']:
        return 'null'

    try:
        # Try to parse as JSON
        if isinstance(custom_fields_json, str):
            custom_fields = json.loads(custom_fields_json)
        else:
            custom_fields = custom_fields_json

        # If it's an empty list or dict, return null
        if not custom_fields:
            return 'null'

        # Extract and format title - value pairs
        formatted_items = []

        if isinstance(custom_fields, list):
            if not custom_fields:
                return 'null'
            # Process list of objects, looking for title and values
            for item in custom_fields:
                if isinstance(item, dict):
                    title = item.get('title')
                    values = item.get('values')
                    if title is not None and values is not None:
                        actual_value = _extract_value_from_values(values)
                        if actual_value is not None and actual_value != 'image':
                            formatted_items.append(f"{title} - {actual_value}")
                    # Skip items that don't have title/values structure (like direct attachment_id objects)
        elif isinstance(custom_fields, dict):
            # Process single object, looking for title and values
            title = custom_fields.get('title')
            values = custom_fields.get('values')
            if title is not None and values is not None:
                actual_value = _extract_value_from_values(values)
                if actual_value is not None and actual_value != 'image':
                    formatted_items.append(f"{title} - {actual_value}")
            # Skip dicts that don't have title/values structure (like direct attachment_id objects)

        # Format properties with line breaks (no header)
        if formatted_items:
            return '<br>'.join(formatted_items)
        else:
            return 'null'

    except (json.JSONDecodeError, TypeError, AttributeError):
        # If parsing fails, return the original value or null
        return str(custom_fields_json) if custom_fields_json else 'null'


def format_comment(comment):
    """
    Format comment for display in hover popup with text wrapping.
    Returns 'null' if empty or None.
    """
    if not comment or comment in ['null', 'NULL', '']:
        return 'null'
    return wrap_text(str(comment))


def format_properties_for_table(properties_json):
    """
    Format properties JSON for display in performance table.
    Returns formatted string with each property on its own line, or None if no properties.
    """
    if not properties_json or properties_json in ['null', 'NULL', '']:
        return None

    try:
        # Try to parse as JSON
        if isinstance(properties_json, str):
            properties = json.loads(properties_json)
        else:
            properties = properties_json

        # If it's an empty list or dict, return None
        if not properties:
            return None

        # Extract and format title - value pairs
        formatted_items = []

        if isinstance(properties, list):
            if not properties:
                return None
            # Process list of objects, looking for title and values
            for item in properties:
                if isinstance(item, dict):
                    title = item.get('title')
                    values = item.get('values')
                    if title is not None and values is not None:
                        actual_value = _extract_value_from_values(values)
                        if actual_value is not None and actual_value != 'image':
                            formatted_items.append(f"{title} - {actual_value}")
        elif isinstance(properties, dict):
            # Process single object, looking for title and values
            title = properties.get('title')
            values = properties.get('values')
            if title is not None and values is not None:
                actual_value = _extract_value_from_values(values)
                if actual_value is not None and actual_value != 'image':
                    formatted_items.append(f"{title} - {actual_value}")

        # Return formatted properties with line breaks
        if formatted_items:
            return '\n'.join(formatted_items)
        else:
            return None

    except (json.JSONDecodeError, TypeError, AttributeError):
        return None


def calculate_activity_performance_data(df: pd.DataFrame) -> dict:
    """
    Calculate activity performance metrics including overruns, percentages, and summary statistics.

    Args:
        df: DataFrame with activity data including time_limit, is_over_time_limit,
            activity_overrun_reason_description columns

    Returns:
        dict: Contains performance_data list and summary_stats dict
    """
    if df.empty:
        return {
            'performance_data': [],
            'summary_stats': {
                'total_activities': 0,
                'over_time_count': 0,
                'over_time_percentage': 0,
                'on_time_count': 0,
                'on_time_percentage': 0,
                'average_overrun_minutes': 0
            }
        }

    performance_data = []
    total_overrun_minutes = 0
    over_time_activities = 0

    for _, row in df.iterrows():
        # Calculate actual duration in minutes
        actual_duration_min = row.get('duration_min', 0)
        expected_duration_min = row.get('time_limit', 0) if pd.notna(row.get('time_limit')) else 0

        # Calculate variance (positive = overrun, negative = under time)
        variance_min = actual_duration_min - expected_duration_min if expected_duration_min > 0 else 0

        # Determine performance status
        is_over_time = row.get('is_over_time_limit', False)
        if is_over_time:
            status = 'over'
            over_time_activities += 1
            total_overrun_minutes += max(0, variance_min)
        elif expected_duration_min > 0:
            status = 'on_time'  # Completed within expected time (includes under time)
        else:
            status = 'no_limit'  # No time limit set

        # Format durations
        actual_duration_formatted = format_duration_minutes(actual_duration_min)
        expected_duration_formatted = format_duration_minutes(expected_duration_min) if expected_duration_min > 0 else 'N/A'
        variance_formatted = format_variance_minutes(variance_min) if expected_duration_min > 0 else 'N/A'

        # Format overrun reason
        overrun_reason = row.get('activity_overrun_reason_description', '')
        if not overrun_reason or overrun_reason in ['null', 'NULL', '']:
            overrun_reason = 'N/A'

        # Format properties for display
        properties_formatted = format_properties_for_table(row.get('service_activity_custom_fields'))

        performance_data.append({
            'activity_name': row.get('activity_name', ''),
            'activity_type': row.get('activity_type', ''),
            'blade_key': row.get('blade_key', 'Common'),
            'actual_duration_min': actual_duration_min,
            'expected_duration_min': expected_duration_min,
            'actual_duration_formatted': actual_duration_formatted,
            'expected_duration_formatted': expected_duration_formatted,
            'variance_min': variance_min,
            'variance_formatted': variance_formatted,
            'status': status,
            'is_over_time': is_over_time,
            'overrun_reason': overrun_reason,
            'start_datetime': row.get('start_datetime'),
            'end_datetime': row.get('end_datetime'),
            'properties': row.get('service_activity_custom_fields'),
            'properties_formatted': properties_formatted
        })

    # Calculate summary statistics
    total_activities = len(df)
    on_time_count = 0
    no_limit_count = 0

    # Count activities by status
    for item in performance_data:
        if item['status'] == 'on_time':
            on_time_count += 1
        elif item['status'] == 'no_limit':
            no_limit_count += 1

    over_time_percentage = (over_time_activities / total_activities * 100) if total_activities > 0 else 0
    on_time_percentage = (on_time_count / total_activities * 100) if total_activities > 0 else 0
    no_limit_percentage = (no_limit_count / total_activities * 100) if total_activities > 0 else 0
    average_overrun_minutes = (total_overrun_minutes / over_time_activities) if over_time_activities > 0 else 0

    summary_stats = {
        'total_activities': total_activities,
        'over_time_count': over_time_activities,
        'over_time_percentage': round(over_time_percentage, 1),
        'on_time_count': on_time_count,
        'on_time_percentage': round(on_time_percentage, 1),
        'no_limit_count': no_limit_count,
        'no_limit_percentage': round(no_limit_percentage, 1),
        'average_overrun_minutes': round(average_overrun_minutes, 1)
    }

    return {
        'performance_data': performance_data,
        'summary_stats': summary_stats
    }


def format_duration_minutes(minutes):
    """Format duration in minutes to 'Xh Xmin' format."""
    if not minutes or minutes <= 0:
        return '0min'

    hours = int(minutes // 60)
    mins = int(minutes % 60)

    if hours > 0:
        return f"{hours}h {mins}min" if mins > 0 else f"{hours}h"
    else:
        return f"{mins}min"


def format_variance_minutes(variance_min):
    """Format variance in minutes with +/- prefix."""
    if variance_min == 0:
        return "±0min"
    elif variance_min > 0:
        return f"+{format_duration_minutes(variance_min)}"
    else:
        return f"-{format_duration_minutes(abs(variance_min))}"


def filter_performance_data(performance_data, status_filter='all'):
    """
    Filter performance data by status.

    Args:
        performance_data: List of performance data dictionaries
        status_filter: 'all', 'over', 'on_time', 'under', 'no_limit', 'exclude_no_limit'

    Returns:
        Filtered list of performance data
    """
    if status_filter == 'all':
        return performance_data
    elif status_filter == 'exclude_no_limit':
        # Return only over time and on time activities (exclude no_limit)
        return [item for item in performance_data if item['status'] != 'no_limit']

    return [item for item in performance_data if item['status'] == status_filter]