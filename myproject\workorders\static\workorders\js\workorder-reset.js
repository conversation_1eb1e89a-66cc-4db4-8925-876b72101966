/**
 * Handle the reset button click for the workorders page
 */
document.addEventListener('DOMContentLoaded', function() {
  // Get the reset button
  const resetButton = document.querySelector('a[href="/workorders/reset/"]');
  
  if (resetButton) {
    resetButton.addEventListener('click', function(e) {
      // Clear any client-side filters or state
      try {
        // Clear any DataTable state if it exists
        if ($.fn.DataTable.isDataTable('#workorders-table')) {
          const table = $('#workorders-table').DataTable();
          table.state.clear();
        }
        
        // Clear any localStorage items related to workorder filtering
        localStorage.removeItem('workorder_redirected');
        localStorage.removeItem('workorder_redirect_time');
        sessionStorage.removeItem('auto_open_first_workorder');
        sessionStorage.removeItem('auto_open_timestamp');
        
        // Let the default action (redirect) happen
      } catch (error) {
        console.error('Error clearing workorder filters:', error);
      }
    });
  }
});
