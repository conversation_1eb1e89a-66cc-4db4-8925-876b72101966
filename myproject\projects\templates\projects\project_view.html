{% extends 'base.html' %}

{% block title %}Projects{% endblock %}

{% load static %}

{% block header %}
Projects
<div class="float-right">
    <a href="{% url 'projects:project_metrics' %}"
        class="px-4 py-2 font-bold text-white bg-blue-500 rounded hover:bg-blue-700">
        View Metrics
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Load metrics switcher script -->
<script type="module">
    import { switchView } from "{% static 'projects/js/metrics-switcher.js' %}";
    
    // Make switchView function available globally for button onclick handlers
    window.switchView = switchView;
</script>

<!-- Project selection section -->
<div class="p-4 mb-4 w-full bg-white rounded-lg shadow-sm">
    {% include 'projects/project_selection.html' %}
</div>

<div class="overflow-hidden w-full">
    <div class="overflow-x-auto w-full">
        {% include 'projects/project_statistics.html' %}
        {% include 'projects/project_table.html' %}
    </div>

    <!-- Metrics Section -->
    <div class="p-4 mt-8 bg-gray-50 rounded-lg shadow">
        <div class="flex justify-between items-center mb-3">
            <div class="flex items-center space-x-3">
                <h2 class="text-lg font-semibold text-gray-800">Metrics</h2>
                <div class="inline-flex rounded-lg shadow-sm">
                    <button id="weeklyBtn" onclick="switchView('weekly')"
                        class="relative px-4 py-1.5 font-medium text-white bg-blue-500 rounded-l-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 hover:bg-blue-600">
                        Weekly
                        <span
                            class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600 transition-opacity duration-200"></span>
                    </button>
                    <button id="monthlyBtn" onclick="switchView('monthly')"
                        class="relative px-4 py-1.5 font-medium text-gray-700 bg-white rounded-r-lg transition-all duration-200 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50">
                        Monthly
                        <span
                            class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600 opacity-0 transition-opacity duration-200"></span>
                    </button>
                </div>
            </div>
        </div>

        <div id="metricsContent" class="relative">
            <!-- Loading Overlay -->
            <div id="loadingOverlay"
                class="flex hidden absolute inset-0 z-10 justify-center items-center bg-white bg-opacity-80 rounded-lg">
                <div class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full border-4 border-blue-500 animate-spin border-t-transparent"></div>
                    <span class="mt-2 text-sm text-gray-600">Loading metrics...</span>
                </div>
            </div>

            <!-- Weekly Metrics Section -->
            <div id="weeklyMetrics" class="transition-opacity duration-300 metrics-view">
                {% with period_type='Week' metrics=weekly_metrics %}
                {% include 'projects/project_metric_table.html' %}
                {% endwith %}
            </div>

            <!-- Monthly Metrics Section -->
            <div id="monthlyMetrics" class="hidden transition-opacity duration-300 metrics-view">
                {% with period_type='Month' metrics=monthly_metrics %}
                {% include 'projects/project_metric_table.html' %}
                {% endwith %}
            </div>
        </div>
    </div>
</div>

{% endblock %}