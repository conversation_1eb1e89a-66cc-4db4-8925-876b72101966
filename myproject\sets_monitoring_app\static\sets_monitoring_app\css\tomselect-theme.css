/* Tom Select custom styling */
.ts-wrapper .ts-control {
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    height: 40px !important;
    min-height: 40px !important;
    max-height: 40px !important;
    padding: 8px 8px !important;
    background: white !important;
    box-shadow: none !important;
    font-size: 13px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    display: flex !important;
    align-items: center !important;
    scroll-behavior: smooth !important;
}

.ts-wrapper.focus .ts-control {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5) !important;
    outline: none !important;
}

.ts-wrapper .ts-control .ts-input {
    color: #374151 !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
    vertical-align: middle !important;
}

.ts-wrapper .ts-control .ts-input::placeholder {
    color: #6b7280 !important;
    vertical-align: middle !important;
    line-height: 1.4 !important;
}

.ts-dropdown {
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.ts-wrapper .ts-control .item {
    background: #f3f4f6 !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.25rem !important;
    padding: 2px 6px !important;
    margin: 1px !important;
    display: inline-flex !important;
    align-items: center !important;
    max-width: calc(100% - 4px) !important;
    line-height: 1.2 !important;
    font-size: 12px !important;
}

/* Ensure proper wrapping and scrolling for multi-select */
.ts-wrapper.multi .ts-control {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    line-height: 1.4 !important;
    gap: 2px !important;
}

.ts-wrapper.multi .ts-control .ts-input {
    display: inline-block !important;
    width: auto !important;
    min-width: 60px !important;
    flex-shrink: 0 !important;
    margin: 0 !important;
}

/* Remove any default select styling */
.ts-wrapper {
    position: relative !important;
}

/* Add dropdown arrow for all Tom Select controls */
.ts-wrapper .ts-control::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    pointer-events: none;
}

/* Rotate arrow when dropdown is open */
.ts-wrapper.dropdown-active .ts-control::after {
    transform: translateY(-50%) rotate(180deg);
}

/* Ensure padding for the arrow */
.ts-wrapper .ts-control {
    padding-right: 28px !important;
}

/* Custom scrollbar styling */
.ts-wrapper .ts-control::-webkit-scrollbar {
    width: 4px !important;
}

.ts-wrapper .ts-control::-webkit-scrollbar-track {
    background: transparent !important;
}

.ts-wrapper .ts-control::-webkit-scrollbar-thumb {
    background: #d1d5db !important;
    border-radius: 2px !important;
}

.ts-wrapper .ts-control::-webkit-scrollbar-thumb:hover {
    background: #9ca3af !important;
}
