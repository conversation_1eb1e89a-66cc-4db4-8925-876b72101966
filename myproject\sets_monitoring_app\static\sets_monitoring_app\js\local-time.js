// Initialize the current time UTC from the server-provided value
window.currentTimeUtc = null;

document.addEventListener('DOMContentLoaded', function () {
    // Get the UTC time from the data attribute
    const container = document.querySelector('.container');
    window.currentTimeUtc = container.dataset.currentTimeUtc;
    console.log('Current time UTC:', window.currentTimeUtc);

    // Convert UTC to local time
    function updateLocalTime() {
        const utcTime = new Date(window.currentTimeUtc);
        const localTimeStr = utcTime.toLocaleString('en-GB', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        document.getElementById('localTime').textContent = localTimeStr;
    }

    // Update local time immediately
    updateLocalTime();
}); 