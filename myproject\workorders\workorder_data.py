from typing import Dict, Any, Optional, List, Tuple, Union
import pandas as pd
import logging
import gc
from datetime import datetime
from functools import wraps
from google.cloud import bigquery

from myproject.utils import get_bigquery_client
from .constants import *

logger = logging.getLogger(__name__)


def cleanup_memory():
    """Force garbage collection to free up memory after heavy operations."""
    gc.collect()


# ========================= HELPER FUNCTIONS =========================

def handle_bigquery_errors(default_return=None):
    """Decorator for consistent BigQuery error handling."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"BigQuery error in {func.__name__}: {str(e)}")
                return default_return if default_return is not None else []
        return wrapper
    return decorator


def execute_bigquery_query(query: str, params: List = None, return_type: str = 'list'):
    """Execute BigQuery query with consistent error handling and memory optimization."""
    client = get_bigquery_client()
    if client is None:
        raise RuntimeError("BigQuery client not initialized")

    job_config = bigquery.QueryJobConfig(query_parameters=params or [])
    query_job = client.query(query, job_config=job_config)
    results = query_job.result()

    if return_type == 'single':
        return next(results, None)
    elif return_type == 'dataframe':
        # Memory optimization: Use pandas.DataFrame.from_records for better memory efficiency
        try:
            # Direct DataFrame creation from BigQuery results is more memory efficient
            # than creating intermediate list
            df = pd.DataFrame.from_records([dict(row) for row in results])
            return df
        except Exception as e:
            logger.error(f"Error creating DataFrame: {str(e)}")
            return pd.DataFrame()
    elif return_type == 'total_rows':
        return results.total_rows
    else:
        # Memory optimization: Convert to list efficiently
        try:
            return list(results)
        except Exception as e:
            logger.error(f"Error converting results to list: {str(e)}")
            return []


def build_filter_conditions(
    project_name: Union[str, List[str], None] = None,
    service_code: Union[str, List[str], None] = None,
    set_name: Union[str, List[str], None] = None,
    customer: Union[str, List[str], None] = None,
    activity_date: Optional[str] = None,
    workorder_name: Optional[str] = None,
    workorder_id: Optional[int] = None
) -> Tuple[Dict[str, str], List]:
    """Build filter conditions and parameters for BigQuery queries."""
    filters = {}
    params = []
    
    # Build filters for each field type
    for field_info in [
        ('project', project_name, 'project_name', ''),
        ('service', service_code, 'service_code', ''),
        ('set', set_name, 'set_name', ''),
        ('customer', customer, 'pwt.client_name', f'FROM {PROJECT_WORKORDER_TABLE} pwt WHERE pwt.workorder_id = {PROJECT_FIELD_ACTIVITY_TABLE}.workorder_id')
    ]:
        filter_key, value, field_name, special_clause = field_info
        
        if value:
            filter_str, filter_params = _build_single_filter(filter_key, value, field_name, special_clause)
            if filter_str:
                filters[f'{filter_key}_filter'] = filter_str
                params.extend(filter_params)
    
    # Handle date filter
    if activity_date:
        parsed_date = parse_activity_date(activity_date)
        filters['date_filter'] = """
        AND (
            DATE(max_all_activity_date) = @activity_date
            OR workorder_id IN (
                SELECT DISTINCT workorder_id
                FROM `budibase-dev-369406.presentation.project_field_activity`
                WHERE DATE(start_datetime) = @activity_date
                OR DATE(end_datetime) = @activity_date
            )
        )
        """
        params.append(bigquery.ScalarQueryParameter("activity_date", "DATE", parsed_date))
    
    # Handle name and ID filters
    if workorder_name:
        filters['name_filter'] = "AND LOWER(wo_name) LIKE CONCAT('%', LOWER(@workorder_name), '%')"
        params.append(bigquery.ScalarQueryParameter("workorder_name", "STRING", workorder_name))
    
    if workorder_id:
        filters['id_filter'] = "AND workorder_id = @workorder_id"
        params.append(bigquery.ScalarQueryParameter("workorder_id", "INTEGER", workorder_id))
    
    return filters, params


def _build_single_filter(filter_key: str, value: Union[str, List[str]], field_name: str, special_clause: str = '') -> Tuple[str, List]:
    """Build filter string and parameters for a single field."""
    params = []
    
    if isinstance(value, list) and len(value) > 0:
        placeholders = []
        for i, item in enumerate(value):
            param_name = f"{filter_key}_{i}"
            placeholders.append(f"{field_name} = @{param_name}")
            params.append(bigquery.ScalarQueryParameter(param_name, "STRING", item))
        
        condition = f"({' OR '.join(placeholders)})"
        if special_clause:
            return f"AND EXISTS (SELECT 1 {special_clause} AND {condition})", params
        else:
            return f"AND {condition}", params
            
    elif isinstance(value, str) and value:
        param_name = filter_key
        if special_clause:
            filter_str = f"AND EXISTS (SELECT 1 {special_clause} AND {field_name} = @{param_name})"
        else:
            filter_str = f"AND {field_name} = @{param_name}"
        params.append(bigquery.ScalarQueryParameter(param_name, "STRING", value))
        return filter_str, params
    
    return '', []


def format_hours(minutes: Optional[float]) -> str:
    """Format minutes into hours and minutes display."""
    if minutes is None:
        return "N/A"
    hours = int(minutes) // 60
    remaining_minutes = int(minutes) % 60
    return f"{hours}h {remaining_minutes:02d}min"


def parse_activity_date(date_string: str):
    """Parse activity date string with error handling."""
    try:
        return datetime.strptime(date_string, '%Y-%m-%d').date()
    except ValueError:
        return date_string


def format_date_range(start_date, end_date) -> str:
    """Format date range for display with HTML line break."""
    start_str = start_date.strftime('%Y-%m-%d') if start_date else 'N/A'
    end_str = end_date.strftime('%Y-%m-%d') if end_date else 'N/A'
    return f"{start_str}<br>to {end_str}"


def format_datetime_display(dt) -> str:
    """Format datetime for display."""
    if pd.notna(dt):
        return dt.strftime('%Y-%m-%d %H:%M')
    return 'N/A'


# ========================= MAIN FUNCTIONS =========================

@handle_bigquery_errors(default_return=pd.DataFrame())
def get_raw_workorder_data(workorder_id: int) -> pd.DataFrame:
    """Get raw workorder data from BigQuery."""
    df = execute_bigquery_query(
        RAW_WORKORDER_DATA_QUERY,
        [bigquery.ScalarQueryParameter("workorder_id", "INTEGER", workorder_id)],
        'dataframe'
    )
    # Force garbage collection after creating large DataFrame
    cleanup_memory()
    return df


@handle_bigquery_errors()
def get_workorder_metadata(workorder_id: int) -> Optional[Dict[str, Any]]:
    """Get workorder metadata from BigQuery."""
    metadata_row = execute_bigquery_query(
        WORKORDER_METADATA_QUERY,
        [bigquery.ScalarQueryParameter("workorder_id", "INTEGER", workorder_id)],
        'single'
    )

    if metadata_row:
        return {
            "Workorder Name": metadata_row.wo_name,
            "Completed Date": metadata_row.max_all_activity_date,
            "Marked as Completed": (metadata_row.is_completed, metadata_row.wo_completion_date),
            "Project": metadata_row.project_name,
            "Set": metadata_row.set_name,
            "Turbine": metadata_row.turbine_name,
            "Service": metadata_row.service_code,
            "Direct Hours": format_hours(metadata_row.duration_in_minutes),
            "Delay Hours": format_hours(metadata_row.delay_duration_in_minutes),
        }
    return None


def get_workorders_flexible(
    project_name: Union[str, List[str], None] = None,
    service_code: Union[str, List[str], None] = None,
    set_name: Union[str, List[str], None] = None,
    customer: Union[str, List[str], None] = None,
    activity_date: Optional[str] = None,
    workorder_name: Optional[str] = None,
    workorder_id: Optional[int] = None,
    limit: int = 100,
    offset: int = 0,
    return_count: bool = False
) -> Union[List[Any], Tuple[List[Dict[str, Any]], int]]:
    """Unified function for getting workorders with flexible options."""
    filters, params = build_filter_conditions(
        project_name, service_code, set_name, customer, 
        activity_date, workorder_name, workorder_id
    )
    
    # Build limit clause
    if workorder_id:
        limit_clause = ""
    elif limit is None:
        # No limit for client-side pagination - get ALL workorders
        limit_clause = ""
    else:
        limit_clause = f"LIMIT {limit} OFFSET {offset}"
    
    # Format the query with the filter conditions
    query = WORKORDERS_QUERY_TEMPLATE.format(
        project_filter=filters.get('project_filter', ''),
        service_filter=filters.get('service_filter', ''),
        set_filter=filters.get('set_filter', ''),
        customer_filter=filters.get('customer_filter', ''),
        date_filter=filters.get('date_filter', ''),
        name_filter=filters.get('name_filter', ''),
        id_filter=filters.get('id_filter', ''),
        limit_clause=limit_clause
    )

    try:
        results = execute_bigquery_query(query, params)
        
        if return_count:
            # For get_workorder_data - format results and get count
            data = []
            for row in results:
                start_date = row['min_start_date'].strftime('%Y-%m-%d') if row['min_start_date'] else None
                end_date = row['max_all_activity_date'].strftime('%Y-%m-%d') if row['max_all_activity_date'] else None
                
                data.append({
                    'wo_name': row['wo_name'],
                    'set_and_service': f"{row['set_name']}<hr>{row['service_code']}",
                    'start_end_dates': format_date_range(row['min_start_date'], row['max_all_activity_date']),
                    'max_all_activity_date': end_date,
                    'min_start_date': start_date,
                    'workorder_id': row['workorder_id']
                })
            
            # SAFE OPTIMIZATION: Use original template but get total_rows instead of full results
            count_query = WORKORDERS_QUERY_TEMPLATE.format(
                **{k: v for k, v in filters.items()},
                **{k: '' for k in ['project_filter', 'service_filter', 'set_filter', 'customer_filter', 'date_filter', 'name_filter', 'id_filter'] if k not in filters},
                limit_clause=''
            )
            total_records = execute_bigquery_query(count_query, params, 'total_rows')
            return data, total_records
        else:
            # For get_workorders - return raw results
            return results[0] if workorder_id and results else results
            
    except Exception as e:
        logger.error(f"Error querying BigQuery for workorders: {str(e)}")
        return ([], 0) if return_count else []


def get_workorders(
    project_name: Optional[str] = None,
    service_code: Optional[str] = None,
    set_name: Optional[str] = None,
    customer: Optional[str] = None,
    activity_date: Optional[str] = None,
    workorder_name: Optional[str] = None,
    workorder_id: Optional[int] = None,
    limit: int = 100
) -> List[Any]:
    """Get workorders list."""
    return get_workorders_flexible(
        project_name, service_code, set_name, customer,
        activity_date, workorder_name, workorder_id, limit
    )


@handle_bigquery_errors(default_return={'projects': [], 'services': [], 'sets': [], 'customers': []})
def get_dropdown_values() -> Dict[str, List[Tuple[str, str]]]:
    """Get dropdown values for filters."""
    dropdown_values = execute_bigquery_query(DROPDOWN_QUERY)

    dropdown_dict = {'projects': [], 'services': [], 'sets': [], 'customers': []}

    for row in dropdown_values:
        dropdown_type = row['dropdown_type']
        if dropdown_type in ['project', 'service', 'set', 'customer']:
            key = f"{dropdown_type}s"
            dropdown_dict[key].append((row['value'], row['label']))

    # Sort dropdown values, handling None values
    for key in dropdown_dict:
        dropdown_dict[key] = sorted(dropdown_dict[key], key=lambda x: (x[1] is None, x[1]))

    return dropdown_dict


def get_workorders_and_dropdown_values(
    project_name: Optional[str] = None,
    service_code: Optional[str] = None,
    set_name: Optional[str] = None,
    customer: Optional[str] = None,
    activity_date: Optional[str] = None,
    workorder_name: Optional[str] = None,
    workorder_id: Optional[int] = None,
    limit: int = 100
) -> Tuple[List[Any], Dict[str, List[Tuple[str, str]]]]:
    """Get workorders and dropdown values together."""
    workorders = get_workorders(project_name, service_code, set_name, customer, activity_date, workorder_name, workorder_id, limit)
    dropdown_values = get_dropdown_values()
    return workorders, dropdown_values


@handle_bigquery_errors()
def get_workorder(workorder_id: int) -> Optional[Any]:
    """Get single workorder by ID."""
    return execute_bigquery_query(
        SINGLE_WORKORDER_QUERY,
        [bigquery.ScalarQueryParameter("workorder_id", "INTEGER", workorder_id)],
        'single'
    )


@handle_bigquery_errors()
def get_daily_reports(workorder_id: int) -> Optional[List[Dict[str, Any]]]:
    """Get all archived Daily Reports for a workorder."""
    results = execute_bigquery_query(
        DAILY_REPORTS_QUERY,
        [bigquery.ScalarQueryParameter("workorder_id", "INTEGER", workorder_id)]
    )

    if results:
        reports = []
        for row in results:
            reports.append({
                'report_url': row['report_url'],
                'created_at': row['created_at'],
                'report_type': row['report_type'],
                'dates': row['dates'] if 'dates' in row else None
            })
        return reports
    return None


def get_activity_data(workorder_id: int) -> Optional[List[Dict[str, Any]]]:
    """Get STANDARD and DELAY activities for a workorder."""
    raw_data = get_raw_workorder_data(workorder_id)

    if raw_data.empty:
        return None

    # Filter for STANDARD and DELAY activities
    activity_df = raw_data[
        (raw_data['activity_type'].isin(['STANDARD', 'DELAY'])) &
        (raw_data['start_datetime'].notna()) &
        (raw_data['end_datetime'].notna())
    ]

    if activity_df.empty:
        return None

    # Format the data for display
    activities = []
    for _, row in activity_df.iterrows():
        activities.append({
            'start_datetime': format_datetime_display(row['start_datetime']),
            'end_datetime': format_datetime_display(row['end_datetime']),
            'activity_name': row['activity_name'],
            'activity_type': row['activity_type'],
            'duration': format_hours(row['duration_min']),
            'activity_duration_type': row['activity_duration_type']
        })

    # Sort by start_datetime
    return sorted(activities, key=lambda x: x['start_datetime'])


def get_workorder_data(start: int, length: int, project_name: str, service_code: str, set_name: str, customer: str, workorder_name: str, activity_date: str) -> Tuple[List[Dict[str, Any]], int]:
    """Get workorder data with pagination (uses the flexible function)."""
    # Regular server-side pagination
    return get_workorders_flexible(
        project_name=project_name,
        service_code=service_code,
        set_name=set_name,
        customer=customer,
        activity_date=activity_date,
        workorder_name=workorder_name,
        limit=length,
        offset=start,
        return_count=True
    )


@handle_bigquery_errors(default_return=(None, None))
def get_set_code_and_timezone(workorder_id):
    """Get the set_code and timezone for a workorder from BigQuery."""
    query = f"""
    SELECT set_name, timezone_name
    FROM {PROJECT_FIELD_ACTIVITY_TABLE}
    WHERE workorder_id = @workorder_id
    LIMIT 1
    """

    row = execute_bigquery_query(
        query,
        [bigquery.ScalarQueryParameter("workorder_id", "INTEGER", workorder_id)],
        'single'
    )
    
    if not row:
        return None, None

    set_name = row.set_name
    timezone_name = row.timezone_name

    if not set_name:
        return None, timezone_name

    # Extract the set code from the set name
    if set_name.startswith("Team "):
        set_code = set_name[5:].split(' ')[0].split(':')[0].strip()
    else:
        set_code = set_name.split(' ')[0].split(':')[0].strip()

    return set_code if set_code else None, timezone_name


@handle_bigquery_errors(default_return=(None, None))
def get_workorder_date_range(workorder_id):
    """Get the start and end dates for a workorder from BigQuery, only considering STANDARD and DELAY activities."""
    query = f"""
    SELECT MIN(start_datetime) as start_date, MAX(end_datetime) as end_date
    FROM {PROJECT_FIELD_ACTIVITY_TABLE}
    WHERE workorder_id = @workorder_id
      AND start_datetime IS NOT NULL
      AND end_datetime IS NOT NULL
      AND (activity_type = 'STANDARD' OR activity_type = 'DELAY')
    """

    row = execute_bigquery_query(
        query,
        [bigquery.ScalarQueryParameter("workorder_id", "INTEGER", workorder_id)],
        'single'
    )
    
    return (row.start_date, row.end_date) if row else (None, None) 


@handle_bigquery_errors(default_return=[])
def get_workorders_summary_data(workorder_ids: List[int]) -> List[Dict[str, Any]]:
    """Get summary data with hours breakdown for a list of workorder IDs."""
    if not workorder_ids:
        return []
    
    from .constants import WORKORDER_SUMMARY_QUERY
    
    results = execute_bigquery_query(
        WORKORDER_SUMMARY_QUERY,
        [bigquery.ArrayQueryParameter("workorder_ids", "INTEGER", workorder_ids)]
    )
    
    summary_data = []
    for row in results:
        summary_data.append({
            'workorder_id': row.workorder_id,
            'wo_name': row.wo_name,
            'direct_minutes': row.direct_minutes,
            'delay_minutes': row.delay_minutes,
            'blade_a_minutes': row.blade_a_minutes,
            'blade_b_minutes': row.blade_b_minutes,
            'blade_c_minutes': row.blade_c_minutes,
            'common_minutes': row.common_minutes,
        })
    
    return summary_data 