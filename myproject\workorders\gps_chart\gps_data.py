"""
GPS Data Processing Module
Extracts speed and timeline data from Navirec API responses for composite chart visualization
"""
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


def process_navirec_timeline_data(api_response: Dict[str, Any]) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]], str, str]:
    """
    Extract speed data and activity timeline from Navirec timeline API response.
    
    Args:
        api_response: The JSON response from Navirec vehicle_timeline endpoint
        
    Returns:
        Tuple of (speed_data, activity_timeline, start_time, end_time) from API response
    """
    speed_data = []
    activity_timeline = []
    api_start_time = ""
    api_end_time = ""
    
    try:
        # Extract start_time and end_time from API response
        if 'data' in api_response and api_response['data']:
            first_entry = api_response['data'][0]
            api_start_time = first_entry.get('start_time', '')
            api_end_time = first_entry.get('end_time', '')
        
        # Handle both single timeline entry and list of entries
        timeline_events = []
        if 'timeline_events' in api_response:
            # Direct timeline_events in response
            timeline_events = api_response['timeline_events']
        elif 'data' in api_response:
            # Data array containing timeline_events
            data = api_response['data']
            for entry in data:
                if 'timeline_events' in entry:
                    timeline_events.extend(entry['timeline_events'])
        
        if not timeline_events:
            return speed_data, activity_timeline, api_start_time, api_end_time
            
        # Process timeline events
        for event in timeline_events:
            # Only process trip events
            if event.get('event_type') == 'trip':
                trip = event['trip']
                activity = trip.get('activity', '')
                start_time = trip.get('start_time')
                end_time = trip.get('end_time')
                
                # Skip events with missing time data
                if not start_time or not end_time:
                    continue
                
                # Add to activity timeline
                activity_timeline.append({
                    'activity': activity,
                    'start_time': start_time,
                    'end_time': end_time,
                    'message': event.get('message', ''),
                    'address': event.get('address', ''),
                    'start_address': trip.get('start_address', event.get('address', '')),
                    'end_address': trip.get('end_address', event.get('address', '')),
                    'distance': float(trip.get('distance') or 0),  # Handle None values
                    'duration_minutes': calculate_duration_minutes(start_time, end_time)
                })
                
                # Add speed data points
                if activity == 'driving':
                    max_speed = trip.get('max_speed') or 0
                    if max_speed and max_speed > 0:
                        # Add speed points for driving segments
                        speed_data.append({
                            'time': start_time,
                            'speed': 0,  # Start at 0
                            'activity': activity,
                            'start_address': trip.get('start_address', event.get('address', ''))
                        })
                        speed_data.append({
                            'time': start_time,
                            'speed': float(max_speed),  # Ensure it's a float
                            'activity': activity,
                            'start_address': trip.get('start_address', event.get('address', ''))
                        })
                        speed_data.append({
                            'time': end_time,
                            'speed': float(max_speed),  # Ensure it's a float
                            'activity': activity,
                            'start_address': trip.get('start_address', event.get('address', ''))
                        })
                        speed_data.append({
                            'time': end_time,
                            'speed': 0,  # Return to 0
                            'activity': activity,
                            'start_address': trip.get('start_address', event.get('address', ''))
                        })
                else:
                    # Add zero speed points for non-driving activities
                    speed_data.append({
                        'time': start_time,
                        'speed': 0,
                        'activity': activity,
                        'start_address': trip.get('start_address', event.get('address', ''))
                    })
                    speed_data.append({
                        'time': end_time,
                        'speed': 0,
                        'activity': activity,
                        'start_address': trip.get('start_address', event.get('address', ''))
                    })
        
        # Sort by time (handle None values)
        speed_data.sort(key=lambda x: x['time'] or '9999-12-31T23:59:59Z')
        activity_timeline.sort(key=lambda x: x['start_time'] or '9999-12-31T23:59:59Z')
        
    except Exception as e:
        logger.error(f"Error processing Navirec timeline data: {str(e)}")
        
    return speed_data, activity_timeline, api_start_time, api_end_time


def get_activity_color(activity: str) -> str:
    """Return color for activity type matching Navirec platform styling."""
    activity_colors = {
        'driving': '#22c55e',      # Green
        'idling': '#f97316',       # Orange  
        'parking': '#3b82f6',      # Blue
        'offline': '#6b7280',      # Gray
    }
    return activity_colors.get(activity, '#6b7280')  # Default to gray


def calculate_duration_minutes(start_time: str, end_time: str) -> float:
    """Calculate duration in minutes between two ISO timestamps."""
    try:
        if not start_time or not end_time:
            return 0.0
            
        start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        duration = (end - start).total_seconds() / 60
        return round(duration, 1)
    except Exception as e:
        logger.error(f"Error calculating duration: {str(e)}")
        return 0.0


def format_address(address: str) -> str:
    """Simplify address for display."""
    if not address:
        return "Unknown Location"
    
    # Take first part before comma for brevity
    parts = address.split(',')
    return parts[0] if parts else address[:50] 