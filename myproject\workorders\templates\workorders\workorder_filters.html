<form method="get" action="{% url 'workorder_view' %}" id="workorder-form" class="w-full">
  <div class="grid grid-cols-1 lg:grid-cols-[1fr_1fr_1fr_1fr_1fr_1fr_auto] gap-2 w-full items-end">
    <div class="flex flex-col">
      <label for="project_name" class="text-sm font-medium text-gray-700 mb-1">Project</label>
      <select name="project_name" id="project_name" class="form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 select2-input" multiple>
        <option value=""></option>
        {% for project_name, project_label in dropdown_values.projects %}
          <option value="{{ project_name }}" {% if project_name in selected_project_name %}selected{% endif %}>
            {{ project_label }}
          </option>
        {% endfor %}
      </select>
    </div>

    <div class="flex flex-col">
      <label for="service_code" class="text-sm font-medium text-gray-700 mb-1">Service</label>
      <select name="service_code" id="service_code" class="form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 select2-input" multiple>
        <option value=""></option>
        {% for service_code, service_label in dropdown_values.services %}
          <option value="{{ service_code }}" {% if service_code in selected_service_code %}selected{% endif %}>
            {{ service_label }}
          </option>
        {% endfor %}
      </select>
    </div>

    <div class="flex flex-col">
      <label for="set_name" class="text-sm font-medium text-gray-700 mb-1">Set</label>
      <select name="set_name" id="set_name" class="form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 select2-input" multiple>
        <option value=""></option>
        {% for set_name, set_label in dropdown_values.sets %}
          <option value="{{ set_name }}" {% if set_name in selected_set_name %}selected{% endif %}>
            {{ set_label }}
          </option>
        {% endfor %}
      </select>
    </div>

    <div class="flex flex-col">
      <label for="customer" class="text-sm font-medium text-gray-700 mb-1">Customer</label>
      <select name="customer" id="customer" class="form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 select2-input" multiple>
        <option value=""></option>
        {% for customer, customer_label in dropdown_values.customers %}
          <option value="{{ customer }}" {% if customer in selected_customer %}selected{% endif %}>
            {{ customer_label }}
          </option>
        {% endfor %}
      </select>
    </div>

    <div class="flex flex-col">
      <label for="workorder_name" class="text-sm font-medium text-gray-700 mb-1">Search</label>
      <input type="text" name="workorder_name" id="workorder_name" class="form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 cursor-text" value="{{ selected_workorder_name|default:'' }}" placeholder="Workorder Name">
    </div>

    <div class="flex flex-col">
      <label for="activity_date" class="text-sm font-medium text-gray-700 mb-1">Date</label>
      <input type="date" name="activity_date" id="activity_date" class="form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 cursor-pointer" value="{{ selected_activity_date|default:'' }}">
    </div>

    <button type="submit" class="w-full xl:w-[45px] xl:h-[45px] h-[35px] bg-blue-500 text-white hover:bg-blue-600 rounded-md flex items-center justify-center transition-all duration-300 xl:mb-0 xl:mt-0 mt-2 mb-2">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
        <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
      </svg>
      <span class="inline lg:hidden ml-2">Search</span>
    </button>
  </div>
</form> 