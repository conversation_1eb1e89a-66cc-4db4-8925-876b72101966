from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.core.serializers.json import DjangoJSONEncoder
import json
import logging

from .helper_functions import (
    get_payroll_query_results, process_payroll_results
)

logger = logging.getLogger(__name__)


@login_required
def payroll_view(request):
    """
    Main payroll view that displays all payroll data.
    """
    func_name = "payroll_view"

    try:
        # Get all data (no date range limit)
        results = get_payroll_query_results(func_name)
        if results is None:
            logger.warning(f"[{func_name}] No data available")
            payroll_data = {
                'technicians': [],
                'dates': [],
                'data': {},
                'summary': {}
            }
        else:
            payroll_data = process_payroll_results(results, func_name)

        # Serialize data for JavaScript
        try:
            payroll_data_json = json.dumps(payroll_data, cls=DjangoJSONEncoder, ensure_ascii=True)
            # Validate JSON
            json.loads(payroll_data_json)

        except Exception as e:
            logger.error(f"[{func_name}] JSON serialization failed: {str(e)}")
            payroll_data_json = '{}'

        context = {
            'page_title': 'Payroll',
            'payroll_data': payroll_data,
            'payroll_data_json': payroll_data_json,
        }

        return render(request, 'fsm_payroll/payroll_view.html', context)

    except Exception as e:
        logger.error(f"[{func_name}] Error processing payroll view: {str(e)}")
        return render(request, 'fsm_payroll/payroll_view.html', {
            'page_title': 'Payroll',
            'payroll_data': {},
            'payroll_data_json': '{}',
            'error': 'Unable to load payroll data'
        })
