/* Select2 Tailwind CSS customization */
.select2-container--tailwindcss-3 {
    cursor: pointer !important;
    height: 38px !important;
}

.select2-container--tailwindcss-3.select2-container--open .select2-dropdown {
    overflow-y: hidden !important;
}

/* Focus and highlight states */
.select2-container--tailwindcss-3.select2-container--focus .select2-selection--multiple,
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple {
    border-color: #3B82F6 !important;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5) !important;
}

/* Default state */
.select2-container--tailwindcss-3:not(.select2-container--open):not(.select2-container--focus) .select2-selection--multiple {
    border-color: #e2e8f0 !important;
    box-shadow: none !important;
}

/* Dropdown options */
.select2-container--tailwindcss-3 .select2-results__option {
    cursor: pointer !important;
}

.select2-container--tailwindcss-3 .select2-results__option--highlighted {
    background-color: #3B82F6 !important;
    color: white !important;
}

.select2-container--tailwindcss-3 .select2-results__option--selected:after {
    color: #3B82F6 / var(--tw-text-opacity, 1);
}

/* Selection container */
.select2-container--tailwindcss-3 .select2-selection--multiple {
    height: 38px !important;
    min-height: 38px !important;
    max-height: 38px !important;
    overflow-y: hidden !important;
    position: relative !important;
    cursor: pointer !important;
    padding: 0 !important;
    border-color: #e2e8f0 !important;
    display: flex !important;
    align-items: center !important;
}

/* Dropdown arrow */
.select2-container--tailwindcss-3 .select2-selection--multiple::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2364748b'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    pointer-events: none;
    z-index: 1;
}

/* Rotate arrow when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple::after {
    transform: translateY(-50%) rotate(180deg);
}

/* Selection rendered area */
.select2-container--tailwindcss-3 .select2-selection--multiple .select2-selection__rendered {
    display: flex !important;
    flex-wrap: wrap !important;
    overflow-y: hidden !important;
    align-items: center !important;
    height: 100% !important;
    padding-left: 0 !important;
    position: relative !important;
}

/* Adjust rendered area when dropdown is open to ensure proper search field positioning */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-selection__rendered {
    position: static !important;
}

/* Hide search field by default */
.select2-container--tailwindcss-3 .select2-selection--multiple .select2-search--inline {
    display: none !important;
}

/* Show search field when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-search--inline {
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
}

/* Show search input when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-search--inline .select2-search__field {
    width: calc(100% - 30px) !important; /* Account for dropdown arrow */
    margin: 0 !important;
    padding: 0 8px !important;
    height: 36px !important;
    line-height: 36px !important;
    font-size: 0.875rem !important;
    color: #4B5563 !important;
    text-align: left !important;
    position: relative !important;
    left: 0 !important;
    display: block !important;
}

/* Hide custom text when dropdown is open */
.select2-container--tailwindcss-3.select2-container--open .select2-selection--multiple .select2-selection__rendered .select2-selection__rendered-text {
    display: none !important;
}

/* Hide other Select2 default elements */
.select2-container--tailwindcss-3 .select2-selection__choice,
.select2-container--tailwindcss-3 .select2-selection__clear {
    display: none !important;
}

/* Hide pill display */
.select2-container--tailwindcss-3 .select2-selection__choice,
.select2-container--tailwindcss-3 .select2-selection--multiple .select2-selection__choice,
.select2-container--tailwindcss-3 .select2-selection--multiple .select2-selection__rendered .select2-selection__choice {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    max-height: 0 !important;
    max-width: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
}

/* Custom text display */
.select2-container--tailwindcss-3 .select2-selection--multiple .select2-selection__rendered .select2-selection__rendered-text {
    color: #4B5563 !important;
    font-size: 0.875rem !important;
    line-height: 36px !important;
    padding-left: 8px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    pointer-events: none !important;
    display: block !important;
    position: relative !important;
    z-index: 5 !important;
}
