// Manages workload data, filters, and metrics.
const workloadStore = {
    rawData: [], // Stores the full dataset retrieved from the server.
    filteredData: [], // Stores the data currently visible after filters are applied.
    storedPeriod: { // The full date range of rawData.
        start: null,
        end: null
    },
    activeFilters: { // Current state of all active filters.
        supervisors: [],
        serviceCodes: [], 
        dateRange: {
            start: null,
            end: null
        }
    },
    metrics: {}, // Cached calculations for displayed sets.

    // Initializes the store with the raw dataset and its overall period.
    initialize(data, period) {
        this.rawData = data;
        this.storedPeriod = period;

        // Default active date range: last 7 days of available data.
        const endDate = data.length > 0 ? new Date(Math.max(...data.map(item => new Date(item.date)))) : new Date();
        const startDate = data.length > 0 ? new Date(endDate) : new Date(new Date().setDate(endDate.getDate() -6));
        if (data.length > 0) startDate.setDate(endDate.getDate() - 6);

        this.activeFilters.dateRange = {
            start: startDate,
            end: endDate
        };
        
        // Load status changes for the period
        this.loadStatusChanges();
        
        this.applyFilters();
    },

    // Load status changes for the current period
    async loadStatusChanges() {
        if (StatusManager && StatusManager.loadStatusChanges) {
            const startDate = this.storedPeriod.start ? this.storedPeriod.start.toISOString().split('T')[0] : null;
            const endDate = this.storedPeriod.end ? this.storedPeriod.end.toISOString().split('T')[0] : null;
            
            if (startDate && endDate) {
                await StatusManager.loadStatusChanges(startDate, endDate);
            }
        }
    },

    // Updates active filters and re-applies them to the raw data.
    updateFilters(filters, shouldApplyFiltersAndUpdateMetrics = true) {
        // Max 90-day date range.
        if (filters.dateRange) {
            const start = filters.dateRange.start;
            const end = filters.dateRange.end;
            if (start && end) {
                const daysDiff = Math.floor((end - start) / (1000 * 60 * 60 * 24));
                if (daysDiff > 89) {
                    filters.dateRange.end = new Date(start);
                    filters.dateRange.end.setDate(start.getDate() + 89);
                }
            }
        }
        this.activeFilters = { ...this.activeFilters, ...filters };
        if (shouldApplyFiltersAndUpdateMetrics) {
            this.applyFilters();
        }
    },

    // Filters rawData based on activeFilters and updates filteredData.
    applyFilters() {
        const { supervisors, serviceCodes, dateRange } = this.activeFilters;
        const showAllSupervisors = supervisors.length === 0;

        // Step 1: If supervisor filter is active, identify which sets have activity from selected supervisor(s)
        const relevantSets = new Set();
        if (!showAllSupervisors) {
            this.rawData.forEach(item => {
                const itemDate = new Date(item.date);
                const matchesDate = (!dateRange.start || itemDate >= dateRange.start) &&
                    (!dateRange.end || itemDate <= dateRange.end);
                const matchesSupervisor = item.supervisor && supervisors.includes(item.supervisor);
                
                if (matchesSupervisor && matchesDate) {
                    relevantSets.add(item.set_name);
                }
            });
        }

        // Step 2: Filter data based on set relevance, service codes, and date range
        this.filteredData = this.rawData.filter(item => {
            const itemDate = new Date(item.date);

            // Date filtering
            const matchesDate = (!dateRange.start || itemDate >= dateRange.start) &&
                (!dateRange.end || itemDate <= dateRange.end);

            // Service code filtering (unchanged)
            const showAllServiceCodes = serviceCodes.length === 0;
            const matchesServiceCode = showAllServiceCodes || 
                (item.service_codes && Array.isArray(item.service_codes) && 
                 item.service_codes.some(code => serviceCodes.includes(code)));

            // Set filtering: show all sets if no supervisor selected, otherwise only relevant sets
            const matchesSetSelection = showAllSupervisors || relevantSets.has(item.set_name);

            return matchesSetSelection && matchesServiceCode && matchesDate;
        });

        // Recalculate forward-fill expected units after filtering
        this.recalculateForwardFill();
        this.updateMetrics();
    },

    // Recalculates forward-fill expected units for the filtered data
    recalculateForwardFill() {
        
        const { supervisors, dateRange } = this.activeFilters;
        const showAllSupervisors = supervisors.length === 0;
        
        const relevantSets = new Set();
        this.filteredData.forEach(item => {
            relevantSets.add(item.set_name);
        });
        
        
        // Generate all dates in the current date range
        const allDates = [];
        if (dateRange.start && dateRange.end) {
            let currentDate = new Date(dateRange.start);
            while (currentDate <= dateRange.end) {
                allDates.push(currentDate.toISOString().split('T')[0]);
                currentDate.setDate(currentDate.getDate() + 1);
            }
        }
        
        
        // For each relevant set, create records for all dates and apply forward-fill
        relevantSets.forEach(setName => {
            
            // Get all historical data for this set for forward-fill context
            const allSetRecords = this.rawData
                .filter(item => item.set_name === setName)
                .sort((a, b) => new Date(a.date) - new Date(b.date));
            
            
            // Process each date in the range
            allDates.forEach(dateStr => {
                
                // Find existing filtered record for this set-date combination
                let existingRecord = this.filteredData.find(item => 
                    item.set_name === setName && item.date === dateStr
                );
                
                if (existingRecord) {
                    
                    // Check if this record has actual workorder data
                    const hasActualWorkorder = existingRecord.service_code_sources && 
                        Array.isArray(existingRecord.service_code_sources) && 
                        existingRecord.service_code_sources.includes('workorder');
                    
                    if (hasActualWorkorder && existingRecord.expected_units > 0) {
                        return; // Skip forward-fill for this record
                    }
                }
                
                // This date either has no record or needs forward-fill
                
                const recordDate = new Date(dateStr);
                let mostRecentWorkorder = null;
                
                // Look backwards through ALL historical data for the most recent workorder
                for (let i = allSetRecords.length - 1; i >= 0; i--) {
                    const histRecord = allSetRecords[i];
                    const histDate = new Date(histRecord.date);
                    
                    // Only consider records before or on the current date
                    if (histDate <= recordDate) {
                        const hasHistWorkorder = histRecord.service_code_sources && 
                            Array.isArray(histRecord.service_code_sources) && 
                            histRecord.service_code_sources.includes('workorder');
                        
                        const hasServiceCodeWithUnits = histRecord.service_codes && 
                            Array.isArray(histRecord.service_codes) && 
                            histRecord.service_codes.length > 0 && 
                            histRecord.expected_units > 0;
                            
                        if ((hasHistWorkorder && histRecord.expected_units > 0) || 
                            (hasServiceCodeWithUnits && !histRecord.service_code_sources?.includes('previous_workorder'))) {
                            mostRecentWorkorder = histRecord;
                            break;
                        }
                    }
                }
                
                if (mostRecentWorkorder) {
                    const forwardFillExpectedUnits = mostRecentWorkorder.expected_units;
                    
                    if (existingRecord) {
                        // Update existing record
                        existingRecord.expected_units = forwardFillExpectedUnits;
                        existingRecord.service_codes = [...new Set([...(existingRecord.service_codes || []), ...(mostRecentWorkorder.service_codes || [])])];
                        existingRecord.service_code_sources = [...new Set([...(existingRecord.service_code_sources || []), 'previous_workorder'])];
                        existingRecord.service_kpis = mostRecentWorkorder.service_kpis || [];
                        
                    } else {
                        // Create new record for this date with forward-filled data
                        
                        // Find a supervisor from the filtered data to use as a template
                        const templateSupervisor = showAllSupervisors ? 
                            (allSetRecords.find(r => r.supervisor) || {}).supervisor || 'Unknown' :
                            supervisors[0];
                        
                        const newRecord = {
                            set_name: setName,
                            supervisor: templateSupervisor,
                            date: dateStr,
                            direct_hours: 0,
                            idle_hours: 0,
                            units: 0,
                            expected_units: forwardFillExpectedUnits,
                            total_time_span: 0,
                            workorder_ids: [],
                            service_codes: mostRecentWorkorder.service_codes || [],
                            project_codes: [],
                            service_code_sources: ['previous_workorder'],
                            service_kpis: mostRecentWorkorder.service_kpis || [],
                            report_url: null,
                            fallback_report_url: null
                        };
                        
                        this.filteredData.push(newRecord);
                    }
                } else {
                }
            });
        });
        
    },

    // Recalculates and caches metrics for the currently filtered data.
    updateMetrics() {
        const setData = {};
        this.filteredData.forEach(item => {
            if (!setData[item.set_name]) {
                setData[item.set_name] = [];
            }
            setData[item.set_name].push(item);
        });

        this.metrics = {};
        Object.entries(setData).forEach(([setName, data]) => {
            this.metrics[setName] = MetricsCalculator.calculateSetMetrics(data);
        });
    },

    // Extracts a unique list of supervisors from the raw data.
    getSupervisors() {
        return [...new Set(this.rawData.map(item => item.supervisor))].filter(Boolean);
    },

    // Checks if the requested date range is outside the currently stored raw data period.
    needsDataFetch(start, end) {
        if (!this.storedPeriod.start || !this.storedPeriod.end) return true;
        return start < this.storedPeriod.start || end > this.storedPeriod.end;
    }
};