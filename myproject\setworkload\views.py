from django.shortcuts import render
from datetime import datetime, timedelta
from myproject.utils import get_bigquery_client
from django.template.defaulttags import register
from collections import defaultdict
import json
from django.core.serializers.json import DjangoJSONEncoder
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.db import models
import logging
import time
from .helper_functions import (
    validate_date_range, get_workload_query_results,
    process_workload_results, process_workload_view_data
)
from .models import WorkloadComment, WorkloadStatusType, WorkloadStatusChange

logger = logging.getLogger(__name__)

@register.filter
def get_item(dictionary, key):
    return dictionary.get(key)

def format_set_name(name):
    """Convert underscores to spaces in set names."""
    return name.replace('_', ' ')

def get_workload_data(request):
    """API endpoint for fetching workload data for a specific date range."""
    func_name = "get_workload_data"
    try:
        start_date = datetime.fromisoformat(request.GET.get('start')).date()
        end_date = datetime.fromisoformat(request.GET.get('end')).date()

        is_valid, error_msg = validate_date_range(start_date, end_date)
        if not is_valid:
            logger.warning(f"[{func_name}] {error_msg}")
            return JsonResponse({
                'error': 'Date range cannot exceed 90 days'
            }, status=400)

        results = get_workload_query_results(start_date, end_date, func_name)
        if results is None:
            return JsonResponse({
                'error': 'Database connection failed'
            }, status=500)

        json_data = process_workload_results(results, func_name)

        return JsonResponse(json_data, safe=False)

    except (ValueError, TypeError) as e:
        logger.error(f"[{func_name}] Invalid date format: {str(e)}")
        return JsonResponse({
            'error': 'Invalid date format'
        }, status=400)
    except Exception as e:
        logger.error(f"[{func_name}] Error fetching workload data: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)

def set_workload_view(request):
    """Main view for the set workload dashboard."""
    func_name = "set_workload_view"

    try:
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=89)

        results = get_workload_query_results(start_date, end_date, func_name)
        if results is None:
            return render(request, 'setworkload/setworkload_view.html', {
                'workload_data': {},
                'dates': [],
                'workload_data_json': '[]'
            })

        filtered_workload_data, dates, json_data = process_workload_view_data(
            results, start_date, end_date, func_name
        )

        try:
            workload_data_json = json.dumps(
                json_data,
                cls=DjangoJSONEncoder,
                ensure_ascii=False
            )
            json.loads(workload_data_json)
        except Exception as e:
            logger.error(f"[{func_name}] JSON serialization failed: {str(e)}")
            return render(request, 'setworkload/setworkload_view.html', {
                'workload_data': {},
                'dates': [],
                'workload_data_json': '[]'
            })

        context = {
            'workload_data': filtered_workload_data,
            'dates': dates,
            'workload_data_json': workload_data_json
        }

        return render(request, 'setworkload/setworkload_view.html', context)

    except Exception as e:
        logger.error(f"[{func_name}] Error processing workload data: {str(e)}")
        return render(request, 'setworkload/setworkload_view.html', {
            'workload_data': {},
            'dates': [],
            'workload_data_json': '[]'
        })


@login_required
@require_http_methods(["GET"])
def get_comments(request):
    """API endpoint to get comments for a specific set and date."""
    try:
        set_name = request.GET.get('set_name')
        date_str = request.GET.get('date')

        if not set_name or not date_str:
            return JsonResponse({
                'error': 'Missing required parameters: set_name and date'
            }, status=400)

        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=400)

        comments = WorkloadComment.objects.filter(
            set_name=set_name,
            date=date
        ).order_by('-created_at')

        comments_data = []
        for comment in comments:
            comments_data.append({
                'id': comment.id,
                'user_email': comment.user_email,
                'comment_text': comment.comment_text,
                'created_at': comment.created_at.isoformat(),
                'created_at_formatted': comment.created_at.strftime('%Y-%m-%d %H:%M (UTC)')
            })

        return JsonResponse({
            'success': True,
            'comments': comments_data,
            'current_user_email': request.user.email
        })

    except Exception as e:
        logger.error(f"Error fetching comments: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def add_comment(request):
    """API endpoint to add a new comment."""
    try:
        data = json.loads(request.body)

        set_name = data.get('set_name')
        date_str = data.get('date')
        comment_text = data.get('comment_text', '').strip()

        if not set_name or not date_str or not comment_text:
            return JsonResponse({
                'error': 'Missing required fields: set_name, date, and comment_text'
            }, status=400)

        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=400)

        # Create the comment
        comment = WorkloadComment.objects.create(
            user_email=request.user.email,
            set_name=set_name,
            date=date,
            comment_text=comment_text
        )

        return JsonResponse({
            'success': True,
            'comment': {
                'id': comment.id,
                'user_email': comment.user_email,
                'comment_text': comment.comment_text,
                'created_at': comment.created_at.isoformat(),
                'created_at_formatted': comment.created_at.strftime('%Y-%m-%d %H:%M (UTC)')
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        logger.error(f"Error adding comment: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_comments_bulk(request):
    """API endpoint to get comment counts for multiple sets and dates."""
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        if not start_date_str or not end_date_str:
            return JsonResponse({
                'error': 'Missing required parameters: start_date and end_date'
            }, status=400)

        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=400)

        # Get all comments in the date range
        comments = WorkloadComment.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).values('set_name', 'date').annotate(
            comment_count=models.Count('id')
        )

        # Create a dictionary for quick lookup
        comment_counts = {}
        for comment in comments:
            key = f"{comment['set_name']}_{comment['date']}"
            comment_counts[key] = comment['comment_count']

        return JsonResponse({
            'success': True,
            'comment_counts': comment_counts
        })

    except Exception as e:
        logger.error(f"Error fetching bulk comments: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_status_types(request):
    """API endpoint to get all active status types for dropdown."""
    try:
        status_types = WorkloadStatusType.objects.filter(is_active=True).order_by('status_name')
        
        status_types_data = []
        for status_type in status_types:
            status_types_data.append({
                'status_code': status_type.status_code,
                'status_name': status_type.status_name,
                'color_class': status_type.color_class,
                'description': status_type.description
            })

        return JsonResponse({
            'success': True,
            'status_types': status_types_data
        })

    except Exception as e:
        logger.error(f"Error fetching status types: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_status_changes(request):
    """API endpoint to get status changes for a specific set and date."""
    try:
        set_name = request.GET.get('set_name')
        date_str = request.GET.get('date')

        if not set_name or not date_str:
            return JsonResponse({
                'error': 'Missing required parameters: set_name and date'
            }, status=400)

        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=400)

        # Use raw SQL to completely bypass Django ORM foreign key issues
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT id, set_name, date, status_code, user_email, changed_at, previous_status_code
                FROM workload_status_changes 
                WHERE set_name = %s AND date = %s
                ORDER BY changed_at DESC 
                LIMIT 1
            """, [set_name, date])
            
            row = cursor.fetchone()
            if row:
                status_change_data = {
                    'id': row[0],
                    'set_name': row[1],
                    'date': row[2],
                    'status_code': row[3],
                    'user_email': row[4],
                    'changed_at': row[5],
                    'previous_status_code': row[6]
                }
            else:
                status_change_data = None

        if status_change_data:
            # Get the status type separately using the status_code value
            status_type = WorkloadStatusType.objects.get(status_code=status_change_data['status_code'])
            
            status_data = {
                'id': status_change_data['id'],
                'set_name': status_change_data['set_name'],
                'date': status_change_data['date'].isoformat(),
                'status_code': status_type.status_code,
                'status_name': status_type.status_name,
                'color_class': status_type.color_class,
                'user_email': status_change_data['user_email'],
                'changed_at': status_change_data['changed_at'].isoformat(),
                'changed_at_formatted': status_change_data['changed_at'].strftime('%Y-%m-%d %H:%M (UTC)'),
                'previous_status_code': status_change_data['previous_status_code']
            }
            return JsonResponse({
                'success': True,
                'status_change': status_data
            })
        else:
            return JsonResponse({
                'success': True,
                'status_change': None
            })

    except Exception as e:
        logger.error(f"Error fetching status change: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def set_status_change(request):
    """API endpoint to set/update status for a specific set and date."""
    try:
        data = json.loads(request.body)

        set_name = data.get('set_name')
        date_str = data.get('date')
        status_code = data.get('status_code')

        if not set_name or not date_str or not status_code:
            return JsonResponse({
                'error': 'Missing required fields: set_name, date, and status_code'
            }, status=400)

        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=400)

        # Verify status code exists
        try:
            status_type = WorkloadStatusType.objects.get(status_code=status_code, is_active=True)
        except WorkloadStatusType.DoesNotExist:
            return JsonResponse({
                'error': 'Invalid status code'
            }, status=400)

        # Get the most recent status change for this set/date to track previous status
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT status_code
                FROM workload_status_changes 
                WHERE set_name = %s AND date = %s
                ORDER BY changed_at DESC 
                LIMIT 1
            """, [set_name, date])
            
            row = cursor.fetchone()
            previous_status_code = row[0] if row else None

        # Always create a new status change record using raw SQL to avoid foreign key issues
        from django.db import connection
        from django.utils import timezone
        
        changed_at = timezone.now()
        with connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO workload_status_changes 
                (set_name, date, status_code, user_email, changed_at, previous_status_code)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id
            """, [set_name, date, status_code, request.user.email, changed_at, previous_status_code])
            
            status_change_id = cursor.fetchone()[0]

        return JsonResponse({
            'success': True,
            'status_change': {
                'id': status_change_id,
                'set_name': set_name,
                'date': date.isoformat(),
                'status_code': status_type.status_code,
                'status_name': status_type.status_name,
                'color_class': status_type.color_class,
                'user_email': request.user.email,
                'changed_at': changed_at.isoformat(),
                'changed_at_formatted': changed_at.strftime('%Y-%m-%d %H:%M (UTC)'),
                'previous_status_code': previous_status_code
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        logger.error(f"Error setting status change: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_status_changes_bulk(request):
    """API endpoint to get status changes for multiple sets and dates."""
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        if not start_date_str or not end_date_str:
            return JsonResponse({
                'error': 'Missing required parameters: start_date and end_date'
            }, status=400)

        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=400)

        # Get the latest status change for each set/date combination
        from django.db.models import Max
        
        # First get the latest changed_at for each set/date combination
        latest_changes = WorkloadStatusChange.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        ).values('set_name', 'date').annotate(
            latest_changed_at=Max('changed_at')
        )
        
        # Then get the actual status changes for those latest times using raw SQL
        status_changes_data = {}
        from django.db import connection
        for latest in latest_changes:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT set_name, date, status_code, user_email, changed_at
                    FROM workload_status_changes 
                    WHERE set_name = %s AND date = %s AND changed_at = %s
                    LIMIT 1
                """, [latest['set_name'], latest['date'], latest['latest_changed_at']])
                
                row = cursor.fetchone()
                if row:
                    # Get status type separately
                    status_type = WorkloadStatusType.objects.get(status_code=row[2])
                    
                    key = f"{row[0]}_{row[1]}"
                    status_changes_data[key] = {
                        'status_code': status_type.status_code,
                        'status_name': status_type.status_name,
                        'color_class': status_type.color_class,
                        'user_email': row[3],
                        'changed_at': row[4].isoformat(),
                        'changed_at_formatted': row[4].strftime('%Y-%m-%d %H:%M (UTC)')
                    }

        return JsonResponse({
            'success': True,
            'status_changes': status_changes_data
        })

    except Exception as e:
        logger.error(f"Error fetching bulk status changes: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_status_changes_history(request):
    """API endpoint to get all status changes history for a specific set and date."""
    try:
        set_name = request.GET.get('set_name')
        date_str = request.GET.get('date')

        if not set_name or not date_str:
            return JsonResponse({
                'error': 'Missing required parameters: set_name and date'
            }, status=400)

        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=400)

        # Get all status changes for this set/date using raw SQL
        from django.db import connection
        status_history = []
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT id, status_code, user_email, changed_at, previous_status_code
                FROM workload_status_changes 
                WHERE set_name = %s AND date = %s
                ORDER BY changed_at DESC
            """, [set_name, date])
            
            for row in cursor.fetchall():
                # Get status type separately
                status_type = WorkloadStatusType.objects.get(status_code=row[1])
                
                # Get previous status name for display
                previous_status_name = None
                if row[4]:  # previous_status_code
                    try:
                        prev_status = WorkloadStatusType.objects.get(status_code=row[4])
                        previous_status_name = prev_status.status_name
                    except WorkloadStatusType.DoesNotExist:
                        previous_status_name = row[4]
                
                status_history.append({
                    'id': row[0],
                    'status_code': status_type.status_code,
                    'status_name': status_type.status_name,
                    'color_class': status_type.color_class,
                    'user_email': row[2],
                    'changed_at': row[3].isoformat(),
                    'previous_status_code': row[4],
                    'previous_status_name': previous_status_name
                })

        return JsonResponse({
            'success': True,
            'status_changes': status_history
        })

    except Exception as e:
        logger.error(f"Error fetching status changes history: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)
