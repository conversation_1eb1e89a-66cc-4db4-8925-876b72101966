/**
 * Activity Performance JavaScript Module
 * Handles filtering, sorting, and dynamic progress bar updates for activity performance data
 */

// Module pattern to prevent global pollution and provide cleanup
const ActivityPerformance = (function() {
    let isInitialized = false;
    let eventHandlers = {
        statusFilter: null,
        bladeFilter: null,
        hideDelayActivities: null,
        sortByFilter: null
    };

    // Apply immediate sorting as soon as the script loads
    function applySorting() {
        const tableBody = document.getElementById('performanceTableBody');
        if (tableBody) {
            const rows = Array.from(tableBody.querySelectorAll('.performance-row'));
            if (rows.length > 0) {
                rows.sort((a, b) => {
                    const aVariance = parseFloat(a.dataset.variance) || 0;
                    const bVariance = parseFloat(b.dataset.variance) || 0;
                    return bVariance - aVariance; // Variance Descending
                });
                rows.forEach(row => tableBody.appendChild(row));
            }
        }
    }

    function cleanup() {
        if (!isInitialized) return;

        // Remove all event listeners
        const statusFilter = document.getElementById('statusFilter');
        const bladeFilter = document.getElementById('bladeFilter');
        const hideDelayActivities = document.getElementById('hideDelayActivities');
        const sortByFilter = document.getElementById('sortByFilter');

        if (statusFilter && eventHandlers.statusFilter) {
            statusFilter.removeEventListener('change', eventHandlers.statusFilter);
        }
        if (bladeFilter && eventHandlers.bladeFilter) {
            bladeFilter.removeEventListener('change', eventHandlers.bladeFilter);
        }
        if (hideDelayActivities && eventHandlers.hideDelayActivities) {
            hideDelayActivities.removeEventListener('change', eventHandlers.hideDelayActivities);
        }
        if (sortByFilter && eventHandlers.sortByFilter) {
            sortByFilter.removeEventListener('change', eventHandlers.sortByFilter);
        }

        // Clear handler references
        eventHandlers = {
            statusFilter: null,
            bladeFilter: null,
            hideDelayActivities: null,
            sortByFilter: null
        };

        isInitialized = false;
    }

    function initialize() {
        // Prevent multiple initializations
        if (isInitialized) {
            cleanup(); // Clean up existing listeners first
        }

        // Get filter elements
        const statusFilter = document.getElementById('statusFilter');
        const bladeFilter = document.getElementById('bladeFilter');
        const sortByFilter = document.getElementById('sortByFilter');
        const hideDelayActivities = document.getElementById('hideDelayActivities');
        const tableBody = document.getElementById('performanceTableBody');
        const emptyState = document.getElementById('emptyState');
        const visibleCount = document.getElementById('visibleCount');
        const rows = tableBody ? tableBody.querySelectorAll('.performance-row') : [];

        function filterTable() {
            if (!tableBody || rows.length === 0) return;

            const statusValue = statusFilter ? statusFilter.value : 'all';
            const bladeValue = bladeFilter ? bladeFilter.value : 'all';
            const hideDelayValue = hideDelayActivities ? hideDelayActivities.checked : false;

            let visibleRowCount = 0;
            let visibleOnTimeCount = 0;
            let visibleOverTimeCount = 0;
            let visibleNoLimitCount = 0;

            rows.forEach(row => {
                const rowStatus = row.dataset.status;
                const rowBlade = row.dataset.blade;
                const rowActivityType = row.dataset.activityType;

                let showRow = true;

                // Status filter
                if (statusValue === 'exclude_no_limit') {
                    // Show only over time and on time activities (exclude no_limit)
                    if (rowStatus === 'no_limit') {
                        showRow = false;
                    }
                } else if (statusValue !== 'all' && rowStatus !== statusValue) {
                    showRow = false;
                }

                // Blade filter
                if (bladeValue !== 'all' && rowBlade !== bladeValue) {
                    showRow = false;
                }

                // Hide delay activities filter
                if (hideDelayValue && rowActivityType === 'DELAY') {
                    showRow = false;
                }

                // Show/hide row and count by status
                if (showRow) {
                    row.style.display = '';
                    visibleRowCount++;
                    
                    // Count by status for progress bar update
                    if (rowStatus === 'on_time') {
                        visibleOnTimeCount++;
                    } else if (rowStatus === 'over') {
                        visibleOverTimeCount++;
                    } else if (rowStatus === 'no_limit') {
                        visibleNoLimitCount++;
                    }
                } else {
                    row.style.display = 'none';
                }
            });

            // Update visible count
            if (visibleCount) {
                visibleCount.textContent = visibleRowCount;
            }

            // Update progress bar
            updateProgressBar(visibleOnTimeCount, visibleOverTimeCount, visibleNoLimitCount, visibleRowCount);

            // Show/hide empty state
            if (visibleRowCount === 0) {
                if (tableBody) tableBody.style.display = 'none';
                if (emptyState) emptyState.style.display = 'block';
            } else {
                if (tableBody) tableBody.style.display = '';
                if (emptyState) emptyState.style.display = 'none';
            }

            // Apply current sorting after filtering
            if (sortByFilter) {
                sortTable(sortByFilter.value);
            } else {
                // Fallback to default sorting if sortByFilter not available
                sortTable('variance_desc');
            }
        }

        function updateProgressBar(onTimeCount, overTimeCount, noLimitCount, totalCount) {
            if (totalCount === 0) return;

            // Calculate percentages
            const onTimePercentage = (onTimeCount / totalCount * 100);
            const overTimePercentage = (overTimeCount / totalCount * 100);
            const noLimitPercentage = (noLimitCount / totalCount * 100);

            // Update percentage text
            const percentageText = document.getElementById('onTimePercentage');
            if (percentageText) {
                percentageText.textContent = `${onTimePercentage.toFixed(1)}% On Time`;
            }

            // Update total count
            const totalElement = document.querySelector('.flex-shrink-0 .text-lg');
            if (totalElement) {
                totalElement.textContent = totalCount;
            }

            // Update progress bar segments
            const progressBar = document.querySelector('.w-full.bg-gray-200.rounded-full.h-4.flex');
            if (progressBar) {
                // Clear existing segments - this is safe as we're replacing innerHTML
                progressBar.innerHTML = '';

                // Add On Time segment
                if (onTimeCount > 0) {
                    const onTimeSegment = document.createElement('div');
                    onTimeSegment.className = 'bg-green-500 h-4 cursor-help transition-all duration-300 hover:bg-green-600';
                    onTimeSegment.style.width = `${onTimePercentage}%`;
                    onTimeSegment.title = `On Time: ${onTimeCount} activities (${onTimePercentage.toFixed(1)}%)`;
                    progressBar.appendChild(onTimeSegment);
                }

                // Add Over Time segment
                if (overTimeCount > 0) {
                    const overTimeSegment = document.createElement('div');
                    overTimeSegment.className = 'bg-red-500 h-4 cursor-help transition-all duration-300 hover:bg-red-600';
                    overTimeSegment.style.width = `${overTimePercentage}%`;
                    overTimeSegment.title = `Over Time: ${overTimeCount} activities (${overTimePercentage.toFixed(1)}%)`;
                    progressBar.appendChild(overTimeSegment);
                }

                // Add No Limit segment
                if (noLimitCount > 0) {
                    const noLimitSegment = document.createElement('div');
                    noLimitSegment.className = 'bg-gray-400 h-4 cursor-help transition-all duration-300 hover:bg-gray-500';
                    noLimitSegment.style.width = `${noLimitPercentage}%`;
                    noLimitSegment.title = `No Limit: ${noLimitCount} activities (${noLimitPercentage.toFixed(1)}%)`;
                    progressBar.appendChild(noLimitSegment);
                }
            }
        }

        // Main sorting function that handles all sorting options
        function sortTable(sortType) {
            const tableBody = document.getElementById('performanceTableBody');
            if (!tableBody) return;

            const rows = Array.from(tableBody.querySelectorAll('.performance-row'));

            rows.sort((a, b) => {
                switch (sortType) {
                    case 'variance_desc':
                        // Variance Descending (biggest positive first)
                        const aVarianceDesc = parseFloat(a.dataset.variance) || 0;
                        const bVarianceDesc = parseFloat(b.dataset.variance) || 0;
                        return bVarianceDesc - aVarianceDesc;

                    case 'variance_asc':
                        // Variance Ascending (smallest negative first)
                        const aVarianceAsc = parseFloat(a.dataset.variance) || 0;
                        const bVarianceAsc = parseFloat(b.dataset.variance) || 0;
                        return aVarianceAsc - bVarianceAsc;

                    case 'time':
                    default:
                        // Time (from start time) - earliest first
                        const aStartTime = new Date(a.dataset.startDatetime);
                        const bStartTime = new Date(b.dataset.startDatetime);
                        return aStartTime - bStartTime;
                }
            });

            // Re-append sorted rows
            rows.forEach(row => tableBody.appendChild(row));
        }

        // Function to add tooltips to comment N/A cells for over status activities
        function addCommentTooltips() {
            const rows = document.querySelectorAll('.performance-row');

            rows.forEach((row) => {
                const status = row.dataset.status;
                // Find the comment cell (last td in the row that's visible on large screens)
                const commentCell = row.querySelector('td.hidden.lg\\:table-cell:last-child span');

                if (commentCell && commentCell.textContent.trim() === 'N/A' && status === 'over') {
                    commentCell.classList.add('cursor-help');
                    commentCell.setAttribute('title', 'No delay comment or activity properties available');
                }
            });
        }

        // Store event handler references for cleanup
        eventHandlers.statusFilter = filterTable;
        eventHandlers.bladeFilter = filterTable;
        eventHandlers.hideDelayActivities = filterTable;
        eventHandlers.sortByFilter = function() {
            sortTable(sortByFilter.value);
        };

        // Initialize filters (sorting already applied immediately above)
        filterTable();
        addCommentTooltips();

        // Add event listeners
        if (statusFilter) statusFilter.addEventListener('change', eventHandlers.statusFilter);
        if (bladeFilter) bladeFilter.addEventListener('change', eventHandlers.bladeFilter);
        if (hideDelayActivities) hideDelayActivities.addEventListener('change', eventHandlers.hideDelayActivities);
        if (sortByFilter) sortByFilter.addEventListener('change', eventHandlers.sortByFilter);

        isInitialized = true;
    }

    // Public API
    return {
        init: initialize,
        cleanup: cleanup,
        applySorting: applySorting
    };
})();

// Apply immediate sorting
ActivityPerformance.applySorting();

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    ActivityPerformance.init();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    ActivityPerformance.cleanup();
});

// Make cleanup available globally for manual cleanup
window.ActivityPerformance = ActivityPerformance;
