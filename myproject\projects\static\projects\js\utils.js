/**
 * Utility functions for project table functionality
 * These functions are used across multiple JS files for common operations
 */
export function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

export function getColumnRenderer(data, type, row, meta) {
    if (type === "display") {
        // Handle null/empty values
        if (data === null || data === " .. " || data === "") {
            return "- No data -";
        }

        // Handle boolean values
        if (typeof data === "boolean") {
            return `<div class="flex justify-center"><input type="checkbox" ${data ? 'checked' : ''} disabled></div>`;
        }

        // Get the alignment class from the column definition
        const columnClass = meta.settings.aoColumns[meta.col].className || '';
        const alignmentClass = columnClass.includes('text-left') ? 'text-left' : 
                             columnClass.includes('text-center') ? 'text-center' : 
                             columnClass.includes('text-right') ? 'text-right' : '';

        // Apply conditional formatting based on column name
        let bgColor = "";
        const columnName = meta.settings.aoColumns[meta.col].data;
        
        // Color-code duration variance values
        if (columnName === "duration_variance" && typeof data === "number") {
            bgColor = data < 0 ? "bg-green-100" :  // Ahead of schedule
                    data === 0 ? "" :              // On schedule
                    data < 10 ? "bg-orange-100" :  // Slightly behind
                    "bg-red-200";                  // Significantly behind
        } 
        // Color-code start date variance values
        else if (columnName === "start_date_variance" && typeof data === "number") {
            bgColor = data === 0 ? "bg-green-100" :      // Started on time
                    Math.abs(data) <= 4 ? "bg-orange-100" : // Minor variance
                    "bg-red-200";                        // Major variance
        }

        return `<div class="truncate rounded ${alignmentClass} ${bgColor}" title="${data}">${data}</div>`;
    }

    // For filtering/sorting, return the raw data
    return data;
}
