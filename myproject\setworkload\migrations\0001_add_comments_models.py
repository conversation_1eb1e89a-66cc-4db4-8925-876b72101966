# Generated migration for WorkloadComment model

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='WorkloadComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_email', models.Email<PERSON>ield(help_text='Email of the user who added the comment', max_length=255)),
                ('set_name', models.CharField(help_text='Name of the set this comment relates to', max_length=255)),
                ('date', models.DateField(help_text='Date this comment relates to')),
                ('comment_text', models.TextField(help_text='The actual comment text')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, help_text='When the comment was created')),
            ],
            options={
                'db_table': 'workload_comments',
                'ordering': ['created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='workloadcomment',
            index=models.Index(fields=['set_name', 'date'], name='workload_comments_set_name_date_idx'),
        ),
        migrations.AddIndex(
            model_name='workloadcomment',
            index=models.Index(fields=['user_email'], name='workload_comments_user_email_idx'),
        ),
        migrations.AddIndex(
            model_name='workloadcomment',
            index=models.Index(fields=['created_at'], name='workload_comments_created_at_idx'),
        ),
    ]
