"""Customer BigQuery client with caching capabilities."""

import datetime
import hashlib
import json
import pickle
from typing import Op<PERSON>

from google.cloud import bigquery

from .models import BigQueryCache

from django.db import IntegrityError, transaction


class BigQueryRow(dict):
    """Custom BigQuery row class to handle caching."""

    def __getattr__(self, item):
        """Return the attribute value or the dictionary value."""
        try:
            return self[item]
        except KeyError:
            raise AttributeError(
                f"'{self.__class__.__name__}' object has no attribute '{item}'"
            ) from None


class BigQueryResult:
    """Custom BigQuery result class to handle caching."""

    def __init__(self, cached_result):
        self._cached_result = cached_result
        self._index = 0

    @property
    def total_rows(self):
        """Return the total number of rows in the result."""
        return len(self._cached_result)

    def __iter__(self):
        """Return an iterator over the cached result."""
        self._index = 0
        return self

    def __next__(self):
        """Return the next item in the result."""
        while self._index < len(self._cached_result):
            item = self._cached_result[self._index]
            self._index += 1
            return item
        raise StopIteration


# pylint: disable=too-few-public-methods
class CacheableQueryJob:
    """Custom QueryJob class to handle caching."""

    def __init__(self, cached_result):
        self._cached_result = cached_result

    def result(self):
        """Return the cached result."""
        return BigQueryResult(self._cached_result)


class BigQueryClientWithCache(bigquery.Client):
    """Bigquery client that overrides query method to use a cache, if possible."""

    @staticmethod
    def get_query_args(cache: BigQueryCache) -> dict:
        """Convert query parameters to a dictionary and return them."""
        return {
            "query_str": cache.query,
            "job_config": pickle.loads(cache.job_config),
        }

    @staticmethod
    def _hash_query_params(
        query_str: str, job_config: Optional[bigquery.QueryJobConfig] = None
    ) -> str:
        """Given parameters of original query, return a hash of the query.

        Returns a tuple of the hash and the query dict.
        """
        query_params = {
            "query": query_str,
            "job_config": job_config.to_api_repr() if job_config else None,
        }
        query_str = json.dumps(query_params, sort_keys=True)
        return hashlib.sha256(query_str.encode("utf-8")).hexdigest()

    def _get_cache(
        self, query_str: str, job_config: Optional[bigquery.QueryJobConfig] = None
    ) -> Optional[bigquery.QueryJob]:
        """Retrieve cache from the given query params dict."""
        cache_key = self._hash_query_params(query_str, job_config)
        # pylint: disable=no-member
        cache_hit = BigQueryCache.objects.filter(key=cache_key).first()
        if cache_hit:
            cache_hit.last_accessed = datetime.datetime.now(tz=datetime.timezone.utc)
            cache_hit.save()
            return CacheableQueryJob(pickle.loads(cache_hit.result))
        return []

    def _set_cache(
        self,
        job: bigquery.QueryJob,
        query_str: str,
        job_config: Optional[bigquery.QueryJobConfig] = None,
    ) -> None:
        """Save cache entry with race condition protection."""

        cache_key = self._hash_query_params(query_str, job_config)

        # Memory optimization: Serialize the result efficiently
        try:
            # Use list comprehension for better memory efficiency
            result_list = [BigQueryRow(row) for row in job.result()]
            result_data = pickle.dumps(result_list)

            # Clean up the list to free memory immediately
            del result_list

        except Exception as e:
            logger.error(f"Error serializing cache data: {str(e)}")
            return

        # Use atomic transaction to prevent race conditions
        try:
            with transaction.atomic():
                # pylint: disable=no-member
                cache_hit = BigQueryCache.objects.filter(key=cache_key).first()
                if cache_hit:
                    # Update existing cache entry
                    cache_hit.result = result_data
                    cache_hit.last_updated = datetime.datetime.now(tz=datetime.timezone.utc)
                    cache_hit.save()
                else:
                    # Create new cache entry
                    cache_entry = BigQueryCache(
                        key=cache_key,
                        result=result_data,
                        query=query_str,
                        job_config=pickle.dumps(job_config),
                        last_updated=datetime.datetime.now(tz=datetime.timezone.utc),
                    )
                    cache_entry.save()
        except IntegrityError:
            # Handle race condition where another process created the cache entry
            try:
                # Try to update the existing entry that was created by another process
                with transaction.atomic():
                    cache_hit = BigQueryCache.objects.filter(key=cache_key).first()
                    if cache_hit:
                        cache_hit.result = result_data
                        cache_hit.last_updated = datetime.datetime.now(tz=datetime.timezone.utc)
                        cache_hit.save()
            except Exception:
                pass
        except Exception:
            pass

    # pylint: disable=arguments-differ
    def query(
        self,
        query_str: str,
        job_config: Optional[bigquery.QueryJobConfig] = None,
        use_cache: bool = True,
    ) -> bigquery.QueryJob:
        """
        Override the query method to use a cache if available.

        This query is limited version of the superclass query method, to make sure
        we can cache the result of the query reliably.

        Args:
            query_str: The SQL query to execute.
            job_config: The job configuration object.
            use_cache: By default we use, else we store the cache and return original job.
        """
        if use_cache:
            cached_result = self._get_cache(query_str, job_config)
            if cached_result:
                return cached_result

        job = super().query(query_str, job_config=job_config)

        # Set cache (with race condition protection)
        self._set_cache(job, query_str, job_config)

        if use_cache:
            # Try to get from cache again after setting it
            cached_result = self._get_cache(query_str, job_config)
            if cached_result:
                return cached_result
            else:
                return job
        return job
