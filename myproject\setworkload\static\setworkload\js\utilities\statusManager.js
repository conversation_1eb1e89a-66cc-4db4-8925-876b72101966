/**
 * StatusManager - <PERSON>les workload status operations
 * Manages status types, status changes, and dropdown UI interactions
 */
const StatusManager = {
    statusTypes: {},
    statusChanges: {},
    currentSetName: null,
    currentDate: null,
    isInitialized: false,

    /**
     * Initialize status types and changes
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            await this.loadStatusTypes();
            this.isInitialized = true;
        } catch (error) {
            console.error('StatusManager: Initialization failed:', error);
        }
    },

    /**
     * Load status types from API
     */
    async loadStatusTypes() {
        try {
            const response = await fetch('/setworkload/api/status-types/');
            const data = await response.json();
            
            if (data.success) {
                this.statusTypes = {};
                data.status_types.forEach(type => {
                    this.statusTypes[type.status_code] = type;
                });
            } else {
                console.error('StatusManager: Failed to load status types:', data.error);
            }
        } catch (error) {
            console.error('StatusManager: Error loading status types:', error);
        }
    },

    /**
     * Load status changes for date range
     */
    async loadStatusChanges(startDate, endDate) {
        try {
            const response = await fetch(`/setworkload/api/status-changes/bulk/?start_date=${startDate}&end_date=${endDate}`);
            const data = await response.json();
            
            if (data.success) {
                this.statusChanges = data.status_changes;
            } else {
                console.error('StatusManager: Failed to load status changes:', data.error);
            }
        } catch (error) {
            console.error('StatusManager: Error loading status changes:', error);
        }
    },

    /**
     * Get status for a specific cell
     */
    getStatusForCell(setName, date) {
        const key = `${setName}_${date}`;
        return this.statusChanges[key] || null;
    },

    /**
     * Get color class for a status code
     */
    getColorClass(statusCode) {
        const statusType = this.statusTypes[statusCode];
        return statusType ? statusType.color_class : 'bg-red-50';
    },

    /**
     * Convert Tailwind color class to actual color value for inline styles
     */
    getColorFromClass(colorClass) {
        const colorMap = {
            'bg-red-50': '#f87171',      // More visible red for dot (No Activity)
            'bg-blue-100': '#3b82f6',    // More visible blue for dot (Weather)
            'bg-purple-100': '#8b5cf6',  // More visible purple for dot (Travel)
            'bg-orange-100': '#f97316',  // More visible orange for dot (Idle)
            'bg-gray-100': '#6b7280',    // More visible gray for dot (Technical Idle)
            'bg-green-50': '#22c55e',    // More visible green for dot
            'bg-yellow-50': '#eab308'    // More visible yellow for dot
        };
        return colorMap[colorClass] || '#f87171';
    },

    /**
     * Add status button to a no-activity cell
     */
    addDropdownButton(cellElement, setName, date) {
        // Only add status button if cell doesn't already have one
        if (cellElement.querySelector('.status-button-container')) {
            return;
        }

        const iconsContainer = cellElement.querySelector('.icons-top-right-container');
        if (!iconsContainer) return;

        const statusContainer = document.createElement('div');
        statusContainer.className = 'status-button-container relative';

        const statusButton = document.createElement('button');
        // Always use default white styling (no color change for status indication)
        statusButton.className = 'flex items-center justify-center w-7 h-7 bg-white hover:bg-gray-50 text-gray-600 border border-gray-300 rounded-full shadow-md transition-all duration-150 hover:scale-110 status-icon';
        statusButton.title = "View/Change set's status for this day";
        statusButton.dataset.setName = setName;
        statusButton.dataset.date = date;
        
        // Use a status flag icon
        statusButton.innerHTML = `<svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 3v1.5M3 21v-6m0 0l2.77-.693a9 9 0 016.208.682l.108.054a9 9 0 006.086.71l3.114-.732a48.524 48.524 0 01-.005-10.499l-3.11.732a9 9 0 01-6.085-.711l-.108-.054a9 9 0 00-6.208-.682L3 4.5M3 15V4.5" />
        </svg>`;

        statusButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.openStatusPopup(setName, date);
        });

        statusContainer.appendChild(statusButton);
        iconsContainer.appendChild(statusContainer);
    },

    /**
     * Opens the status popup for a specific set and date.
     * @param {string} setName - The name of the set.
     * @param {string} date - The date in YYYY-MM-DD format.
     */
    async openStatusPopup(setName, date) {
        this.currentSetName = setName;
        this.currentDate = date;

        // Show loading popup immediately
        this.showLoadingPopup();

        try {
            // Fetch both status and history data in parallel for faster loading
            const [statusResponse, historyResponse] = await Promise.all([
                fetch(`/setworkload/api/status-changes/?set_name=${encodeURIComponent(setName)}&date=${date}`),
                fetch(`/setworkload/api/status-changes/history/?set_name=${encodeURIComponent(setName)}&date=${date}`)
            ]);

            // Process status response
            if (!statusResponse.ok) {
                this.closeStatusPopup();
                alert('Error loading status changes. Please try again.');
                return;
            }

            const statusData = await statusResponse.json();
            if (!statusData.success) {
                this.closeStatusPopup();
                alert('Error loading status changes. Please try again.');
                return;
            }

            // Process history response
            let statusHistory = [];
            if (historyResponse.ok) {
                const historyData = await historyResponse.json();
                if (historyData.success) {
                    statusHistory = historyData.status_changes || [];
                }
            }

            // Replace loading popup with actual content
            this.showStatusPopup(statusData.status_change, statusHistory);

        } catch (error) {
            this.closeStatusPopup();
            alert('Error loading status changes. Please try again.');
        }
    },

    /**
     * Shows the status popup with the current status and history.
     * @param {Object|null} currentStatus - Current status object or null.
     * @param {Array} statusHistory - Array of status change objects.
     */
    showStatusPopup(currentStatus, statusHistory) {
        // Remove existing popup if any
        this.closeStatusPopup();

        // Create popup overlay
        const overlay = document.createElement('div');
        overlay.id = 'status-popup-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.onclick = (e) => {
            if (e.target === overlay) {
                this.closeStatusPopup();
            }
        };

        // Create popup container
        const popup = document.createElement('div');
        popup.className = 'bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden';
        
        // Create popup content
        popup.innerHTML = `
            <div class="p-6">
                <!-- Header Section -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-1">
                        <h3 class="text-lg font-semibold text-gray-900">Status changes for ${this.currentSetName}</h3>
                        <button id="close-status-popup" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        On ${this.formatDate(this.currentDate)}
                    </div>
                </div>

                <!-- Current Status Section -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Current Status:</h4>
                    <div class="flex items-center">
                        <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: ${this.getColorFromClass(currentStatus ? currentStatus.color_class : 'bg-red-50')}"></span>
                        <span class="text-sm text-gray-700">${currentStatus ? currentStatus.status_name : 'No Activity'}</span>
                    </div>
                </div>

                <!-- Status History Section -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Status History:</h4>
                    <div id="status-history-list" class="max-h-40 overflow-y-auto">
                        ${this.renderStatusHistory(statusHistory)}
                    </div>
                </div>

                <!-- Change Status Section -->
                <div class="border-t pt-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Change Status:</h4>
                    ${this.renderStatusOptions()}
                </div>
            </div>
        `;

        overlay.appendChild(popup);
        document.body.appendChild(overlay);

        // Add event listeners
        this.setupStatusPopupEventListeners();
    },

    /**
     * Closes the status popup.
     */
    closeStatusPopup() {
        const overlay = document.getElementById('status-popup-overlay');
        if (overlay) {
            overlay.remove();
        }
    },

    /**
     * Shows a loading popup while data is being fetched
     */
    showLoadingPopup() {
        // Remove existing popup if any
        this.closeStatusPopup();

        // Create popup overlay
        const overlay = document.createElement('div');
        overlay.id = 'status-popup-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.onclick = (e) => {
            if (e.target === overlay) {
                this.closeStatusPopup();
            }
        };

        // Create popup container
        const popup = document.createElement('div');
        popup.className = 'bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto';

        // Create loading content
        popup.innerHTML = `
            <div class="p-6">
                <!-- Header Section -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-1">
                        <h3 class="text-lg font-semibold text-gray-900">Status changes for ${this.currentSetName}</h3>
                        <button id="close-status-popup" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        On ${this.formatDate(this.currentDate)}
                    </div>
                </div>

                <!-- Loading Section -->
                <div class="flex flex-col items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                    <div class="text-sm text-gray-500">Loading data...</div>
                </div>
            </div>
        `;

        overlay.appendChild(popup);
        document.body.appendChild(overlay);

        // Add close button event listener
        const closeBtn = document.getElementById('close-status-popup');
        closeBtn.onclick = () => this.closeStatusPopup();
    },

    /**
     * Set status for a specific set and date
     */
    async setStatus(setName, date, statusCode) {
        try {
            const response = await fetch('/setworkload/api/status-changes/set/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    set_name: setName,
                    date: date,
                    status_code: statusCode
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                alert(`Failed to update status: ${response.status} ${response.statusText}`);
                return;
            }
            
            const data = await response.json();
            
            if (data.success) {
                // Update local cache - always store the latest status change
                const key = `${setName}_${date}`;
                this.statusChanges[key] = {
                    status_code: statusCode,
                    status_name: data.status_change.status_name,
                    color_class: data.status_change.color_class,
                    user_email: data.status_change.user_email,
                    changed_at: data.status_change.changed_at,
                    changed_at_formatted: data.status_change.changed_at_formatted
                };

                // Update cell appearance
                this.updateCellStatus(setName, date, statusCode);
                
                // Trigger metrics recalculation since status change affects working days count
                if (typeof workloadStore !== 'undefined' && workloadStore.updateMetrics) {
                    workloadStore.updateMetrics();
                    // Refresh the table to show updated metrics
                    if (typeof TableManager !== 'undefined' && TableManager.updateTableWithoutServiceCodeRefresh) {
                        TableManager.updateTableWithoutServiceCodeRefresh();
                    }
                }
                
            } else {
                alert('Failed to update status: ' + data.error);
            }
        } catch (error) {
            alert('Error updating status. Please try again.');
        }
    },

    /**
     * Update cell appearance based on status
     */
    updateCellStatus(setName, date, statusCode) {
        // Find the cell
        const cell = document.querySelector(`td[data-set-name="${setName}"][data-date="${date}"]`);
        if (!cell) {
            // Try finding by position in table (alternative method)
            const table = document.querySelector('table');
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                const headerCells = table.querySelectorAll('thead th');
                
                // Find date column index
                let dateIndex = -1;
                headerCells.forEach((th, index) => {
                    if (th.textContent.includes(date) || th.dataset.date === date) {
                        dateIndex = index;
                    }
                });

                // Find set row and update cell
                if (dateIndex > 0) {
                    rows.forEach(row => {
                        const setCell = row.querySelector('.set-name-cell span');
                        if (setCell && setCell.textContent.trim() === setName) {
                            const targetCell = row.children[dateIndex];
                            if (targetCell) {
                                this.applyCellStatusUpdate(targetCell, statusCode);
                            }
                        }
                    });
                }
            }
            return;
        }

        this.applyCellStatusUpdate(cell, statusCode);
    },

    /**
     * Apply status update to a cell element
     */
    applyCellStatusUpdate(cellElement, statusCode) {
        // Update background color
        const colorClass = this.getColorClass(statusCode);
        
        // Remove existing background color classes
        cellElement.classList.remove('bg-red-50', 'bg-blue-100', 'bg-purple-100', 'bg-orange-100', 'bg-gray-100', 'bg-green-50', 'bg-yellow-50');
        
        // Add new color class
        cellElement.classList.add(colorClass);

        // Update tooltip if custom status
        if (statusCode !== 'no_activity') {
            const statusInfo = this.statusChanges[`${cellElement.dataset.setName || 'unknown'}_${cellElement.dataset.date || 'unknown'}`];
            if (statusInfo) {
                const originalTitle = cellElement.title;
                cellElement.title = `${originalTitle}\nStatus: ${statusInfo.status_name} (set by ${statusInfo.user_email} on ${statusInfo.changed_at_formatted})`;
            }
        }

        // Status button should always remain white - no color change for status indication
        const statusButton = cellElement.querySelector('.status-icon');
        if (statusButton) {
            // Always use default white styling
            statusButton.className = 'flex items-center justify-center w-7 h-7 bg-white hover:bg-gray-50 text-gray-600 border border-gray-300 rounded-full shadow-md transition-all duration-150 hover:scale-110 status-icon';
            statusButton.title = 'View/Set status for this day';
        }
    },

    /**
     * Renders the status history HTML.
     * @param {Array} statusHistory - Array of status change objects.
     * @returns {string} HTML string for status history.
     */
    renderStatusHistory(statusHistory) {
        if (statusHistory.length === 0) {
            return '<div class="text-gray-500 text-sm">No status changes yet.</div>';
        }

        return statusHistory.map(change => `
            <div class="mb-3 pb-3 border-b border-gray-100 last:border-b-0">
                <div class="flex items-start mb-2">
                    <svg class="w-4 h-4 mr-2 text-gray-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <div class="flex flex-col">
                        <span class="text-sm font-medium text-gray-900">${change.user_email}</span>
                        <span class="text-xs text-gray-500">${this.formatDateTimeWithoutTimezone(change.changed_at)}</span>
                    </div>
                </div>
                <div class="text-sm text-gray-700 ml-6">
                    Changed from <span class="font-medium">${change.previous_status_name || 'No Activity'}</span> to <span class="font-medium">${change.status_name}</span>
                </div>
            </div>
        `).join('');
    },

    /**
     * Renders the status options HTML.
     * @returns {string} HTML string for status options.
     */
    renderStatusOptions() {
        // Organize statuses by category
        const idleStatuses = [];
        const workStatuses = [];
        
        // Categorize status types
        Object.values(this.statusTypes).forEach(statusType => {
            if (statusType.status_code === 'no_activity') return;
            
            if (statusType.status_code.includes('idle') || statusType.status_name.includes('Idle')) {
                idleStatuses.push(statusType);
            } else {
                workStatuses.push(statusType);
            }
        });
        
        // Sort idle statuses with "Idle" first
        idleStatuses.sort((a, b) => {
            if (a.status_code === 'idle') return -1;
            if (b.status_code === 'idle') return 1;
            return a.status_name.localeCompare(b.status_name);
        });
        
        // Sort work statuses alphabetically
        workStatuses.sort((a, b) => a.status_name.localeCompare(b.status_name));
        
        let optionsHtml = '<div class="space-y-3">';
        
        // Group other statuses (work + reset)
        const otherStatuses = [
            ...workStatuses,
            { status_code: 'no_activity', status_name: 'No Activity', color_class: 'bg-red-50' }
        ];
        
        optionsHtml += `
            <div class="grid grid-cols-2 gap-3">
                <!-- Idle Status Column -->
                <div class="space-y-2">
        `;
        
        // Add idle statuses to first column
        idleStatuses.forEach(statusType => {
            optionsHtml += `
                <button class="status-option-btn flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors text-left w-full" 
                        data-status="${statusType.status_code}">
                    <span class="inline-block w-2.5 h-2.5 rounded-full mr-2 flex-shrink-0" style="background-color: ${this.getColorFromClass(statusType.color_class)}"></span>
                    <span class="text-xs font-medium truncate">${statusType.status_name}</span>
                </button>
            `;
        });
        
        optionsHtml += `
                </div>
                <!-- Other Status Column -->
                <div class="space-y-2">
        `;
        
        // Add other statuses to second column
        otherStatuses.forEach(statusType => {
            optionsHtml += `
                <button class="status-option-btn flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors text-left w-full" 
                        data-status="${statusType.status_code}">
                    <span class="inline-block w-2.5 h-2.5 rounded-full mr-2 flex-shrink-0" style="background-color: ${this.getColorFromClass(statusType.color_class)}"></span>
                    <span class="text-xs font-medium truncate">${statusType.status_name}</span>
                </button>
            `;
        });
        
        optionsHtml += `
                </div>
            </div>
        </div>`;
        
        return optionsHtml;
    },

    /**
     * Sets up event listeners for the status popup.
     */
    setupStatusPopupEventListeners() {
        const closeBtn = document.getElementById('close-status-popup');
        closeBtn.onclick = () => this.closeStatusPopup();

        // Add event listeners to all status option buttons
        const statusOptions = document.querySelectorAll('.status-option-btn');
        statusOptions.forEach(option => {
            // Remove existing handler if any
            if (option._statusClickHandler) {
                option.removeEventListener('click', option._statusClickHandler);
            }
            
            // Store handler reference and add listener
            option._statusClickHandler = (e) => {
                e.preventDefault();
                const statusCode = option.dataset.status;
                this.setStatusFromPopup(statusCode);
            };
            option.addEventListener('click', option._statusClickHandler);
        });
    },

    /**
     * Sets status from the popup and refreshes the popup content.
     * @param {string} statusCode - The status code to set.
     */
    async setStatusFromPopup(statusCode) {
        try {
            // Update UI immediately for better responsiveness
            this.updatePopupStatusDisplay(statusCode);

            // Then update the backend
            await this.setStatus(this.currentSetName, this.currentDate, statusCode);

            // Refresh the popup with updated data (including new history entry)
            this.openStatusPopup(this.currentSetName, this.currentDate);
        } catch (error) {
            alert('Error changing status. Please try again.');
            // Revert UI changes on error by reopening popup
            this.openStatusPopup(this.currentSetName, this.currentDate);
        }
    },

    /**
     * Updates the popup's current status display immediately for better UX
     * @param {string} statusCode - The new status code
     */
    updatePopupStatusDisplay(statusCode) {
        const statusType = this.statusTypes[statusCode];
        if (!statusType) return;

        // Update current status display
        const currentStatusSection = document.querySelector('#status-popup-overlay .mb-6:nth-child(2)');
        if (currentStatusSection) {
            const statusDisplay = currentStatusSection.querySelector('.flex.items-center');
            if (statusDisplay) {
                const colorDot = statusDisplay.querySelector('.inline-block');
                const statusText = statusDisplay.querySelector('.text-sm');

                if (colorDot && statusText) {
                    colorDot.style.backgroundColor = this.getColorFromClass(statusType.color_class);
                    statusText.textContent = statusType.status_name;
                }
            }
        }
    },

    /**
     * Formats a date string for display.
     * @param {string} dateStr - Date string in YYYY-MM-DD format.
     * @returns {string} Formatted date string.
     */
    formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        });
    },

    /**
     * Formats a datetime string in the user's timezone without timezone brackets.
     * @param {string} isoDateStr - ISO datetime string from the server.
     * @returns {string} Formatted datetime string without timezone.
     */
    formatDateTimeWithoutTimezone(isoDateStr) {
        const date = new Date(isoDateStr);

        // Get user's timezone
        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // Format the date in user's timezone
        const formattedDate = date.toLocaleDateString('en-GB', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            timeZone: userTimezone
        });

        const formattedTime = date.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
            timeZone: userTimezone
        });

        return `${formattedDate} ${formattedTime}`;
    },

    /**
     * Get CSRF token for API requests
     */
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    },

    /**
     * Check if a cell should have status dropdown (no activity cells)
     */
    shouldHaveStatusDropdown(hasActivity, hasUnits) {
        return !hasActivity && !hasUnits;
    }
};

// Initialize when DOM is loaded - but main.js will handle the full initialization
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if main.js hasn't already done it
    if (!StatusManager.isInitialized) {
        StatusManager.initialize();
    }
});