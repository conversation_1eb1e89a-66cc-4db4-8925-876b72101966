import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import random

from .telemetry_data import get_telemetry_data


def _calculate_and_format_activity_totals(df):
    """
    Calculates total time for each activity and updates task descriptions for display.
    Returns the updated DataFrame and the task description mapping.
    """
    chart_df = df.copy()
    task_description_mapping = {}

    if not chart_df.empty and 'start' in chart_df.columns and 'finish' in chart_df.columns:
        chart_df['duration'] = pd.to_datetime(chart_df['finish']) - pd.to_datetime(chart_df['start'])
        activity_totals = chart_df.groupby('task_description')['duration'].sum()

        for task_desc, total_duration in activity_totals.items():
            # Skip adding time totals for Video activities
            if task_desc == 'Video':
                task_description_mapping[task_desc] = task_desc
                continue
                
            total_seconds = total_duration.total_seconds()
            hours = int(total_seconds // 3600)
            minutes = int((total_seconds % 3600) // 60)

            if hours > 0:
                time_str = f"{hours}h {minutes}min"
            else:
                time_str = f"{minutes}min"

            time_style = (
                "font-size: 0.85em; "
                "color: #1976d2; "
                "font-weight: 500; "
                "margin-left: 8px;"
            )
            new_task_desc = f'{task_desc} <span style="{time_style}">({time_str})</span>'
            task_description_mapping[task_desc] = new_task_desc

        chart_df['task_description'] = chart_df['task_description'].map(task_description_mapping)

    return chart_df, task_description_mapping


def _add_applying_markers(fig, applying_df):
    """
    Adds scatter markers for 'Applying' presets to the Plotly figure.
    """
    if applying_df is not None and not applying_df.empty:
        timestamps = applying_df['timestamp'].tolist()
        hashes = applying_df['hash'].tolist()
        hover_texts = ["<b>Presets: Applying</b><br><b>Time:</b> " +
                      ts.strftime('%Y-%m-%d %H:%M:%S') + "<br>Click to view data"
                      for ts in timestamps]

        fig.add_trace(go.Scatter(
            x=timestamps,
            y=["Presets: Applying"] * len(timestamps),
            mode="markers",
            marker=dict(
                symbol="square",
                size=10,
                color="green",
                line=dict(width=1, color="black")
            ),
            name="Presets: Applying",
            text=hover_texts,
            hoverinfo="text",
            customdata=hashes,
            showlegend=True
        ))


def _add_initializing_markers(fig, initializing_df):
    """
    Adds scatter markers for 'Initializing' presets to the Plotly figure.
    """
    if initializing_df is not None and not initializing_df.empty:
        timestamps = initializing_df['timestamp'].tolist()
        # Use timestamp as customdata for grouped initializations
        timestamp_strings = [ts.strftime('%Y-%m-%d %H:%M:%S.%f') for ts in timestamps]
        
        # Create hover text showing count of presets for each timestamp
        hover_texts = []
        for idx, ts in enumerate(timestamps):
            count = initializing_df.iloc[idx]['count'] if 'count' in initializing_df.columns else 1
            preset_word = "preset" if count == 1 else "presets"
            hover_text = f"<b>Presets: Initializing</b><br><b>Time:</b> {ts.strftime('%Y-%m-%d %H:%M:%S')}"
            hover_text += f"<br><b>Count:</b> {count} {preset_word}"
            hover_text += "<br>Click to view data"
            hover_texts.append(hover_text)

        fig.add_trace(go.Scatter(
            x=timestamps,
            y=["Presets: Initializing"] * len(timestamps),
            mode="markers",
            marker=dict(
                symbol="diamond",
                size=10,
                color="grey",
                line=dict(width=1, color="black")
            ),
            name="Presets: Initializing",
            text=hover_texts,
            hoverinfo="text",
            customdata=timestamp_strings,  # Use timestamp string for grouping
            showlegend=True
        ))


def _reorder_activities_for_ui(categories):
    """
    Reorders activities for UI display, specifically moving 
    "LER-SPA: Preset application" to be above "SPA Extruder: ON".
    """
    if not categories:
        return categories
    
    categories_copy = categories.copy()
    ler_spa_preset = "LER-SPA: Preset application"
    spa_extruder = "SPA Extruder: ON"
    
    # Find the positions of both activities (handle formatted names with time suffixes)
    ler_spa_index = None
    spa_extruder_index = None
    
    for i, activity in enumerate(categories_copy):
        if activity.startswith(ler_spa_preset):
            ler_spa_index = i
        elif activity.startswith(spa_extruder):
            spa_extruder_index = i
    
    # If both activities exist and LER-SPA is currently below SPA Extruder, reorder
    if (ler_spa_index is not None and spa_extruder_index is not None and 
        ler_spa_index > spa_extruder_index):
        
        # Remove LER-SPA from its current position
        ler_spa_activity = categories_copy.pop(ler_spa_index)
        
        # Find the new position of SPA Extruder after removal
        spa_extruder_new_index = None
        for i, activity in enumerate(categories_copy):
            if activity.startswith(spa_extruder):
                spa_extruder_new_index = i
                break
        
        # Insert LER-SPA just above SPA Extruder
        if spa_extruder_new_index is not None:
            categories_copy.insert(spa_extruder_new_index, ler_spa_activity)
    
    return categories_copy


def _configure_chart_layout(fig, title, set_code):
    """
    Configures the layout for the Plotly figure.
    """
    fig.update_layout(
        height=600,
        yaxis_title="Task",
        margin=dict(l=50, r=50, t=50, b=50),
        title=dict(
            text=title or f"Telemetry Data for {set_code}",
            x=0.06,
            xanchor='left',
            font=dict(size=16)
        ),
        autosize=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=-0.15,
            xanchor="left",
            x=0
        )
    )


def _create_video_activities_for_workorder_34320():
    """
    Creates specific video activities for workorder ID 34320.
    6 activities on June 13, 2025, with predefined times and details.
    """
    # Define the 6 specific activities
    activities_data = [
        {
            'activity_type': 'Filler',
            'blade': 'A',
            'start': datetime(2025, 6, 14, 9, 19, 0),
            'end': datetime(2025, 6, 14, 9, 23, 0),
            'video_type': 'Filler Video',
            'videos': {
                'main': 'Main_Bottom_Filler_NXUS-4146_H2_WGV1-0521_A_2025.06.14_09_19_09_23_h264.mp4',
                'secondary1': '2nd_Side_Filler_NXUS-4146_H2_WGV1-0521_A_2025.06.14_09_19_09_23_h264.mp4',
                'secondary2': '3rd_Cartridges_Filler_NXUS-4146_H2_WGV1-0521_A_2025.06.14_09_19_09_23_h264.mp4'
            },
            'video_labels': {
                'main': 'Bottom View',
                'secondary1': 'Side View',
                'secondary2': 'Cartridges View'
            }
        },
        {
            'activity_type': 'Grinding',
            'blade': 'B', 
            'start': datetime(2025, 6, 13, 10, 41, 0),
            'end': datetime(2025, 6, 13, 10, 44, 0),
            'video_type': 'Grinding video',
            'videos': {
                'main': 'Main_Bottom_Grinding_NXUS-4146_H2_WGV1-0521_B_2025.06.13_10_41_10_44_h264.mp4',
                'secondary1': '2nd_Top_Grinding_NXUS-4146_H2_WGV1-0521_B_2025.06.13_10_41_10_44_h264.mp4',
                'secondary2': '3rd_Toolbase_Grinding_NXUS-4146_H2_WGV1-0521_B_2025.06.13_10_41_10_44_h264.mp4'
            },
            'video_labels': {
                'main': 'Bottom View',
                'secondary1': 'Top View',
                'secondary2': 'Toolbase View'
            }
        },
        {
            'activity_type': 'Cleaning',
            'blade': 'B',
            'start': datetime(2025, 6, 13, 11, 37, 0),
            'end': datetime(2025, 6, 13, 11, 40, 0),
            'video_type': 'Cleaning video',
            'videos': {
                'main': 'Main_Sid_Cleaning_NXUS-4146_H2_WGV1-0521_B_2025.06.13_11_37_11_40_h264.mp4',
                'secondary1': '2nd_Top_Cleaning_NXUS-4146_H2_WGV1-0521_B_2025.06.13_11_37_11_40_h264.mp4',
                'secondary2': '3rd_Toolbase_Cleaning_NXUS-4146_H2_WGV1-0521_B_2025.06.13_11_37_11_40_h264.mp4'
            },
            'video_labels': {
                'main': 'Side View',
                'secondary1': 'Top View',
                'secondary2': 'Toolbase View'
            }
        },
        {
            'activity_type': 'LEP',
            'blade': 'B',
            'start': datetime(2025, 6, 13, 12, 9, 0),
            'end': datetime(2025, 6, 13, 12, 13, 0),
            'video_type': 'LEP Video',
            'videos': {
                'main': 'Main_Bottom_LEP_NXUS-4146_H2_WGV1-0521_B_2025.06.13_12_09_12_13_h264.mp4',
                'secondary1': '2nd_Side_LEP_NXUS-4146_H2_WGV1-0521_B_2025.06.13_12_09_12_13_h264.mp4',
                'secondary2': '3rd_Cartridges_LEP_NXUS-4146_H2_WGV1-0521_B_2025.06.13_12_09_12_13_h264.mp4'
            },
            'video_labels': {
                'main': 'Bottom View',
                'secondary1': 'Side View',
                'secondary2': 'Cartridges View'
            }
        },
        {
            'activity_type': 'Filler',
            'blade': 'C',
            'start': datetime(2025, 6, 13, 14, 3, 0),
            'end': datetime(2025, 6, 13, 14, 7, 0),
            'video_type': 'Filler Video',
            'videos': {
                'main': 'Main_Bottom_Filler_NXUS-4146_H2_WGV1-0521_C_2025.06.13_14_03_14_07_h264.mp4',
                'secondary1': '2nd_Side_Filler_NXUS-4146_H2_WGV1-0521_C_2025.06.13_14_03_14_07_h264.mp4',
                'secondary2': '3rd_Cartridges_Filler_NXUS-4146_H2_WGV1-0521_C_2025.06.13_14_03_14_07_h264.mp4'
            },
            'video_labels': {
                'main': 'Bottom View',
                'secondary1': 'Side View',
                'secondary2': 'Cartridges View'
            }
        },
        {
            'activity_type': 'LEP',
            'blade': 'C',
            'start': datetime(2025, 6, 13, 16, 22, 0),
            'end': datetime(2025, 6, 13, 16, 26, 0),
            'video_type': 'LEP Video',
            'videos': {
                'main': 'Main_Bottom_LEP_NXUS-4146_H2_WGV1-0521_C_2025.06.13_16_22_16_26_h264.mp4',
                'secondary1': '2nd_Side_LEP_NXUS-4146_H2_WGV1-0521_C_2025.06.13_16_22_16_26_h264.mp4',
                'secondary2': '3rd_Cartridges_LEP_NXUS-4146_H2_WGV1-0521_C_2025.06.13_16_22_16_26_h264.mp4'
            },
            'video_labels': {
                'main': 'Bottom View',
                'secondary1': 'Side View',
                'secondary2': 'Cartridges View'
            }
        }
    ]
    
    # Create video activities list
    video_activities = []
    
    for activity_data in activities_data:
        # Create hover text with activity details
        hover_text = (
            f"<b>Blade:</b> {activity_data['blade']}<br>"
            f"<b>Activity:</b> {activity_data['activity_type']}<br>"
            f"<b>Start:</b> {activity_data['start'].strftime('%Y-%m-%d %H:%M:%S')}<br>"
            f"<b>End:</b> {activity_data['end'].strftime('%Y-%m-%d %H:%M:%S')}<br>"
            f"<i>Click to view videos</i>"
        )
        
        video_activities.append({
            'task_description': 'Video',
            'start': activity_data['start'],
            'finish': activity_data['end'],
            'hover_text': hover_text,
            'video_type': activity_data['video_type'],
            'blade': activity_data['blade'],
            'activity_type': activity_data['activity_type'],
            'videos': activity_data['videos'],
            'video_labels': activity_data['video_labels']
        })
    
    return pd.DataFrame(video_activities)


def generate_telemetry_chart(df, category_orders, title=None, applying_df=None, initializing_df=None, preset_details_data=None, workorder_id=None):
    """
    Generates a Plotly timeline chart from telemetry data.

    Args:
        df (pandas.DataFrame): The telemetry data
        category_orders (dict): Category orders for the chart
        title (str, optional): The chart title
        applying_df (pandas.DataFrame, optional): The preset applications data with groups
        preset_details_data (dict, optional): Structured preset data for table display
        workorder_id (int, optional): The workorder ID for special handling

    Returns:
        tuple: (chart_html, preset_details_data) - HTML representation of the chart and preset details
    """
    # Handle empty dataframe case
    original_df_empty = df.empty
    
    # Add video activities for workorder 34320
    if workorder_id == 34320:
        video_df = _create_video_activities_for_workorder_34320()
        if original_df_empty:
            df = video_df
        else:
            df = pd.concat([video_df, df], ignore_index=True)

    if df.empty:
        return None, {}

    try:
        # Calculate activity totals and format task descriptions
        chart_df, task_description_mapping = _calculate_and_format_activity_totals(df)

        # Update category orders to include preset rows, video row, and mapped task descriptions
        preset_applying_row_name = "Presets: Applying"
        preset_initializing_row_name = "Presets: Initializing"
        video_row_name = "Video"

        has_applying_data = applying_df is not None and not applying_df.empty
        has_initializing_data = initializing_df is not None and not initializing_df.empty
        has_preset_data = has_applying_data or has_initializing_data
        has_video_data = workorder_id == 34320

        # Build category order with Video at the top
        category_list = []
        
        if has_video_data:
            category_list.append(video_row_name)
            
        if has_preset_data:
            category_list.extend([preset_applying_row_name, preset_initializing_row_name])
            
        if "task_description" in category_orders:
            # Only include non-video tasks in the mapped categories
            original_categories = [task for task in category_orders["task_description"] if task != video_row_name]
            mapped_categories = [task_description_mapping.get(original_task, original_task)
                                 for original_task in original_categories]
            # Apply reordering logic to ensure LER-SPA appears above SPA Extruder
            reordered_categories = _reorder_activities_for_ui(mapped_categories)
            category_list.extend(reordered_categories)

        updated_category_orders = {"task_description": category_list} if category_list else category_orders

        # Separate video and non-video data for different handling
        if workorder_id == 34320 and 'hover_text' in chart_df.columns:
            video_df = chart_df[chart_df['task_description'] == 'Video'].copy()
            non_video_df = chart_df[chart_df['task_description'] != 'Video'].copy()
        else:
            video_df = pd.DataFrame()
            non_video_df = chart_df

        # Create the timeline chart with non-video data first
        fig = px.timeline(
            non_video_df,
            x_start="start",
            x_end="finish",
            y="task_description",
            category_orders=updated_category_orders,
            template="seaborn",
        )

        # Add video activities separately with red color and custom hover
        if workorder_id == 34320 and not video_df.empty:
            # Create individual traces for each video activity to ensure proper metadata handling
            for i, (_, video_row) in enumerate(video_df.iterrows()):
                # Create a single-row dataframe for this activity
                single_activity_df = pd.DataFrame([video_row])
                
                # Create a timeline chart for this single activity
                single_video_fig = px.timeline(
                    single_activity_df,
                    x_start="start",
                    x_end="finish", 
                    y="task_description",
                    template="seaborn"
                )
                
                # Update the trace for this video activity
                for trace in single_video_fig.data:
                    trace.marker.color = 'red'
                    trace.hovertemplate = '%{customdata}<extra></extra>'
                    trace.customdata = [video_row['hover_text']]
                    trace.hoverinfo = 'skip'
                    
                    # Add metadata for this specific video activity
                    trace.meta = {
                        'blade': video_row.get('blade', ''),
                        'activity_type': video_row.get('activity_type', ''),
                        'start': video_row['start'].strftime('%Y-%m-%d %H:%M:%S'),
                        'end': video_row['finish'].strftime('%Y-%m-%d %H:%M:%S'),
                        'video_type': video_row.get('video_type', ''),
                        'videos': video_row.get('videos', {}),
                        'video_labels': video_row.get('video_labels', {}),
                        'index': i
                    }
                    
                    # Add this trace to the main figure
                    fig.add_trace(trace)

        # Add preset markers
        _add_applying_markers(fig, applying_df)
        _add_initializing_markers(fig, initializing_df)

        # Configure the layout
        _configure_chart_layout(fig, title, chart_df.get('set_code', [None])[0]) # Safely access set_code

        # Convert to HTML
        chart_html = fig.to_html(full_html=False, include_plotlyjs='cdn')

        # Memory cleanup: Explicitly delete large objects
        del fig
        if not chart_df.empty:
            del chart_df
        if not video_df.empty:
            del video_df
        if not non_video_df.empty:
            del non_video_df

        return chart_html, preset_details_data or {}
    except Exception as e:
        logger.error(f"Error generating telemetry chart: {str(e)}")
        return None, {}