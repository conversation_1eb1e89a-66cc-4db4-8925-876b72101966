from django.shortcuts import render, redirect
from django.utils import timezone
from datetime import datetime
import pytz
import json
from django.core.serializers.json import DjangoJSONEncoder
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from .helper_functions import fetch_robotic_sets_data, fetch_sets_activity_past_days
from .models import RoboticSet
from .weather_service import WeatherService, WeatherServiceError

class SetJSONEncoder(DjangoJSONEncoder):
    """
    Custom JSON encoder for RoboticSet objects.
    Handles serialization of RoboticSet instances for API responses,
    formatting fields like name, time, and activity details.
    """
    def default(self, obj):
        if isinstance(obj, RoboticSet):
            return {
                'name': obj.name.replace(' set', ''),  # Remove the word "set" from the name
                'project_code': obj.display_project_code,
                'country': obj.country,
                'timezone': obj.timezone_name,
                'timezone_city': obj.timezone_city,
                'activity_time': obj.activity_time,
                'time_since_activity': obj.time_since_activity,
                'is_daytime': obj.is_daytime,
                'local_time': obj.local_time.strftime('%H:%M'),  # Format local time as HH:MM
                'activity_details': obj.activity_details,
                'project_details': obj.project_details,
            }
        return super().default(obj)

def dashboard(request):
    """
    Main dashboard view showing robotic sets status.

    Flow:
    1. Fetches current data for all active sets from BigQuery
    2. Creates RoboticSet objects to process and organize the data
    3. Groups sets by status (red, orange, green) based on their activity
    4. Applies region/country filtering if specified

    Display rules:
    - RED sets (attention needed) are shown first
    - ORANGE sets (review needed) are shown second
    - GREEN sets (active/normal) are shown last

    Context includes:
    - Current time (UTC and formatted)
    - Sets grouped by status
    - Total number of sets
    - Available regions for filtering
    - Currently selected region
    """
    try:
        # Fetch raw data from BigQuery
        bq_data = fetch_robotic_sets_data()
        raw_sets_data = bq_data['sets']
        last_sync_time = bq_data['last_sync_time']

        # Fetch past days activity data
        past_days_data = fetch_sets_activity_past_days()

        # Convert raw data to RoboticSet objects and collect countries
        active_sets = []
        countries = set()
        for data in raw_sets_data:
            set_obj = RoboticSet(
                name=data['set_name'].replace(' set', ''),
                project_code=data['project_name'],
                country=data['country'],
                timezone_name=data['timezone'],
                last_activity=data['last_activity'],
                local_time=data['local_time'],
                minutes_worked=data.get('minutes_worked'),
                service_code=data.get('service_code'),
                activity_name=data.get('activity_name'),
                activity_type=data.get('activity_type'),
                start_datetime=data.get('start_datetime'),
                past_days_activity=past_days_data.get(data['set_name'].replace(' set', '')),
                project_start_date=data.get('project_start_date'),
                project_end_date=data.get('project_end_date')
            )
            active_sets.append(set_obj)
            if data['country']:
                countries.add(data['country'])

        def clean_filter_param(param_value):
            """Clean and validate filter parameter values."""
            if not param_value:
               return []
            values = param_value.split(',')
            # Strip whitespace and filter out empty/invalid values
            cleaned = [s.strip() for s in values if s.strip() and len(s.strip()) <= 100]
            return cleaned[:50]  # Limit to prevent abuse
        
        # Extract and clean filter parameters
        selected_regions = clean_filter_param(request.GET.get('region', ''))
        selected_sets = clean_filter_param(request.GET.get('sets', ''))
        selected_service_codes = clean_filter_param(request.GET.get('service_codes', ''))
        selected_projects = clean_filter_param(request.GET.get('projects', ''))


        # Collect unique values for dropdown options
        all_sets = set()
        all_service_codes = set()
        all_projects = set()

        for data in raw_sets_data:
            set_name = data['set_name'].replace(' set', '')
            all_sets.add(set_name)
            if data.get('service_code'):
                all_service_codes.add(data['service_code'])
            if data.get('project_name'):
                all_projects.add(data['project_name'])

        # Apply filters to active_sets
        if selected_sets:
            active_sets = [s for s in active_sets if s.name in selected_sets]

        if selected_service_codes:
            active_sets = [s for s in active_sets if s.service_code and s.service_code in selected_service_codes]

        if selected_projects:
            active_sets = [s for s in active_sets if s.project_code and s.project_code in selected_projects]

        # Filter by regions if specified
        if selected_regions:
            active_sets = [s for s in active_sets if s.country and s.country in selected_regions]

        # Group sets by status after filtering
        grouped_sets = {
            'red': [],
            'orange': [],
            'green': []
        }

        for set_obj in active_sets:
            status = set_obj.status_color.replace('border-', '').split('-')[0]
            if status == 'red':
                grouped_sets['red'].append(set_obj)
            elif status in ['orange']:
                grouped_sets['orange'].append(set_obj)
            elif status == 'green':
                grouped_sets['green'].append(set_obj)

        context = {
            'current_time_utc': last_sync_time.isoformat(),
            'current_time': last_sync_time.strftime('%Y-%m-%d %I:%M:%S %p'),
            'grouped_sets': grouped_sets,
            'total_sets': sum(len(sets) for sets in grouped_sets.values()),
            'regions': sorted(list(countries)),
            'selected_regions': selected_regions,
            # Filter options for dropdowns
            'all_sets': sorted(list(all_sets)),
            'all_service_codes': sorted(list(all_service_codes)),
            'all_projects': sorted(list(all_projects)),
            # Selected filter values
            'selected_sets': selected_sets,
            'selected_service_codes': selected_service_codes,
            'selected_projects': selected_projects,
        }

        return render(request, 'dashboard.html', context)

    except Exception as e:
        # In case of error, return an error context
        current_time_utc = timezone.now()
        context = {
            'current_time_utc': current_time_utc.isoformat(),  # ISO format for JavaScript
            'current_time': current_time_utc.strftime('%Y-%m-%d %I:%M:%S %p'),  # Fallback format
            'error_message': str(e),
            'active_sets': [],
            'regions': [],
            'selected_region': 'All',
            'active_sets_json': '[]'
        }
        return render(request, 'dashboard.html', context)

def get_timezone_for_country(country):
    """
    Get a default timezone for a country.
    Used as a fallback when the time_zone field is not available in the data.

    The mapping covers major countries and their primary/capital city timezones.
    For countries with multiple timezones, typically uses the timezone of the capital
    or most populous city.

    Args:
        country (str): Full country name (e.g., 'United States')

    Returns:
        str: Timezone string (e.g., 'America/New_York') or None if country not found
    """
    country_timezone_map = {
        'United States': 'America/New_York',
        'China': 'Asia/Shanghai',
        'India': 'Asia/Kolkata',
        'Australia': 'Australia/Sydney',
        'United Kingdom': 'Europe/London',
        'Germany': 'Europe/Berlin',
        'France': 'Europe/Paris',
        'Spain': 'Europe/Madrid',
        'Italy': 'Europe/Rome',
        'Brazil': 'America/Sao_Paulo',
        'Canada': 'America/Toronto',
        'Japan': 'Asia/Tokyo',
        'Russia': 'Europe/Moscow',
        'Mexico': 'America/Mexico_City',
        'South Korea': 'Asia/Seoul',
        'Turkey': 'Europe/Istanbul',
        'Indonesia': 'Asia/Jakarta',
        'Netherlands': 'Europe/Amsterdam',
        'Saudi Arabia': 'Asia/Riyadh',
        'Switzerland': 'Europe/Zurich',
        'Argentina': 'America/Argentina/Buenos_Aires',
        'Sweden': 'Europe/Stockholm',
        'Poland': 'Europe/Warsaw',
        'Belgium': 'Europe/Brussels',
        'Thailand': 'Asia/Bangkok',
        'Austria': 'Europe/Vienna',
        'Norway': 'Europe/Oslo',
        'United Arab Emirates': 'Asia/Dubai',
        'Denmark': 'Europe/Copenhagen',
        'Singapore': 'Asia/Singapore',
        'Malaysia': 'Asia/Kuala_Lumpur',
        'Philippines': 'Asia/Manila',
        'Ireland': 'Europe/Dublin',
        'Pakistan': 'Asia/Karachi',
        'Israel': 'Asia/Jerusalem',
        'Greece': 'Europe/Athens',
        'Portugal': 'Europe/Lisbon',
        'Czech Republic': 'Europe/Prague',
        'Romania': 'Europe/Bucharest',
        'New Zealand': 'Pacific/Auckland',
        'Vietnam': 'Asia/Ho_Chi_Minh',
        'Chile': 'America/Santiago',
        'Finland': 'Europe/Helsinki',
        'Hungary': 'Europe/Budapest',
        'Bangladesh': 'Asia/Dhaka',
        'Egypt': 'Africa/Cairo',
        'South Africa': 'Africa/Johannesburg',
        'Ukraine': 'Europe/Kiev',
        'Colombia': 'America/Bogota',
        'Serbia': 'Europe/Belgrade',
        'Lithuania': 'Europe/Vilnius',
        'Latvia': 'Europe/Riga',
        'Estonia': 'Europe/Tallinn',
        'Croatia': 'Europe/Zagreb',
        'Slovenia': 'Europe/Ljubljana',
        'Bulgaria': 'Europe/Sofia',
        'Slovakia': 'Europe/Bratislava',
    }

    return country_timezone_map.get(country)

def reset_filters(_request: HttpRequest) -> HttpResponse:
    """
    Reset all filters and redirect to the dashboard view.
    This clears any region filtering that might be applied.
    """
    return redirect('sets_monitoring_app:dashboard')


def get_country_region(country):
    """
    Maps a country to its geographical region.
    Used for regional filtering and grouping of sets.

    Region codes:
    - NA: North America
    - SA: South America
    - EU: Europe
    - APAC: Asia Pacific
    - ME: Middle East
    - OCE: Oceania
    - AF: Africa
    - RU: Russia (special case due to spanning multiple regions)

    Args:
        country (str): Full country name

    Returns:
        str: Region code or 'Unknown' if country not found
    """
    country_region_map = {
        # North America
        'United States': 'NA',
        'Canada': 'NA',
        'Mexico': 'NA',

        # South America
        'Brazil': 'SA',
        'Argentina': 'SA',
        'Chile': 'SA',
        'Colombia': 'SA',

        # Europe
        'United Kingdom': 'EU',
        'Germany': 'EU',
        'France': 'EU',
        'Spain': 'EU',
        'Italy': 'EU',
        'Netherlands': 'EU',
        'Switzerland': 'EU',
        'Sweden': 'EU',
        'Poland': 'EU',
        'Belgium': 'EU',
        'Austria': 'EU',
        'Norway': 'EU',
        'Denmark': 'EU',
        'Ireland': 'EU',
        'Greece': 'EU',
        'Portugal': 'EU',
        'Czech Republic': 'EU',
        'Romania': 'EU',
        'Finland': 'EU',
        'Hungary': 'EU',
        'Ukraine': 'EU',
        'Serbia': 'EU',
        'Lithuania': 'EU',
        'Latvia': 'EU',
        'Estonia': 'EU',
        'Croatia': 'EU',
        'Slovenia': 'EU',
        'Bulgaria': 'EU',
        'Slovakia': 'EU',

        # Asia
        'China': 'APAC',
        'India': 'APAC',
        'Japan': 'APAC',
        'South Korea': 'APAC',
        'Indonesia': 'APAC',
        'Thailand': 'APAC',
        'Singapore': 'APAC',
        'Malaysia': 'APAC',
        'Philippines': 'APAC',
        'Pakistan': 'APAC',
        'Vietnam': 'APAC',
        'Bangladesh': 'APAC',

        # Middle East
        'Turkey': 'ME',
        'Saudi Arabia': 'ME',
        'United Arab Emirates': 'ME',
        'Israel': 'ME',

        # Oceania
        'Australia': 'OCE',
        'New Zealand': 'OCE',

        # Africa
        'Egypt': 'AF',
        'South Africa': 'AF',

        # Russia (spans multiple regions)
        'Russia': 'RU',
    }

    return country_region_map.get(country, 'Unknown')


@require_http_methods(["GET"])
def weather_forecast_api(request, set_name):
    """
    API endpoint to get 3-day weather forecast for a robotic set.

    Args:
        request: HTTP request object
        set_name: Name of the robotic set

    Returns:
        JsonResponse with weather forecast data or error message
    """
    try:
        weather_service = WeatherService()
        forecast_data = weather_service.get_3day_forecast(set_name)

        return JsonResponse({
            'success': True,
            'data': forecast_data
        })

    except WeatherServiceError as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=503)

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'An unexpected error occurred. Please try again later.'
        }, status=500)
