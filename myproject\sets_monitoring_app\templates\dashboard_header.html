<!-- Dashboard Header -->
<div class="flex flex-col gap-4 justify-between items-start">
    <div class="w-full">
        <h1 class="mb-1 text-2xl font-bold text-gray-800 sm:text-3xl">Real Time Set Status + Weather Forecast</h1>
        <div class="flex flex-col gap-1">
            <p class="text-sm text-gray-600">Last updated: <span id="localTime">{{ current_time }}</span></p>
            <div class="flex flex-wrap gap-2">
                <span
                    class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-gray-800 whitespace-nowrap bg-gray-300 rounded-full">
                    {{ total_sets }} active sets
                </span>
                <span
                    class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-red-700 whitespace-nowrap bg-red-50 rounded-full">
                    <span class="mr-1.5 w-2 h-2 bg-red-600 rounded-full"></span>
                    {{ grouped_sets.red|length }} attention
                </span>
                <span
                    class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-orange-700 whitespace-nowrap bg-orange-50 rounded-full">
                    <span class="mr-1.5 w-2 h-2 bg-orange-500 rounded-full"></span>
                    {{ grouped_sets.orange|length }} review
                </span>
                <span
                    class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-green-700 whitespace-nowrap bg-green-50 rounded-full">
                    <span class="mr-1.5 w-2 h-2 bg-green-600 rounded-full"></span>
                    {{ grouped_sets.green|length }} active
                </span>
            </div>
        </div>
        {% if error_message %}
        <div class="p-2 mt-2 text-red-700 bg-red-100 rounded border border-red-400">
            {{ error_message }}
        </div>
        {% endif %}
    </div>
</div>