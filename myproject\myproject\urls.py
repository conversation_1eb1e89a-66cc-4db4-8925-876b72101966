"""
URL configuration for myproject project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path

from querycache import views as querycache_view
from workorders import views

urlpatterns = [
    path("admin/", admin.site.urls),
    path("", views.workorder_view, name="home"),
    path("workorders/", include("workorders.urls")),
    path("projects/", include("projects.urls")),
    path("anomalymonitor/", include("anomalymonitor.urls")),
    path("setworkload/", include("setworkload.urls")),
    path("payroll/", include("fsm_payroll.urls")),
    path("", include("mozilla_django_oidc.urls")),  # Base OIDC URLs
    path("oidc/", include(("aeroauth.urls", "aeroauth"), namespace="aeroauth")),
    path("sets-monitoring/", include("sets_monitoring_app.urls")),
    path("refresh-cache/", querycache_view.refresh_cache_view, name="project_refresh_cache"),
]


# if settings.DEBUG:
#     import debug_toolbar
#     urlpatterns += debug_toolbar.toolbar.debug_toolbar_urls()


if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
