/**
 * Handles the auto-open functionality for workorders
 * This is used when clicking on a day cube in the setworkload page
 */
var WorkorderAutoOpen = (function() {
  // Private variables
  var shouldAutoOpenFirst = false;

  // Initialize the module
  function init() {
    checkAutoOpenParameters();
    clearStaleFlags();
  }

  // Check URL parameters to determine if we should auto-open
  function checkAutoOpenParameters() {
    // First check if we're already on a workorder detail page
    // If there's a selected workorder in the context, we don't need to auto-open
    if (document.querySelector('.selected-workorder-detail')) {
      shouldAutoOpenFirst = false;
      return;
    }

    // Check if we came from a direct_to_first server-side redirect
    // If so, we don't need to do another client-side redirect
    var urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get("from_direct_to_first") === "true") {
      shouldAutoOpenFirst = false;
      return;
    }

    // Otherwise, check if we should auto-open based on URL parameters
    var autoOpenParam = urlParams.get("auto_open_first");
    shouldAutoOpenFirst = (autoOpenParam === "true");

    // Check if we came from setworkload
    var referrer = document.referrer;
    var cameFromSetworkload = referrer.includes('/setworkload/');
    var allParams = {};
    urlParams.forEach(function(value, key) {
      allParams[key] = value;
    });

    if (cameFromSetworkload && allParams['set_name'] && allParams['activity_date']) {
      shouldAutoOpenFirst = true;
    }

    // Check sessionStorage as a backup method
    try {
      var storedAutoOpen = sessionStorage.getItem('auto_open_first_workorder');
      var timestamp = parseInt(sessionStorage.getItem('auto_open_timestamp') || '0');
      var currentTime = Date.now();
      var timeDiff = currentTime - timestamp;

      // Always clear the sessionStorage flags to prevent loops
      sessionStorage.removeItem('auto_open_first_workorder');
      sessionStorage.removeItem('auto_open_timestamp');

      // Only use the stored value if it's recent (within the last 10 seconds)
      if (storedAutoOpen === 'true' && timeDiff < 10000) {
        shouldAutoOpenFirst = true;
      }
    } catch (e) {
      // Error checking sessionStorage
    }
  }

  // Clear stale redirect flags
  function clearStaleFlags() {
    if (shouldAutoOpenFirst) {
      localStorage.removeItem('workorder_redirected');
      localStorage.removeItem('workorder_redirect_time');
    } else {
      var lastRedirectTime = parseInt(localStorage.getItem('workorder_redirect_time') || '0');
      var currentTime = Date.now();
      var timeSinceLastRedirect = currentTime - lastRedirectTime;

      if (timeSinceLastRedirect > 10000) {
        localStorage.removeItem('workorder_redirected');
        localStorage.removeItem('workorder_redirect_time');
      }
    }
  }

  // Handle the DataTable's drawCallback event
  function handleDrawCallback(api, info) {
    // Remove the auto_open_first parameter from the URL to prevent loops
    if (window.location.search.includes('auto_open_first')) {
      var currentUrl = new URL(window.location.href);
      currentUrl.searchParams.delete('auto_open_first');
      history.replaceState(null, "", currentUrl.toString());
    }

    // If we have records and should auto-open, use a direct approach
    if (shouldAutoOpenFirst && info.recordsTotal > 0) {
      // Check if we've already redirected to prevent loops
      var hasRedirected = localStorage.getItem('workorder_redirected');

      if (hasRedirected !== 'true') {
        // Set the flag to prevent future redirects
        localStorage.setItem('workorder_redirected', 'true');
        localStorage.setItem('workorder_redirect_time', Date.now().toString());

        // Clear the flag after 10 seconds to allow future redirects
        setTimeout(function() {
          localStorage.removeItem('workorder_redirected');
        }, 10000);

        setTimeout(function() {
          try {
            // Try to get the first row data directly
            var firstRowData = api.row(0).data();

            if (firstRowData && firstRowData.detail_url) {
              // Construct the URL with parameters
              var url = new URL(firstRowData.detail_url, window.location.origin);
              url.searchParams.append("project_name", $("#project_name").val());
              url.searchParams.append("service_code", $("#service_code").val());
              // Get the set name value and log it for debugging
              var setName = $("#set_name").val();
              url.searchParams.append("set_name", setName);
              url.searchParams.append("workorder_name", $("#workorder_name").val());
              url.searchParams.append("activity_date", $("#activity_date").val());
              url.searchParams.append("page", 1);

              window.location.href = url.toString();
            }
          } catch (e) {
            // Error in drawCallback handler
          }
        }, 1000);
      }
    }
  }

  // Handle the AJAX complete event
  function handleAjaxComplete() {
    if (shouldAutoOpenFirst) {
      // Check if we've already redirected to prevent loops
      var hasRedirected = localStorage.getItem('workorder_redirected');

      if (hasRedirected !== 'true') {
        // Set the flag to prevent future redirects
        localStorage.setItem('workorder_redirected', 'true');
        localStorage.setItem('workorder_redirect_time', Date.now().toString());

        // Use a more direct approach here
        setTimeout(function() {
          try {
            // Try to get the first row data directly
            var table = $('#workorders-table').DataTable();
            var firstRowData = table.row(0).data();

            if (firstRowData && firstRowData.detail_url) {
              // Construct the URL with parameters
              var url = new URL(firstRowData.detail_url, window.location.origin);
              url.searchParams.append("project_name", $("#project_name").val());
              url.searchParams.append("service_code", $("#service_code").val());
              // Get the set name value and log it for debugging
              var setName = $("#set_name").val();
              url.searchParams.append("set_name", setName);
              url.searchParams.append("workorder_name", $("#workorder_name").val());
              url.searchParams.append("activity_date", $("#activity_date").val());
              url.searchParams.append("page", 1);

              window.location.href = url.toString();
            } else {
              // Clear the flag if we don't redirect
              localStorage.removeItem('workorder_redirected');
            }
          } catch (e) {
            // Clear the flag if there's an error
            localStorage.removeItem('workorder_redirected');
          }
        }, 1000);
      }
    }
  }

  // Handle the DataTable's init.dt event
  function handleInitEvent() {
    if (shouldAutoOpenFirst) {
      // Clear any existing redirect flags to ensure we can redirect
      localStorage.removeItem('workorder_redirected');

      // Wait a short time for the table to be fully rendered
      setTimeout(function() {
        try {
          // Get the DataTable instance
          var table = $('#workorders-table').DataTable();
          var info = table.page.info();

          if (info.recordsTotal > 0) {
            // Get the first row data
            var firstRowData = table.row(0).data();

            if (firstRowData && firstRowData.detail_url) {
              // Set the redirect flag
              localStorage.setItem('workorder_redirected', 'true');
              localStorage.setItem('workorder_redirect_time', Date.now().toString());

              // Remove auto_open_first from URL
              var currentUrl = new URL(window.location.href);
              currentUrl.searchParams.delete('auto_open_first');
              history.replaceState(null, "", currentUrl.toString());

              // Construct the URL with parameters
              var url = new URL(firstRowData.detail_url, window.location.origin);
              url.searchParams.append("project_name", $("#project_name").val());
              url.searchParams.append("service_code", $("#service_code").val());
              // Get the set name value and log it for debugging
              var setName = $("#set_name").val();
              url.searchParams.append("set_name", setName);
              url.searchParams.append("workorder_name", $("#workorder_name").val());
              url.searchParams.append("activity_date", $("#activity_date").val());
              url.searchParams.append("page", 1);

              window.location.href = url.toString();
            }
          }
        } catch (e) {
          // Error in init.dt handler
        }
      }, 500);
    }
  }

  // Setup a backup approach for auto-opening
  function setupBackupApproach() {
    if (shouldAutoOpenFirst) {
      setTimeout(function() {
        // Check if we've already redirected to prevent loops
        var hasRedirected = localStorage.getItem('workorder_redirected');

        if (hasRedirected !== 'true') {
          try {
            var table = $('#workorders-table').DataTable();
            var info = table.page.info();

            if (info.recordsTotal > 0) {
              // Set the flag to prevent future redirects
              localStorage.setItem('workorder_redirected', 'true');
              localStorage.setItem('workorder_redirect_time', Date.now().toString());

              // Try to get the first row data directly
              var firstRowData = table.row(0).data();

              if (firstRowData && firstRowData.detail_url) {
                // Remove the auto_open_first parameter from the URL
                var currentUrl = new URL(window.location.href);
                currentUrl.searchParams.delete('auto_open_first');
                history.replaceState(null, "", currentUrl.toString());

                // Construct the URL with parameters
                var url = new URL(firstRowData.detail_url, window.location.origin);
                url.searchParams.append("project_name", $("#project_name").val());
                url.searchParams.append("service_code", $("#service_code").val());
                // Get the set name value and log it for debugging
                var setName = $("#set_name").val();
                url.searchParams.append("set_name", setName);
                url.searchParams.append("workorder_name", $("#workorder_name").val());
                url.searchParams.append("activity_date", $("#activity_date").val());
                url.searchParams.append("page", 1);

                window.location.href = url.toString();
              } else {
                // Clear the flag if we don't redirect
                localStorage.removeItem('workorder_redirected');
              }
            } else {
              // Clear the flag if there are no records
              localStorage.removeItem('workorder_redirected');
            }
          } catch (e) {
            // Clear the flag if there's an error
            localStorage.removeItem('workorder_redirected');
          }
        }
      }, 2000); // Backup approach with a medium delay
    }
  }

  // Public API
  return {
    init: init,
    handleDrawCallback: handleDrawCallback,
    handleAjaxComplete: handleAjaxComplete,
    handleInitEvent: handleInitEvent,
    setupBackupApproach: setupBackupApproach
  };
})();
