/**
 * Workorder Summary Manager
 * Handles loading and displaying summary data for visible workorders
 */
const WorkorderSummary = {
    isInitialized: false,
    currentData: [],
    currentAverages: {},
    currentMedians: {},
    currentOrder: [], // Store the display order from DataTable

    init() {
        if (this.isInitialized) return;
        this.isInitialized = true;
        
        // Wait for DataTable to be fully loaded before loading summary data
        this.waitForDataTable();
    },

    /**
     * Wait for DataTable to be ready and then load summary data
     */
    waitForDataTable() {
        const maxAttempts = 50; // 5 seconds max wait
        let attempts = 0;
        
        const checkDataTable = () => {
            attempts++;
            
            try {
                const table = $('#workorders-table').DataTable();
                const tableData = table.data();
                
                if (tableData && tableData.length > 0) {
                    this.loadSummaryData();
                    return;
                }
            } catch (error) {
                // DataTable not ready yet
            }
            
            if (attempts < maxAttempts) {
                setTimeout(checkDataTable, 100);
            } else {
                // Timeout waiting for DataTable
                this.showEmptyState();
            }
        };
        
        checkDataTable();
    },

    /**
     * Load summary data for currently visible workorders
     */
    loadSummaryData() {
        // Get visible workorder IDs from the DataTable
        const visibleWorkorderIds = this.getVisibleWorkorderIds();
        
        if (visibleWorkorderIds.length === 0) {
            this.showEmptyState();
            return;
        }

        this.showLoadingState();

        // Prepare form data
        const formData = new FormData();
        visibleWorkorderIds.forEach(id => {
            formData.append('workorder_ids[]', id);
        });

        // Make AJAX request
        fetch(workorderSummaryDataUrl, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': this.getCSRFToken()
            }
        })
        .then(response => {
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            this.currentData = data.data || [];
            this.currentAverages = data.averages || {};
            this.currentMedians = data.medians || {};
            this.currentOrder = this.getVisibleWorkorderOrder(); // Store the order
            this.displaySummaryData();
        })
        .catch(error => {
            this.showErrorState();
        });
    },

    /**
     * Get workorder IDs that are currently visible in the DataTable
     * Returns them in the same order as they appear in the table
     */
    getVisibleWorkorderIds() {
        const workorderIds = [];
        
        try {
            const table = $('#workorders-table').DataTable();
            const visibleData = table.rows({page: 'current'}).data();
            
            // Extract workorder IDs from the visible rows in order
            for (let i = 0; i < visibleData.length; i++) {
                const rowData = visibleData[i];
                
                // Use the workorder_id field directly from the data
                if (rowData.workorder_id) {
                    workorderIds.push(parseInt(rowData.workorder_id));
                }
            }
        } catch (error) {
            // Error extracting workorder IDs
        }
        
        return workorderIds;
    },

    /**
     * Get workorder names in the same order as they appear in the DataTable
     */
    getVisibleWorkorderOrder() {
        const workorderOrder = [];
        
        try {
            const table = $('#workorders-table').DataTable();
            const visibleData = table.rows({page: 'current'}).data();
            
            // Extract workorder IDs and names in display order
            for (let i = 0; i < visibleData.length; i++) {
                const rowData = visibleData[i];
                if (rowData.workorder_id) {
                    workorderOrder.push({
                        id: parseInt(rowData.workorder_id),
                        name: rowData.wo_name,
                        order: i
                    });
                }
            }
        } catch (error) {
            // Error extracting workorder order
        }
        
        return workorderOrder;
    },

    /**
     * Display the summary data in the UI
     */
    displaySummaryData() {
        const summaryContent = document.getElementById('summary-content');
        const summaryEmpty = document.getElementById('summary-empty');
        
        // Hide loading/error states
        this.hideLoadingState();
        this.hideErrorState();

        if (this.currentData.length === 0) {
            this.showEmptyState();
            return;
        }

        // Update table first (creates the metrics elements)
        this.updateTable();

        // Update averages (now that elements exist)
        this.updateAverages();

        // Show content
        summaryContent.classList.remove('hidden');
        summaryEmpty.classList.add('hidden');
    },

    /**
     * Update the averages and medians display
     */
    updateAverages() {
        // Update averages
        const directDisplay = document.getElementById('avg-direct-display');
        const delayDisplay = document.getElementById('avg-delay-display');
        const totalBladeDisplay = document.getElementById('avg-total-blade-display');
        const commonDisplay = document.getElementById('avg-common-display');
        
        // Only update if elements exist (they're created when the table is populated)
        if (directDisplay) {
            directDisplay.textContent = this.formatHours(this.currentAverages.direct_minutes);
        }
        if (delayDisplay) {
            delayDisplay.textContent = this.formatHours(this.currentAverages.delay_minutes);
        }
        if (totalBladeDisplay) {
            totalBladeDisplay.textContent = this.formatHours(this.currentAverages.total_blade_minutes);
        }
        if (commonDisplay) {
            commonDisplay.textContent = this.formatHours(this.currentAverages.common_minutes);
        }

        // Update medians
        const medianDirectDisplay = document.getElementById('median-direct-display');
        const medianDelayDisplay = document.getElementById('median-delay-display');
        const medianTotalBladeDisplay = document.getElementById('median-total-blade-display');
        const medianCommonDisplay = document.getElementById('median-common-display');
        
        if (medianDirectDisplay) {
            medianDirectDisplay.textContent = this.formatHours(this.currentMedians.direct_minutes);
        }
        if (medianDelayDisplay) {
            medianDelayDisplay.textContent = this.formatHours(this.currentMedians.delay_minutes);
        }
        if (medianTotalBladeDisplay) {
            medianTotalBladeDisplay.textContent = this.formatHours(this.currentMedians.total_blade_minutes);
        }
        if (medianCommonDisplay) {
            medianCommonDisplay.textContent = this.formatHours(this.currentMedians.common_minutes);
        }
    },

    /**
     * Update the workorders table
     */
    updateTable() {
        const tableBody = document.getElementById('summary-table-body');
        tableBody.innerHTML = '';
        
        // Ensure summary table header height matches workorder list table header  
        const summaryTable = tableBody.closest('table');
        if (summaryTable) {
            const headerCells = summaryTable.querySelectorAll('thead th');
            headerCells.forEach(th => {
                // Match the exact height from DataTable headers (px-3 py-2 = 6px + 8px top/bottom = 28px + line height)
                th.style.height = '36px';
                th.style.lineHeight = '20px';
            });
        }

        this.currentOrder.forEach((orderItem, index) => {
            // Find the corresponding data for this workorder
            const workorderData = this.currentData.find(data => data.workorder_id === orderItem.id);
            
            const row = document.createElement('tr');
            // Set consistent height to match workorder list rows
            row.style.height = '4.5rem';
            
            // Use workorderData for hours (with fallbacks)
            // Match the exact padding and styling of the aerotask performance table
            let rowHtml = `
                <td class="px-3 py-2 align-top text-sm text-center font-normal text-gray-900">
                    ${this.formatHours(workorderData ? workorderData.direct_minutes : 0)}
                </td>
                <td class="px-3 py-2 align-top text-sm text-center font-normal text-gray-900">
                    ${this.formatHours(workorderData ? workorderData.delay_minutes : 0)}
                </td>
                <td class="px-3 py-2 align-top text-sm text-center font-normal text-gray-900">
                    ${this.formatHours(workorderData ? workorderData.blade_a_minutes : 0)}
                </td>
                <td class="px-3 py-2 align-top text-sm text-center font-normal text-gray-900">
                    ${this.formatHours(workorderData ? workorderData.blade_b_minutes : 0)}
                </td>
                <td class="px-3 py-2 align-top text-sm text-center font-normal text-gray-900">
                    ${this.formatHours(workorderData ? workorderData.blade_c_minutes : 0)}
                </td>
                <td class="px-3 py-2 align-top text-sm text-center font-normal text-gray-900">
                    ${this.formatHours(workorderData ? workorderData.common_minutes : 0)}
                </td>
            `;

            // Add metrics column - only for the first row (spanning all rows)
            if (index === 0) {
                rowHtml += `
                    <td class="sticky right-0 z-20 bg-gray-50 border-l border-gray-200 px-0 py-0 text-sm w-[220px] sm:w-[350px] min-w-[220px] sm:min-w-[350px]" rowspan="${this.currentOrder.length}">
                        <div class="flex flex-col h-full">
                            <!-- Average Section (Top Half) -->
                            <div class="flex-1 px-2 sm:px-4 py-3 flex flex-col justify-center items-center">
                                <div class="flex justify-center mb-2 relative group">
                                    <div class="relative">
                                        <h4 class="text-sm font-bold text-gray-800">AVERAGE</h4>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="absolute -top-1 -right-5 w-4 h-4 text-blue-500 cursor-help">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                                        </svg>
                                    </div>
                                    <!-- Tooltip -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999]">
                                        <div class="text-center">
                                            <div class="font-semibold">Average = Sum of all values ÷ Count of values</div>
                                            <div class="text-gray-300 mt-1">Excludes workorders with all zero values</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-y-1 gap-x-2 sm:gap-x-3 text-xs sm:text-sm w-full max-w-xs">
                                    <span class="text-gray-700 whitespace-nowrap font-medium">Direct Hours:</span>
                                    <span class="tabular-nums text-right text-gray-900 font-semibold" id="avg-direct-display">-</span>
                                    <span class="text-gray-700 whitespace-nowrap font-medium">Delay Hours:</span>
                                    <span class="tabular-nums text-right text-gray-900 font-semibold" id="avg-delay-display">-</span>
                                    <span class="text-gray-700 whitespace-nowrap font-medium">Per Blade:</span>
                                    <span class="tabular-nums text-right text-gray-900 font-semibold" id="avg-total-blade-display">-</span>
                                    <span class="text-gray-700 whitespace-nowrap font-medium">Common Hours:</span>
                                    <span class="tabular-nums text-right text-gray-900 font-semibold" id="avg-common-display">-</span>
                                </div>
                            </div>
                            
                            <!-- Horizontal Divider - Full Width -->
                            <div class="w-full h-px bg-gray-200"></div>
                            
                            <!-- Median Section (Bottom Half) -->
                            <div class="flex-1 px-2 sm:px-4 py-3 flex flex-col justify-center items-center">
                                <div class="flex justify-center mb-2 relative group">
                                    <div class="relative">
                                        <h4 class="text-sm font-bold text-gray-800">MEDIAN</h4>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="absolute -top-1 -right-5 w-4 h-4 text-blue-500 cursor-help">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                                        </svg>
                                    </div>
                                    <!-- Tooltip -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999]">
                                        <div class="text-center">
                                            <div class="font-semibold">Median = Middle value when all values are sorted</div>
                                            <div class="text-gray-300 mt-1">Includes all workorders and all values</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-y-1 gap-x-2 sm:gap-x-3 text-xs sm:text-sm w-full max-w-xs">
                                    <span class="text-gray-700 whitespace-nowrap font-medium">Direct Hours:</span>
                                    <span class="tabular-nums text-right text-gray-900 font-semibold" id="median-direct-display">-</span>
                                    <span class="text-gray-700 whitespace-nowrap font-medium">Delay Hours:</span>
                                    <span class="tabular-nums text-right text-gray-900 font-semibold" id="median-delay-display">-</span>
                                    <span class="text-gray-700 whitespace-nowrap font-medium">Per Blade:</span>
                                    <span class="tabular-nums text-right text-gray-900 font-semibold" id="median-total-blade-display">-</span>
                                    <span class="text-gray-700 whitespace-nowrap font-medium">Common Hours:</span>
                                    <span class="tabular-nums text-right text-gray-900 font-semibold" id="median-common-display">-</span>
                                </div>
                            </div>
                        </div>
                    </td>
                `;
            }
            
            row.innerHTML = rowHtml;
            tableBody.appendChild(row);
        });
    },

    /**
     * Format hours for display (e.g., 5.1 becomes "5h 06min")
     */
    formatHours(minutes) {
        if (minutes === null || minutes === undefined || minutes === 0) {
            return '0h 00min';
        }
        
        // Handle decimal minutes for averages - round to nearest minute for display
        const totalMinutes = Math.round(minutes);
        
        // Convert minutes to hours and minutes (same logic as metadata format_hours function)
        const hours = Math.floor(totalMinutes / 60);
        const remainingMinutes = totalMinutes % 60;
        
        // Format minutes with leading zero if needed
        const formattedMinutes = remainingMinutes.toString().padStart(2, '0');
        
        return `${hours}h ${formattedMinutes}min`;
    },

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * Show loading state
     */
    showLoadingState() {
        document.getElementById('summary-loading').classList.remove('hidden');
        document.getElementById('summary-content').classList.add('hidden');
        document.getElementById('summary-error').classList.add('hidden');
        document.getElementById('summary-empty').classList.add('hidden');
    },

    /**
     * Hide loading state
     */
    hideLoadingState() {
        document.getElementById('summary-loading').classList.add('hidden');
    },

    /**
     * Show error state
     */
    showErrorState() {
        document.getElementById('summary-loading').classList.add('hidden');
        document.getElementById('summary-content').classList.add('hidden');
        document.getElementById('summary-error').classList.remove('hidden');
    },

    /**
     * Hide error state
     */
    hideErrorState() {
        document.getElementById('summary-error').classList.add('hidden');
    },

    /**
     * Show empty state
     */
    showEmptyState() {
        document.getElementById('summary-loading').classList.add('hidden');
        document.getElementById('summary-error').classList.add('hidden');
        document.getElementById('summary-content').classList.add('hidden');
        document.getElementById('summary-empty').classList.remove('hidden');
    },

    /**
     * Get CSRF token from cookies
     */
    getCSRFToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    },

    /**
     * Refresh summary data (called when pagination or filters change)
     */
    refresh() {
        this.loadSummaryData();
    }
};

// Initialize when DOM is ready
$(document).ready(function() {
    // Only initialize if we're on a page with the summary container
    if (document.getElementById('workorder-summary-container')) {
        WorkorderSummary.init();
    }
});

// Export for global access
window.WorkorderSummary = WorkorderSummary; 