import cv2
import numpy as np
import logging
from .debug import log_info

logger = logging.getLogger(__name__)

def align_images(img1, img2):
    """Align images using ORB features"""
    # Ensure images are the same size before alignment
    height, width = img1.shape[:2]
    img2_resized = resize_image(img2, width, height)
    
    # Convert images to grayscale
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2_resized, cv2.COLOR_BGR2GRAY)
    
    # Use ORB detector to find keypoints and descriptors
    orb = cv2.ORB_create()
    keypoints1, descriptors1 = orb.detectAndCompute(gray1, None)
    keypoints2, descriptors2 = orb.detectAndCompute(gray2, None)
    
    # Handle case where no keypoints are found
    if descriptors1 is None or descriptors2 is None:
        logger.warning("No keypoints found, returning resized image without alignment")
        return img2_resized
    
    # Match descriptors using BFMatcher
    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
    matches = bf.match(descriptors1, descriptors2)
    matches = sorted(matches, key=lambda x: x.distance)
    
    # Ensure we have enough matches
    if len(matches) < 4:
        logger.warning("Not enough matches found, returning resized image without alignment")
        return img2_resized
    
    # Extract location of good matches
    points1 = np.zeros((len(matches), 2), dtype=np.float32)
    points2 = np.zeros((len(matches), 2), dtype=np.float32)
    
    for i, match in enumerate(matches):
        points1[i, :] = keypoints1[match.queryIdx].pt
        points2[i, :] = keypoints2[match.trainIdx].pt
    
    try:
        # Find homography
        h, mask = cv2.findHomography(points2, points1, cv2.RANSAC)
        
        if h is None:
            logger.warning("Could not find homography, returning resized image without alignment")
            return img2_resized
        
        # Use homography to warp img2 to align with img1
        aligned_img2 = cv2.warpPerspective(img2_resized, h, (width, height))
        return aligned_img2
    except Exception as e:
        logger.error(f"Error during alignment: {str(e)}")
        return img2_resized

def resize_image(img, target_width, target_height):
    """Resize image to target dimensions while maintaining aspect ratio"""
    current_height, current_width = img.shape[:2]
    
    # Calculate scaling factors
    width_ratio = target_width / current_width
    height_ratio = target_height / current_height
    
    # Use the smaller ratio to maintain aspect ratio
    scale = min(width_ratio, height_ratio)
    
    # Calculate new dimensions
    new_width = int(current_width * scale)
    new_height = int(current_height * scale)
    
    # Resize the image
    resized = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    # Create a black canvas of target size
    result = np.zeros((target_height, target_width, 3), dtype=np.uint8)
    
    # Calculate padding
    x_offset = (target_width - new_width) // 2
    y_offset = (target_height - new_height) // 2
    
    # Place the resized image in the center
    result[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
    
    return result

def load_image_from_file(file_data):
    """Load image from file data into numpy array"""
    img_array = np.frombuffer(file_data.read(), np.uint8)
    img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
    if img is None:
        raise ValueError(f'Could not read image data')
    return img

def validate_image_dimensions(images):
    """Determine target dimensions for image comparison"""
    # Get all image dimensions
    dimensions = [(img.shape[1], img.shape[0]) for img in images.values()]  # width, height
    
    # Find the maximum dimensions to ensure no information is lost
    max_width = max(dim[0] for dim in dimensions)
    max_height = max(dim[1] for dim in dimensions)
    
    logger.info(f"Target dimensions determined: {max_width}x{max_height}")
    return (max_height, max_width)  # Return in height, width format for OpenCV
