const MetricsCalculator = {
    /**
     * Calculates performance metrics for a single set's data.
     * @param {Array} setData - Array of data records for a single set
     * @returns {Object|null} Metrics object with totalVWTG, avgVWTGPerDay, avgHoursPerVWTG, percentageExpectedDone
     */
    calculateSetMetrics(setData) {
        if (!setData || setData.length === 0) return null;

        const totalVWTG = setData.reduce((sum, day) => sum + (day.units || 0), 0);
        const totalDays = setData.length;
        // Uses direct_hours for Hours per vWTG calculation.
        const directHours = setData.reduce((sum, day) => sum + (day.direct_hours || 0), 0);

        // Calculate new percentage metric
        const percentageExpectedDone = this.calculatePercentageExpectedDone(setData);

        return {
            totalVWTG,
            avgVWTGPerDay: totalVWTG / totalDays,
            avgHoursPerVWTG: totalVWTG > 0 ? directHours / totalVWTG : 0,
            percentageExpectedDone
        };
    },

    /**
     * Calculates the percentage of expected units done.
     * Formula: (Total Units Done / (Working Days × Service Speed KPI)) × 100%
     * @param {Array} setData - Array of data records for a single set
     * @returns {number} Percentage of expected units completed
     */
    calculatePercentageExpectedDone(setData) {
        if (!setData || setData.length === 0) return 0;

        // 1. Get total units done
        const totalUnits = setData.reduce((sum, day) => sum + (day.units || 0), 0);

        // 2. Calculate working days (exclude Weather/Travel days)
        const workingDays = this.calculateWorkingDays(setData);

        // 3. Get Service Speed KPI (expected units per day)
        const serviceSpeedKPI = this.getServiceSpeedKPI(setData);

        // 4. Calculate percentage
        if (workingDays === 0 || serviceSpeedKPI === 0) return 0;
        const expectedTotal = workingDays * serviceSpeedKPI;
        return (totalUnits / expectedTotal) * 100;
    },

    /**
     * Calculates the number of working days, excluding Weather, Travel, and Customer Idle days.
     * @param {Array} setData - Array of data records for a single set
     * @returns {number} Number of working days
     */
    calculateWorkingDays(setData) {
        if (!setData || setData.length === 0) return 0;

        return setData.filter(day => {
            // Check if this day has a status change that excludes it
            const status = this.getStatusForDay(day.set_name, day.date);
            return !status || !['weather', 'travel', 'customer_idle'].includes(status.status_code);
        }).length;
    },

    /**
     * Gets the Service Speed KPI (expected units per day) for the set.
     * Uses the most recent expected_units value from the date range.
     * @param {Array} setData - Array of data records for a single set
     * @returns {number} Service Speed KPI (expected units per day)
     */
    getServiceSpeedKPI(setData) {
        if (!setData || setData.length === 0) return 0;

        let latestExpectedUnits = 0;
        let latestDate = null;

        setData.forEach(day => {
            if (day.expected_units > 0 && (!latestDate || day.date > latestDate)) {
                latestExpectedUnits = day.expected_units;
                latestDate = day.date;
            }
        });

        return latestExpectedUnits;
    },

    /**
     * Gets the status for a specific day, using StatusManager if available.
     * @param {string} setName - The set name
     * @param {string} date - The date in YYYY-MM-DD format
     * @returns {Object|null} Status object or null if no status
     */
    getStatusForDay(setName, date) {
        // Check if StatusManager is available and has the method
        if (typeof StatusManager !== 'undefined' && StatusManager.getStatusForCell) {
            return StatusManager.getStatusForCell(setName, date);
        }
        return null;
    }
}; 