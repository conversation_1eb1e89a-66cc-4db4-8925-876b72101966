/**
 * Main DataTable initialization for the workorders list
 */
$(document).ready(function () {
  var urlParams = new URLSearchParams(window.location.search);
  var currentPage = parseInt(urlParams.get("page")) || 1;

  // Initialize the auto-open functionality
  WorkorderAutoOpen.init();

  // Handle form submission to properly serialize multiple select values
  $("#workorder-form").on("submit", function(e) {
    e.preventDefault();

    // Create a new URL with the current path
    var url = new URL(window.location.pathname, window.location.origin);

    // Get form values
    var projectNames = $("#project_name").val() || [];
    var serviceCodes = $("#service_code").val() || [];
    var setNames = $("#set_name").val() || [];
    var customers = $("#customer").val() || [];
    var workorderName = $("#workorder_name").val();
    var activityDate = $("#activity_date").val();

    // Add each selected value to the URL
    projectNames.forEach(function(value) {
      if (value) url.searchParams.append("project_name", value);
    });

    serviceCodes.forEach(function(value) {
      if (value) url.searchParams.append("service_code", value);
    });

    setNames.forEach(function(value) {
      if (value) url.searchParams.append("set_name", value);
    });

    customers.forEach(function(value) {
      if (value) url.searchParams.append("customer", value);
    });

    // Add other form values
    if (workorderName) url.searchParams.append("workorder_name", workorderName);
    if (activityDate) url.searchParams.append("activity_date", activityDate);

    // Redirect to the new URL
    window.location.href = url.toString();
  });

  $("#workorders-table").DataTable({
    serverSide: false,  // Changed to client-side pagination
    pageLength: 15,
    lengthChange: false,
    searching: false,
    info: true,
    ordering: false,
    processing: false,
    autoWidth: false,  // Prevent automatic column width adjustment
    displayStart: (currentPage - 1) * 15,
    pagingType: "simple",
    language: {
      info: "Showing _START_ to _END_ of _TOTAL_ workorders",
      infoEmpty: "Showing 0 to 0 of 0 workorders",
      infoFiltered: "(filtered from _MAX_ total workorders)",
      loadingRecords: '<div class="flex flex-col items-center justify-center py-8 px-4 bg-white"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div><p class="text-gray-600 text-sm">Loading workorders...</p></div>',
      paginate: {
        previous: 'Previous',
        next: 'Next'
      }
    },
    drawCallback: function () {
      var api = this.api();
      var currentPage = Math.ceil((api.page.info().start + 1) / 15);
      var info = api.page.info();
      var visibleRows = api.rows({page: 'current'}).nodes().length;
      var maxRows = api.page.len();

      // Create custom pagination
      WorkorderPagination.createPagination(this, api, currentPage, info);

      // Ensure the wrapper has full width
      var $wrapper = $(this).closest('.dataTables_wrapper');
      $wrapper.addClass('w-full max-w-full flex flex-col');

      // Always ensure the table takes appropriate space
      // Modify the wrapper to use flex with column direction
      $wrapper.addClass('flex flex-col');

      // Create a container for the table that maintains consistent height
      var $tableContainer = $('.dataTables_scrollBody');

      // Always maintain consistent height regardless of row count
      // This prevents height jumping when switching between pages with different numbers of rows
      $tableContainer.addClass('flex-grow');

      // Ensure pagination is always directly below the table
      $('.dataTables_wrapper .dataTables_paginate').addClass('w-full max-w-full').css('margin-top', '0');
      $('.dataTables_wrapper .dataTables_info').addClass('w-full max-w-full');
      
      // Remove any gaps between table and pagination
      $('.dataTables_scrollBody').css('margin-bottom', '0');
      $('#workorders-table').css('margin-bottom', '0');
      
      // Override DataTable default borders to match gray theme
      $('#workorders-table').css('border-color', '#e5e7eb');
      $('#workorders-table thead th').css('border-color', '#e5e7eb');
      $('#workorders-table tbody td').css('border-color', '#e5e7eb');
      $('#workorders-table tbody tr').css('border-color', '#e5e7eb');
      
      // Ensure consistent font weight for headers (override any DataTable defaults)
      $('#workorders-table thead th').css('font-weight', '500'); // font-medium

      // Force consistent row heights to prevent height jumping due to text wrapping
      // Using larger height to accommodate full workorder names with wrapping
      $('#workorders-table tbody tr').each(function() {
        $(this).css('height', '4.5rem');
      });



      // Update URL with current page
      history.replaceState(
        null,
        "",
        WorkorderUtils.updateURLParameter(window.location.href, "page", currentPage)
      );

      // Handle auto-open functionality if needed
      WorkorderAutoOpen.handleDrawCallback(api, info);
      
      // Refresh workorder summary if it exists
      if (window.WorkorderSummary) {
        setTimeout(() => {
          window.WorkorderSummary.refresh();
        }, 100);
      }
    },
    ajax: {
      url: workorderDataUrl,
      type: "POST",
      data: function (d) {
        // For client-side pagination, request all data at once
        d.start = 0;
        d.length = -1;  // Request all records

        // Get values from select2 multi-select fields
        d.project_name = $("#project_name").val();
        d.service_code = $("#service_code").val();
        d.set_name = $("#set_name").val();
        d.customer = $("#customer").val();
        d.workorder_name = $("#workorder_name").val();
        d.activity_date = $("#activity_date").val();
      },

      complete: function(jqXHR, status) {
        if (status === 'success') {
          WorkorderAutoOpen.handleAjaxComplete();
          
          // Refresh workorder summary after new data loads
          if (window.WorkorderSummary) {
            setTimeout(() => {
              window.WorkorderSummary.refresh();
            }, 200);
          }
        }
      }
    },
    columns: [
      {
        data: "wo_name",
        render: function (data, _, row) {
          var url = new URL(row.detail_url, window.location.origin);

          // Handle multiple selections for URL parameters
          var projectNames = $("#project_name").val();
          if (projectNames && projectNames.length) {
            projectNames.forEach(function(value) {
              url.searchParams.append("project_name", value);
            });
          } else {
            url.searchParams.append("project_name", "");
          }

          var serviceCodes = $("#service_code").val();
          if (serviceCodes && serviceCodes.length) {
            serviceCodes.forEach(function(value) {
              url.searchParams.append("service_code", value);
            });
          } else {
            url.searchParams.append("service_code", "");
          }

          var setNames = $("#set_name").val();
          if (setNames && setNames.length) {
            setNames.forEach(function(value) {
              url.searchParams.append("set_name", value);
            });
          } else {
            url.searchParams.append("set_name", "");
          }

          var customers = $("#customer").val();
          if (customers && customers.length) {
            customers.forEach(function(value) {
              url.searchParams.append("customer", value);
            });
          } else {
            url.searchParams.append("customer", "");
          }

          url.searchParams.append("workorder_name", $("#workorder_name").val());
          url.searchParams.append("activity_date", $("#activity_date").val());
          url.searchParams.append(
            "page",
            Math.ceil(
              ($("#workorders-table").DataTable().page.info().start + 1) / 15
            )
          );
                  return (
          '<div class="group relative">' +
            '<a href="' +
            url.toString() +
            '" class="text-blue-500 hover:text-blue-600 font-medium block leading-tight break-words transition-colors duration-150">' +
            data +
            '</a>' +
            '<div class="absolute bottom-full left-0 mb-2 hidden group-hover:block z-50">' +
              '<div class="bg-gray-900 text-white text-xs rounded-lg px-3 py-2 whitespace-nowrap shadow-lg">' +
                '<div class="flex items-center gap-2">' +
                  '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>' +
                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>' +
                  '</svg>' +
                  '<span>Click to open workorder details</span>' +
                '</div>' +
                '<div class="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>' +
              '</div>' +
            '</div>' +
          '</div>'
        );
        },
      },
      {
        data: "set_and_service",
        render: function (data) {
          return '<div class="text-center leading-tight text-sm break-words">' + data.replace("<hr>", '<hr class="my-1 border-gray-300">') + '</div>';
        },
      },
      {
        data: "start_end_dates",
        render: function (data) {
          return `<div class="text-center text-sm leading-tight break-words">${data}</div>`;
        },
      },
    ],
    columnDefs: [
      { targets: "_all", className: "px-3 py-2 align-top" },
      { targets: 0, width: "33.33%", className: "px-3 py-2 align-top" }, // Workorder name column
      { targets: 1, width: "33.33%", className: "px-3 py-2 align-top text-center text-sm" }, // Set/Service column
      { targets: 2, width: "33.33%", className: "px-3 py-2 align-top text-center" } // Start/End date column
    ],
  });

  // Add an event handler for when the DataTable is fully initialized
  $('#workorders-table').on('init.dt', function() {
    WorkorderAutoOpen.handleInitEvent();

    // Get the DataTable API instance
    var api = $(this).DataTable();
    var visibleRows = api.rows({page: 'current'}).nodes().length;
    var maxRows = api.page.len();

    // Ensure the DataTable container has basic flex layout
    var $wrapper = $('.dataTables_wrapper');
    $wrapper.addClass('flex flex-col w-full max-w-full');

    // Create a container for the table that maintains consistent height
    var $tableContainer = $('.dataTables_scrollBody');

    // Always maintain consistent height regardless of row count
    // This prevents height jumping when switching between pages with different numbers of rows
    $tableContainer.addClass('flex-grow');

    // Ensure pagination is always directly below the table
    $('.dataTables_wrapper .dataTables_paginate').addClass('w-full max-w-full').css('margin-top', '0');
    
    // Remove any gaps between table and pagination
    $('.dataTables_scrollBody').css('margin-bottom', '0');
    $('#workorders-table').css('margin-bottom', '0');
    
    // Override DataTable default borders to match gray theme
    $('#workorders-table').css('border-color', '#e5e7eb');
    $('#workorders-table thead th').css('border-color', '#e5e7eb');
    $('#workorders-table tbody td').css('border-color', '#e5e7eb');
    $('#workorders-table tbody tr').css('border-color', '#e5e7eb');
    
    // Ensure consistent font weight for headers (override any DataTable defaults)
    $('#workorders-table thead th').css('font-weight', '500'); // font-medium

    // Force consistent row heights to prevent height jumping due to text wrapping
    // Using larger height to accommodate full workorder names with wrapping
    $('#workorders-table tbody tr').each(function() {
      $(this).css('height', '4.5rem');
    });
  });

  // Backup approach for auto-open
  WorkorderAutoOpen.setupBackupApproach();
});
