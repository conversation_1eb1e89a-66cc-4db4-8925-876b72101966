const ReportManager = {
    /**
     * Creates and returns a daily report icon element.
     * @param {string} reportUrl - The URL for the daily report
     * @param {boolean} hasActivity - Whether the day has activity/units/workorders
     * @returns {HTMLElement|null} The report icon element or null if no URL or activity
     */
    createReportIcon(reportUrl, hasActivity) {
        if (!reportUrl || !hasActivity) {
            return null;
        }

        const reportLink = document.createElement('a');
        reportLink.href = reportUrl;
        reportLink.target = "_blank";
        reportLink.className = `flex items-center justify-center w-7 h-7 bg-blue-500 hover:bg-blue-600 text-white hover:text-white rounded-full shadow-md transition-all duration-150 hover:scale-110`;
        reportLink.title = "View Day's Daily Report";
        reportLink.onclick = (e) => e.stopPropagation();
        reportLink.innerHTML = `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg>`;
        
        return reportLink;
    },

    /**
     * Adds a report icon to a table cell if conditions are met.
     * @param {HTMLElement} container - The container element to add the icon to
     * @param {string} reportUrl - The URL for the daily report
     * @param {boolean} hasActivity - Whether the day has activity
     * @param {boolean} hasUnits - Whether the day has units
     * @param {boolean} hasWorkorders - Whether the day has workorders
     */
    addReportIconToCell(container, reportUrl, hasActivity, hasUnits, hasWorkorders) {
        const reportIcon = this.createReportIcon(reportUrl, hasActivity || hasUnits || hasWorkorders);
        if (reportIcon) {
            container.appendChild(reportIcon);
        }
    }
}; 