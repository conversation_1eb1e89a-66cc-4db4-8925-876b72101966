import os

os.environ['DJANGO_SECRET_KEY'] = "e-uo%23dj7%3Dmv%28e66yj-zje%26%5Ezdapx3z5%3D72l0koou%3Dhlcde%2B%233q"

from .settings import * # NOQA

DEBUG = True

ALLOWED_HOSTS = ['*']
CORS_ALLOWED_ORIGIN_REGEXES = [r"^.*"]
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True

CSRF_COOKIE_SECURE = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_HTTPONLY = True

CSRF_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_SAMESITE = 'Lax'

INSTALLED_APPS.append('debug_toolbar')
INSTALLED_APPS.append('django_browser_reload')

MIDDLEWARE.append('debug_toolbar.middleware.DebugToolbarMiddleware')
MIDDLEWARE.append('django_browser_reload.middleware.BrowserReloadMiddleware')

INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

# after "django.middleware.security.SecurityMiddleware"
MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')
