<div class="overflow-x-auto">
    <div class="bg-white rounded-lg shadow-sm p-4">
        <table class="min-w-full text-[14px] border-collapse">
            <thead>
                <tr>
                    <th colspan="6" class="bg-gray-50 p-3 text-left border-b rounded-tl-lg font-semibold text-gray-700">Start Metrics</th>
                    <th colspan="4" class="bg-gray-50 p-3 text-left border-b font-semibold text-gray-700">End Metrics</th>
                    <th colspan="2" class="bg-gray-50 p-3 text-left border-b rounded-tr-lg font-semibold text-gray-700">Performance Metrics</th>
                </tr>
                <tr class="bg-white [&_th]:p-3 [&_th]:text-left [&_th]:font-medium [&_th]:text-gray-600">
                    <th class="sticky left-0 bg-white z-10 border-b">{{ period_type }}</th>
                    <!-- Schedule Metrics -->
                    <th class="border-b relative group">
                        <span>Projects Started</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Number of projects that started in this period
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>Delayed Projects</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Number of projects that missed planned start date
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>On Time Start Rate, %</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Percentage of projects that started on time
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>Avg Abs Start Variance</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Average absolute variance in project start dates
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>4d Start Offset, %</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Percentage of projects with start date offset > +- 3 days
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>Projects Completed</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Number of projects that completed in this period
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>On Time End Rate, %</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Percentage of projects that ended on time
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>Avg Abs End Variance</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Average absolute variance in project end dates
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>4d End Offset, %</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Percentage of projects with end date offset > +- 3 days
                        </div>
                    </th>
                    <!-- Performance Metrics -->
                    <th class="border-b relative group">
                        <span>Avg Date Changes</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Average number of date changes per project
                        </div>
                    </th>
                    <th class="border-b relative group">
                        <span>Extended Duration Projects, %</span>
                        <div class="invisible group-hover:visible absolute z-20 bg-gray-900 text-white text-sm rounded-lg p-2 -mt-1 top-8 left-0 w-48 shadow-lg">
                            Percentage of projects with extended duration
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody class="[&_td]:p-3">
                {% for row in metrics %}
                <tr class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="sticky left-0 bg-white z-10 font-medium border-b">
                        {% if period_type == 'Week' %}
                            {{ row.week }}
                        {% else %}
                            {{ row.month }}
                        {% endif %}
                    </td>
                    <!-- Start Metrics -->
                    <td class="border-b">{{ row.total_started_projects|default_if_none:"No Data" }}</td>
                    <td class="border-b">{{ row.delayed_projects|default_if_none:"No Data" }}</td>
                    <td class="border-b">{{ row.on_time_start_rate|default_if_none:"No Data" }}</td>
                    <td class="border-b">{{ row.avg_abs_start_variance|default_if_none:"No Data" }}</td>
                    <td class="border-b">{{ row.significant_schedule_variance_starts_pct|default_if_none:"No Data" }}</td>
                    <!-- End Metrics -->
                    <td class="border-b">{{ row.total_completed_projects|default_if_none:"No Data" }}</td>
                    <td class="border-b">{{ row.on_time_completion_percentage|default_if_none:"No Data" }}</td>
                    <td class="border-b">{{ row.avg_abs_end_variance|default_if_none:"No Data" }}</td>
                    <td class="border-b">{{ row.significant_schedule_variance_ends_pct|default_if_none:"No Data" }}</td>
                    <!-- Performance Metrics -->
                    <td class="border-b">{{ row.avg_date_changes|default_if_none:"No Data" }}</td>
                    <td class="border-b">{{ row.extended_duration_percentage|default_if_none:"No Data" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
