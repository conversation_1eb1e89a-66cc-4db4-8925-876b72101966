"""Refresh cache view."""

import datetime
import threading

from django.http import HttpRe<PERSON>, HttpResponse
from django.views.decorators.csrf import csrf_exempt

from myproject.utils import get_bigquery_client

from .models import BigQueryCache

import logging

logger = logging.getLogger(__name__)


def update_all_caches():
    """Update all caches."""
    logger.info("Starting caches update")
    now = datetime.datetime.now(tz=datetime.timezone.utc)
    cache_expiry = datetime.timedelta(minutes=5)
    cache_age = datetime.timedelta(days=30)
    client = get_bigquery_client()
    
    # pylint: disable=no-member
    for cache in BigQueryCache.objects.all():
        if cache.last_updated < now - cache_expiry:
            if cache.last_accessed and cache.last_accessed < now - cache_age:
                cache.delete()
            else:
                args = client.get_query_args(cache)
                client.query(**args, use_cache=False)
    
    logger.info(f"Caches updated")


@csrf_exempt
def refresh_cache_view(request: HttpRequest) -> HttpResponse:
    """Call all caches for a refresh."""
    if request.method == "POST":
        thread = threading.Thread(target=update_all_caches)
        thread.start()
        return HttpResponse(
            "Cache refresh process started (Project Level)", status=200
        )
    return HttpResponse("Method not allowed. Please use POST.", status=405)
