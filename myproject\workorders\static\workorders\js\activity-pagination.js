$(document).ready(function() {
    // Initialize pagination variables
    let currentPage = 1;
    const itemsPerPage = 50; // Display 50 activities per page
    let totalItems = 0;
    let totalPages = 0;
    let activityRows = [];
    let currentFilter = 'all'; // Track current filter state
    let allActivityRows = []; // Store all rows for filtering

    // Function to ensure consistent layout for the activity table
    function setActivityTableLayout() {
        const activityTable = $('#activity-table');
        const activityTableScroll = activityTable.closest('.activity-table-scroll');
        const activityTableContainer = $('.activity-table-container');
        const paginationContainer = $('.activity-pagination');
        const activitySection = $('.activity-section');

        // Get the number of visible rows
        const visibleRows = $('#activity-table tbody tr:visible').length;

        if (visibleRows < 8) {
            // For fewer than 8 rows, adjust the container to fit content
            activityTableContainer.css('flex', '0 1 auto');
            activitySection.css('justify-content', 'flex-start');
        } else {
            // For 8 or more rows, let it fill the space
            activityTableContainer.css('flex', '1');
            activitySection.css('justify-content', 'space-between');
        }

        // Table layout is now handled by HTML classes
    }

    // Function to filter activities based on type
    function filterActivities(filterType) {
        currentFilter = filterType;

        if (filterType === 'all') {
            activityRows = allActivityRows.slice(); // Copy all rows
        } else {
            activityRows = allActivityRows.filter(function(row) {
                return $(row).data('activity-type') === filterType;
            });
        }

        // Update pagination with filtered results
        totalItems = activityRows.length;
        totalPages = Math.ceil(totalItems / itemsPerPage);

        // Reset to first page when filter changes
        currentPage = 1;

        // Update displays
        $('#activity-total').text(totalItems);
        $('#activity-total-pages').text(totalPages);
        $('#activity-visible-count').text(totalItems);

        // Show first page of filtered results
        showActivityPage(1);
    }

    // Function to initialize the pagination
    function initActivityPagination() {
        // Remove any existing empty rows before getting the actual rows
        $('#activity-table tbody tr.empty-row').remove();

        // Get all rows from the activity table (excluding empty rows) and store them
        allActivityRows = $('#activity-table tbody tr:not(.empty-row)').toArray();
        activityRows = allActivityRows.slice(); // Copy all rows initially
        totalItems = activityRows.length;

        // Update total items display
        $('#activity-total').text(totalItems);
        $('#activity-visible-count').text(totalItems);

        // Calculate total pages
        totalPages = Math.ceil(totalItems / itemsPerPage);
        $('#activity-total-pages').text(totalPages);

        // Show the first page
        showActivityPage(1);

        // Disable prev button on first page
        updatePaginationButtons();
    }

    // Make initActivityPagination globally accessible
    window.initActivityPagination = initActivityPagination;

    // Function to show a specific page
    function showActivityPage(page) {

        // Remove any existing empty rows
        $('#activity-table tbody tr.empty-row').remove();

        // Hide all activity rows (both filtered and unfiltered)
        $(allActivityRows).hide();

        // Calculate start and end indices
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage - 1, totalItems - 1);

        // Show rows for the current page
        let visibleCount = 0;
        for (let i = startIndex; i <= endIndex; i++) {
            if (i < activityRows.length) {
                $(activityRows[i]).show();
                visibleCount++;
            }
        }

        // Remove any existing empty rows to ensure clean display
        $('#activity-table tbody tr.empty-row').remove();

        // Update current page
        currentPage = page;
        $('#activity-current-page').text(currentPage);

        // Update range display
        $('#activity-start-range').text(startIndex + 1);
        $('#activity-end-range').text(Math.min(endIndex + 1, totalItems));

        // Update button states
        updatePaginationButtons();

        // Ensure consistent layout
        setActivityTableLayout();

        // Adjust the table container height based on the number of visible rows
        const tableContainer = $('.activity-table-container');
        const visibleRowCount = $('#activity-table tbody tr:visible').length;

        if (visibleRowCount < 8) {
            // Calculate the total height of all visible rows dynamically
            let totalRowsHeight = 0;
            $('#activity-table tbody tr:visible').each(function() {
                totalRowsHeight += $(this).outerHeight(true);
            });

            const headerHeight = $('#activity-table thead').outerHeight();
            const totalHeight = totalRowsHeight + headerHeight;

            // Set the container to exactly fit the visible content
            tableContainer.css('height', totalHeight + 'px');
            tableContainer.css('min-height', 'auto');
        } else {
            // Reset to default for 8 or more rows
            tableContainer.css('height', '');
            tableContainer.css('min-height', '');
        }

    }

    // Function to update pagination button states
    function updatePaginationButtons() {
        // Disable prev button on first page
        if (currentPage === 1) {
            $('#activity-prev').addClass('opacity-50 cursor-not-allowed').prop('disabled', true);
        } else {
            $('#activity-prev').removeClass('opacity-50 cursor-not-allowed').prop('disabled', false);
        }

        // Disable next button on last page
        if (currentPage === totalPages || totalPages === 0) {
            $('#activity-next').addClass('opacity-50 cursor-not-allowed').prop('disabled', true);
        } else {
            $('#activity-next').removeClass('opacity-50 cursor-not-allowed').prop('disabled', false);
        }
    }

    // Event handler for previous button
    $('#activity-prev').click(function() {
        if (currentPage > 1) {
            showActivityPage(currentPage - 1);
        }
    });

    // Event handler for next button
    $('#activity-next').click(function() {
        if (currentPage < totalPages) {
            showActivityPage(currentPage + 1);
        }
    });

    // Filter dropdown event handlers
    $('#activity-filter-btn').click(function(e) {
        e.stopPropagation();
        $('#activity-filter-dropdown').toggleClass('hidden');
    });

    // Close dropdown when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('#activity-filter-btn, #activity-filter-dropdown').length) {
            $('#activity-filter-dropdown').addClass('hidden');
        }
    });

    // Filter option selection
    $('.activity-filter-option').click(function(e) {
        e.preventDefault();
        const filterType = $(this).data('filter');
        const filterText = $(this).text();

        // Update button text
        $('#activity-filter-text').text(filterText);

        // Close dropdown
        $('#activity-filter-dropdown').addClass('hidden');

        // Apply filter
        filterActivities(filterType);
    });

    // Initialize pagination when activities are loaded
    if ($('#activity-table tbody tr').length > 0) {
        initActivityPagination();
    }

    // Check again after a short delay to ensure DOM is fully loaded
    setTimeout(function() {
        if ($('#activity-table tbody tr').length > 0) {
            // Remove any existing empty rows before re-initializing
            $('#activity-table tbody tr.empty-row').remove();
            initActivityPagination();
        }
    }, 500);
});
