import logging
import cv2
import os

logger = logging.getLogger(__name__)

def log_error(message):
    """Log error messages"""
    logger.error(message)

def log_info(message, **kwargs):
    """Log info messages with optional kwargs"""
    if kwargs:
        message = f"{message} - {kwargs}"
    logger.info(message)

def save_debug_image(image, name):
    """Save debug image to temporary directory"""
    try:
        debug_dir = 'debug_images'
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)
        
        filepath = os.path.join(debug_dir, f'{name}.png')
        cv2.imwrite(filepath, image)
        logger.info(f"Saved debug image: {filepath}")
    except Exception as e:
        logger.error(f"Failed to save debug image: {str(e)}")
