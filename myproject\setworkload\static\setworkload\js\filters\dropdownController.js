const DropdownController = {
    /**
     * Simple Select2 display: shows "n selected" or placeholder with tooltip
     * @param {jQuery} $select - The jQuery select element
     */
    applySimpleCountDisplay($select) {
        const $container = $select.next('.select2-container');
        const $rendered = $container.find('.select2-selection__rendered');
        const selectedValues = $select.val() || [];

        // Remove any existing content
        $rendered.empty();

        // Create text content based on selection count
        let displayText = selectedValues.length === 0 ? 'All selected' : `${selectedValues.length} selected`;

        // Create a single span with the display text
        const $displaySpan = $('<span class="select2-selection__placeholder">').text(displayText);

        // Add tooltip for selected values
        if (selectedValues.length > 0) {
            // Get the text of each selected option
            const selectedTexts = selectedValues.map(value => {
                return $select.find(`option[value="${value}"]`).text();
            });

            // Create tooltip content
            const tooltipContent = selectedTexts.join(', ');

            // Add title attribute for native tooltip
            $displaySpan.attr('title', tooltipContent);

            // Add data attributes for accessibility
            $displaySpan.attr('role', 'tooltip');
            $displaySpan.attr('data-tooltip', tooltipContent);
        }

        $rendered.append($displaySpan);

        // Add a dropdown arrow indicator if needed
        if (!$container.find('.select2-selection__arrow').length) {
            $container.find('.select2-selection').append('<span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>');
        }
    },

    /**
     * Initializes Select2 components.
     * @param {Function} onSupervisorChange - Callback for supervisor changes
     * @param {Function} onServiceCodeChange - Callback for service code changes
     */
    initializeSelect2(onSupervisorChange, onServiceCodeChange) {
        if (typeof $ === 'undefined' || typeof $.fn.select2 === 'undefined') return;

        const commonSelect2Options = {
            allowClear: false,
            width: '100%',
            closeOnSelect: false,
            multiple: true,
            minimumResultsForSearch: 0, // Enable search box
            dropdownParent: $('body'), // Ensures dropdown isn't clipped.
            theme: 'tailwindcss-3'
        };

        // Function to set up focus handling for a select element
        const setupFocusHandling = (selectId) => {
            // Create a MutationObserver to watch for the dropdown being added to the DOM
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.addedNodes.length) {
                        // Check if the added node is a Select2 dropdown
                        const addedDropdown = Array.from(mutation.addedNodes).find(
                            node => node.classList && node.classList.contains('select2-container--open')
                        );

                        if (addedDropdown) {
                            // Focus the search field with multiple attempts
                            const focusSearchField = () => {
                                const searchField = addedDropdown.querySelector('.select2-search__field');
                                if (searchField) {
                                    // Try to focus multiple times with increasing delays
                                    setTimeout(() => searchField.focus(), 10);
                                    setTimeout(() => searchField.focus(), 50);
                                    setTimeout(() => searchField.focus(), 100);
                                }
                            };

                            focusSearchField();
                        }
                    }
                });
            });

            // Start observing the document body for Select2 dropdown additions
            observer.observe(document.body, { childList: true, subtree: true });
        };

        try {
            $('#supervisor').select2({
                ...commonSelect2Options,
                placeholder: '',  // Empty placeholder to avoid duplication
            }).on('select2:selecting', function(e) {
                const currentValues = $(this).val() || [];
                if (e.params.args.data.id === 'N/A') {
                    $(this).val([]).trigger('change');
                } else if (currentValues.includes('N/A')) {
                    const newValues = currentValues.filter(v => v !== 'N/A');
                    newValues.push(e.params.args.data.id);
                    e.preventDefault();
                    $(this).val(newValues).trigger('change');
                }
            }).on('select2:select select2:unselect select2:close', (e) => {
                setTimeout(() => {
                    this.applySimpleCountDisplay($(e.currentTarget));
                }, 0);
            });

            $('#service-code').select2({
                ...commonSelect2Options,
                placeholder: '',  // Empty placeholder to avoid duplication
            }).on('select2:selecting', function(e) {
                const currentValues = $(this).val() || [];
                if (e.params.args.data.id === 'N/A') {
                    $(this).val([]).trigger('change');
                } else if (currentValues.includes('N/A')) {
                    const newValues = currentValues.filter(v => v !== 'N/A');
                    newValues.push(e.params.args.data.id);
                    e.preventDefault();
                    $(this).val(newValues).trigger('change');
                }
            }).on('select2:select select2:unselect select2:close', (e) => {
                setTimeout(() => {
                    this.applySimpleCountDisplay($(e.currentTarget));
                }, 0);
            });

            // Set up focus handling for both select elements
            setupFocusHandling();

            // Add direct event handlers as a fallback
            $('#supervisor, #service-code').on('select2:open', function() {
                // Try multiple focus attempts with different timing
                [10, 50, 100, 200].forEach(delay => {
                    setTimeout(() => {
                        const searchField = document.querySelector('.select2-container--open .select2-search__field');
                        if (searchField) {
                            searchField.focus();
                        }
                    }, delay);
                });
            });

            if (onSupervisorChange) {
                $('#supervisor').on('change', onSupervisorChange);
            }
            if (onServiceCodeChange) {
                $('#service-code').on('change', onServiceCodeChange);
            }

            // Apply initial display
            this.applySimpleCountDisplay($('#supervisor'));
            this.applySimpleCountDisplay($('#service-code'));

        } catch (error) {
            // Silently catch Select2 init error.
        }
    },

    /**
     * Populates supervisor dropdown.
     */
    initializeSupervisorSelect() {
        const supervisorSelect = document.getElementById('supervisor');
        if (!supervisorSelect) return;

        const currentValues = workloadStore.activeFilters.supervisors || [];
        const supervisors = [...new Set(workloadStore.rawData.map(item => item.supervisor))]
            .filter(Boolean)
            .filter(supervisor => supervisor !== 'N/A');

        supervisorSelect.innerHTML = '';
        supervisors.forEach(supervisor => {
            const option = document.createElement('option');
            option.value = supervisor;
            option.textContent = supervisor;
            supervisorSelect.appendChild(option);
        });

        const validValues = currentValues.filter(value => supervisors.includes(value));
        $(supervisorSelect).val(validValues).trigger('change.select2');
        this.applySimpleCountDisplay($(supervisorSelect));
    },

    /**
     * Populates service code dropdown based on active filters.
     */
    initializeServiceCodeSelect() {
        const serviceCodeSelect = document.getElementById('service-code');
        if (!serviceCodeSelect) return;

        const currentValues = workloadStore.activeFilters.serviceCodes || [];
        const supervisorValues = $('#supervisor').val() || [];
        const showAllSets = supervisorValues.length === 0;

        serviceCodeSelect.innerHTML = '';
        const allServiceCodes = new Set();
        const dataToUse = showAllSets || !workloadStore.filteredData.length ? workloadStore.rawData : workloadStore.filteredData;

        const setData = {};
        dataToUse.forEach(item => {
            if (!setData[item.set_name]) setData[item.set_name] = { days: {} };
            setData[item.set_name].days[item.date] = item;
        });

        const activeSets = Object.values(setData).filter(data =>
            Object.values(data.days).some(day => day.direct_hours > 0 || day.idle_hours > 0 || day.units > 0)
        );

        activeSets.forEach(data => {
            Object.values(data.days).forEach(day => {
                if (day.service_codes && Array.isArray(day.service_codes)) {
                    day.service_codes.forEach((code) => {
                        let source = "unknown";
                        if (day.service_code_sources && Array.isArray(day.service_code_sources)) {
                            source = day.service_code_sources.includes('workorder') ? 'workorder' :
                                   (day.service_code_sources.includes('previous_workorder') ? 'previous_workorder' : 'unknown');
                        }
                        // Include both workorder and previous_workorder sourced service codes
                        if ((source === 'workorder' || source === 'previous_workorder') && code) allServiceCodes.add(code);
                    });
                }
            });
        });
        const serviceCodes = Array.from(allServiceCodes).sort();
        serviceCodes.forEach(code => {
            const option = document.createElement('option');
            option.value = code;
            option.textContent = code;
            serviceCodeSelect.appendChild(option);
        });

        const validValues = currentValues.filter(value => serviceCodes.includes(value));
        $(serviceCodeSelect).val(validValues).trigger('change.select2');

        // Ensure placeholder is applied after setting values
        setTimeout(() => {
            this.applySimpleCountDisplay($(serviceCodeSelect));
        }, 0);
    }
}; 