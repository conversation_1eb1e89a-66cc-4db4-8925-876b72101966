// DataTable configuration and initialization

import { getCookie, getColumn<PERSON><PERSON><PERSON> } from './utils.js';
import { updateStatistics } from './statistics.js';

// Ideally, this would be imported from select2.js or a shared constants file
const projectFilterIds = ["service", "project_status", "project_manager", "region", "client"];

export function initializeDataTable(projectDataUrl) {
    const csrftoken = getCookie('csrftoken');

    return $("#projects-table").DataTable({
        serverSide: false,
        pageLength: 15,
        lengthChange: false,
        searching: true,
        info: true,
        ordering: true,
        responsive: true,
        scrollX: true,
        autoWidth: false,
        processing: true,
        ajax: {
            url: projectDataUrl,
            type: "POST",
            headers: {
                'X-CSRFToken': csrftoken
            },
            data: function (d) {
                try {
                    const currentFilters = {};
                    projectFilterIds.forEach(id => {
                        const $select = $(`#${id}`);
                        if ($select.length && $select.val() && $select.val().length > 0) {
                            currentFilters[id] = $select.val();
                        }
                    });
                    
                    if (Object.keys(currentFilters).length > 0) {
                         return $.extend({}, d, { filters: JSON.stringify(currentFilters) });
                    }
                    return d;
                } catch (err) {
                    // Silent error handling
                    return d; // Return unmodified data on error
                }
            },
            error: function (xhr, error, thrown) {
                // Silently handle all errors - no console output
                return [];
            },
            dataSrc: function (json) {
                try {
                    return json && json.data ? json.data : [];
                } catch (err) {
                    // Silent error handling
                    return []; // Return empty data set on error
                }
            }
        },
        columns: [
            {
                data: "project_name",
                className: "text-left px-2",
                width: "13%",
                render: function(data, type, row) {
                    if (type === 'display') {
                        if (!data) return "- No name -";
                        if (!row || !row.wrike_link) return data;
                        return `<a href="${row.wrike_link}" target="_blank" class="text-blue-600 hover:text-blue-800 hover:underline">${data}</a>`;
                    }
                    return data;
                }
            },
            { data: "project_status", className: "text-left px-2", width: "10%" },
            { data: "set_name", className: "text-left px-2", width: "10%" },
            { data: "service", className: "text-left px-2", width: "10%" },
            { data: "unit_type", className: "text-center px-1", width: "5%" },
            { data: "units", className: "text-left px-2", width: "5%" },
            { data: "first_promised_date", className: "text-left px-2", width: "12%" },
            { data: "planned_dates", className: "text-left px-2", width: "12%" },
            {
                data: "actual_dates",
                className: "text-left px-2",
                width: "12%",
                render: function(data, type, row) {
                    if (type === 'display') {
                        if (!data) return "- No data -";
                        try {
                            const [start, end] = data.split(' .. ');
                            return `<div class="truncate" title="${data}">${start} .. ${end}</div>`;
                        } catch (e) {
                            return data;
                        }
                    }
                    return data;
                }
            },
            { data: "start_date_variance", className: "text-center px-2", width: "7%" },
            { data: "duration_variance", className: "text-center px-2", width: "7%" },
            { data: "date_changes_count", className: "text-center px-2", width: "7%" },
            { data: "project_manager", visible: false },
            { data: "region", visible: false },
            { data: "client", visible: false },
            { data: "abs_end_date_variance", visible: false },
            { data: "started_on_time", visible: false },
            { data: "completed_on_time", visible: false },
            { data: "project_id", visible: false },
            { data: "abs_start_date_variance", visible: false },
        ],
        order: [[17, "desc"]],
        columnDefs: [{
            targets: "_all",
            render: getColumnRenderer
        }],
        language: {
            info: "Showing _START_ to _END_ of _TOTAL_ projects",
            infoEmpty: "Showing 0 to 0 of 0 projects",
            infoFiltered: "(filtered from _MAX_ total projects)",
            search: ""
        },
        drawCallback: function () {
            try {
                const filteredData = this.api().rows({ filter: "applied" }).data();
                updateStatistics(filteredData);
            } catch (err) {
                // Silent error handling
            }
        },
        initComplete: function (settings, json) {
            try {
                const api = this.api();

                // Populate select dropdowns with unique values
                api.columns().every(function () {
                    const column = this;
                    const columnName = column.dataSrc();
                    const select = $(`#${columnName}`);

                    if (select.length && select.hasClass('select2-input')) {
                        const uniqueValues = new Set();
                        column.data().each(function (value) {
                            if (typeof value === 'string' && value) {
                                value.split(',').forEach(v => {
                                    uniqueValues.add(v.trim());
                                });
                            }
                        });

                        Array.from(uniqueValues).sort().forEach(value => {
                            select.append(new Option(value, value));
                        });
                    }
                });

                // Initialize Select2 after populating options
                $(document).trigger("datatableLoaded");
                api.columns.adjust();
            } catch (err) {
                // Silent error handling
                // Still trigger datatable loaded event even if there was an error
                $(document).trigger("datatableLoaded");
            }
        }
    });
}

export function filterTable(table) {
    // This function is called when filter selections change in select2.js
    try {
        // It should now trigger a server-side reload, which will use the `ajax.data` function
        // to send the latest filters.
        if (table && $.fn.DataTable.isDataTable('#projects-table')) {
            table.ajax.reload(null, false); // Reload data from server, false means don't reset paging
        }
        // updateStatistics will be handled by the DataTable's drawCallback after ajax.reload completes.
    } catch (err) {
        // Silent error handling
    }
}
