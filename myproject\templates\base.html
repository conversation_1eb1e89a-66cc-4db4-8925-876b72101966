{% load static %}
<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-100">

<head>
  {% csrf_token %}
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-4KP6C39QSK"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag("js", new Date());

    gtag("config", "G-4KP6C39QSK");
  </script>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{% block title %}Workorder Data Visualization{% endblock %}</title>
  {% load tailwind_tags %} {% tailwind_css %}

  <script src="https://cdn.plot.ly/plotly-3.0.1.min.js" charset="utf-8" defer></script>
  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.24/css/jquery.dataTables.css" />
  <link rel="icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script type="text/javascript" charset="utf8"
    src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.js"></script>
  <script src="https://unpkg.com/alpinejs@3.14.1/dist/cdn.min.js" defer></script>
  <meta name="version" content="{{ commit_sha }}" />
  {% block extra_head %}{% endblock extra_head %}
</head>

<body class="flex flex-col h-screen">
  <div class="flex flex-col h-full">
    {% include 'headerControls.html' %}
    <main class="flex-grow flex">
      <div class="px-2 py-6 mx-auto max-w-full h-full w-full sm:px-4 lg:px-6 flex flex-col">
        {% block content %}{% endblock %}
      </div>
    </main>
  </div>
</body>

</html>