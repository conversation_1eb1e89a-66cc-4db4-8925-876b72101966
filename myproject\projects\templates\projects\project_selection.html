{% load static %}

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/erimicel/select2-tailwindcss-theme/dist/select2-tailwindcss-theme-plain.min.css">
<link href="{% static 'projects/css/select2-theme.css' %}" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="{% static 'projects/js/select2.js' %}"></script>


<div id="project-filter-form" class="flex flex-col gap-4">
  <div class="grid grid-cols-1 gap-y-2 gap-x-6 w-full sm:grid-cols-2 lg:grid-cols-5">
    <div class="flex flex-col gap-1">
      <label for="service" class="text-sm font-medium text-gray-700">Service</label>
      <div class="relative select2-container-wrapper">
        <select name="service" id="service" class="w-full rounded form-select select2-input" multiple>
          <option></option>
        </select>
      </div>
    </div>

    <div class="flex flex-col gap-1">
      <label for="project_status" class="text-sm font-medium text-gray-700">Status</label>
      <div class="relative select2-container-wrapper">
        <select name="project_status" id="project_status" class="w-full rounded form-select select2-input" multiple>
          <option></option>
        </select>
      </div>
    </div>

    <div class="flex flex-col gap-1">
      <label for="project_manager" class="text-sm font-medium text-gray-700">Project Manager</label>
      <div class="relative select2-container-wrapper">
        <select name="project_manager" id="project_manager" class="w-full rounded form-select select2-input" multiple>
          <option></option>
        </select>
      </div>
    </div>

    <div class="flex flex-col gap-1">
      <label for="region" class="text-sm font-medium text-gray-700">Region</label>
      <div class="relative select2-container-wrapper">
        <select name="region" id="region" class="w-full rounded form-select select2-input" multiple>
          <option></option>
        </select>
      </div>
    </div>

    <div class="flex flex-col gap-1">
      <label for="client" class="text-sm font-medium text-gray-700">Client</label>
      <div class="relative select2-container-wrapper">
        <select name="client" id="client" class="w-full rounded form-select select2-input" multiple>
          <option></option>
        </select>
      </div>
    </div>
  </div>
</div>