/**
 * Handles custom pagination for the workorders DataTable
 */
var WorkorderPagination = (function() {
  // Create custom pagination for the DataTable
  function createPagination(tableElement, api, currentPage, info) {
    var $pagination = $(tableElement).closest('.dataTables_wrapper').find('.dataTables_paginate');
    var $wrapper = $(tableElement).closest('.dataTables_wrapper');
    var $infoElement = $(tableElement).closest('.dataTables_wrapper').find('.dataTables_info');

    // Clear existing pagination
    $pagination.empty();

    // Create the pagination container
    var $paginationContainer = $('<div class="flex flex-col sm:flex-row justify-between items-center gap-4 bg-gray-50 p-4 w-full max-w-full border border-gray-100"></div>');

    // Create the info section (showing X to Y of Z workorders)
    var start = info.start + 1;
    var end = info.end;
    var total = info.recordsTotal;
    var $infoSection = $('<div class="text-sm text-gray-700 text-center sm:text-left w-full sm:w-auto"></div>')
      .html('Showing <span id="workorder-start-range" class="font-medium">' + start + '</span> to <span id="workorder-end-range" class="font-medium">' + end + '</span> of <span id="workorder-total" class="font-medium">' + total + '</span> workorders');

    // Create the pagination controls
    var $paginationControls = $('<div class="flex justify-center sm:justify-end w-full sm:w-auto"></div>');
    var $nav = $('<nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="Pagination"></nav>');

    // Create previous button
    var $prevButton = $('<button id="workorder-prev" class="relative inline-flex items-center px-3 py-2 rounded-l-lg border border-gray-200 bg-white text-sm font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 transition-all duration-150"></button>');
    if (currentPage === 1) {
      $prevButton.addClass('opacity-50 cursor-not-allowed').prop('disabled', true);
    }
    $prevButton.html('<span class="sr-only">Previous</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>');
    $prevButton.on('click', function() {
      if (currentPage > 1) {
        api.page(currentPage - 2).draw(false);
      }
    });

    // Create page numbers display
    var $pageNumbers = $('<div id="workorder-page-numbers" class="relative inline-flex items-center px-4 py-2 border-t border-b border-gray-200 bg-white text-sm font-medium text-gray-700"></div>')
      .html('<span id="workorder-current-page" class="font-semibold text-gray-900">' + currentPage + '</span> / <span id="workorder-total-pages">' + info.pages + '</span>');

    // Create next button
    var $nextButton = $('<button id="workorder-next" class="relative inline-flex items-center px-3 py-2 rounded-r-lg border border-gray-200 bg-white text-sm font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 transition-all duration-150"></button>');
    if (currentPage === info.pages) {
      $nextButton.addClass('opacity-50 cursor-not-allowed').prop('disabled', true);
    }
    $nextButton.html('<span class="sr-only">Next</span><svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>');
    $nextButton.on('click', function() {
      if (currentPage < info.pages) {
        api.page(currentPage).draw(false);
      }
    });

    // Assemble the pagination controls
    $nav.append($prevButton).append($pageNumbers).append($nextButton);
    $paginationControls.append($nav);

    // Assemble the final pagination container
    $paginationContainer.append($infoSection).append($paginationControls);

    // Replace the default info element with our custom one
    $infoElement.hide();

    // Add the pagination to the wrapper
    $pagination.addClass('w-full max-w-full').append($paginationContainer);

    // Completely remove any margin/padding gaps
    $pagination.css({
      'margin-top': '0',
      'margin-bottom': '0',
      'padding-top': '0'
    });

    // Ensure the wrapper has full width
    $wrapper.addClass('w-full max-w-full');
  }

  // Public API
  return {
    createPagination: createPagination
  };
})();
