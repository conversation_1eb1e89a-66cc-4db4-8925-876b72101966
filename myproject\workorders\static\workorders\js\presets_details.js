// Function to copy hash to clipboard
function copyHashToClipboard(hash) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(hash).then(() => {
            // Create temporary notification
            const notification = document.createElement('div');
            notification.textContent = 'Hash copied to clipboard!';
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 text-sm';
            document.body.appendChild(notification);
            
            // Remove notification after 2 seconds
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy hash: ', err);
            // Fallback for copy failure
            fallbackCopyTextToClipboard(hash);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyTextToClipboard(hash);
    }
}

// Fallback copy function for older browsers
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            // Create temporary notification
            const notification = document.createElement('div');
            notification.textContent = 'Hash copied to clipboard!';
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 text-sm';
            document.body.appendChild(notification);
            
            // Remove notification after 2 seconds
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 2000);
        } else {
            console.error('Fallback: Copying text command was unsuccessful');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
    }
    
    document.body.removeChild(textArea);
}

// Function to show all preset details (simplified approach)
function showAllPresetDetails() {
    const messageDiv = document.getElementById('preset-details-message');
    const contentDiv = document.getElementById('preset-details-content');
    const headerDiv = document.getElementById('preset-details-header');
    
    // Hide message and show all content
    messageDiv.classList.add('hidden');
    contentDiv.classList.remove('hidden');
    headerDiv.classList.remove('hidden');
    
    // Clear subtitle when showing all presets
    document.getElementById('preset-type-subtitle').textContent = '';
    
    // Show all accordions (only within preset details section)
    const allAccordions = document.querySelectorAll('#preset-details-content .border.border-gray-200.rounded-lg');
    
    allAccordions.forEach((accordion, index) => {
        accordion.style.display = 'block';
    });
    
    // Update the counts for all accordions
    updateTabCounts();
}

// Function to show preset details for a specific hash
function showPresetDetailsForHash(hash, clickedTimestamp) {
    const messageDiv = document.getElementById('preset-details-message');
    const contentDiv = document.getElementById('preset-details-content');
    const headerDiv = document.getElementById('preset-details-header');
    
    // Filter accordions to only show applying presets with this hash (only within preset details section)
    const allAccordions = document.querySelectorAll('#preset-details-content .border.border-gray-200.rounded-lg');
    let hasMatchingData = false;
    
    allAccordions.forEach((accordion, index) => {
        // Get the hash and group_key from the accordion
        const accordionHash = accordion.getAttribute('data-hash');
        const groupKey = accordion.getAttribute('data-group-key');
        
        // Only show applying presets with matching hash
        if (accordionHash === hash && groupKey && groupKey.includes('_applying')) {
            accordion.style.display = 'block';
            hasMatchingData = true;
        } else {
            accordion.style.display = 'none';
        }
    });
    
    if (hasMatchingData) {
        // Hide message and show header and filtered content
        messageDiv.classList.add('hidden');
        contentDiv.classList.remove('hidden');
        headerDiv.classList.remove('hidden');
        
        // Set subtitle for applying presets with timestamp
        if (clickedTimestamp) {
            const clickedDate = new Date(clickedTimestamp);
            const formattedTimestamp = clickedDate.toLocaleString('sv-SE').replace('T', ' ');
            document.getElementById('preset-type-subtitle').innerHTML = `Viewing Presets Applying on <strong>${formattedTimestamp}</strong>`;
        } else {
            document.getElementById('preset-type-subtitle').textContent = 'Viewing Presets Applying';
        }
        
        // Update the counts for visible accordions
        updateTabCounts();
    } else {
        // No data found for this hash
        messageDiv.classList.remove('hidden');
        contentDiv.classList.add('hidden');
        headerDiv.classList.add('hidden');
    }
}

// Function to filter presets by timestamp and show details (keeping for future use)
function showPresetDetailsForTimestamp(clickedTimestamp) {
    const messageDiv = document.getElementById('preset-details-message');
    const contentDiv = document.getElementById('preset-details-content');
    const headerDiv = document.getElementById('preset-details-header');
    
    if (!clickedTimestamp) {
        // Hide content and header, show message
        messageDiv.classList.remove('hidden');
        contentDiv.classList.add('hidden');
        headerDiv.classList.add('hidden');
        return;
    }
    
    // Convert clicked timestamp to match our data format
    const targetTime = new Date(clickedTimestamp);
    
    // Find accordions that match this timestamp (within 1 minute tolerance) - only within preset details section
    const allAccordions = document.querySelectorAll('#preset-details-content .border.border-gray-200.rounded-lg');
    let hasMatchingData = false;
    
    allAccordions.forEach((accordion, index) => {
        // Get the timestamp from the accordion header using the data-timestamp attribute
        const timestampElement = accordion.querySelector('.text-sm.font-medium.text-gray-900');
        if (timestampElement) {
            const isoTimestamp = timestampElement.getAttribute('data-timestamp');
            const displayText = timestampElement.textContent.trim();
            const accordionTimestamp = new Date(isoTimestamp);
            const timeDiff = Math.abs(accordionTimestamp - targetTime);
            
            if (timeDiff < 1000) { // Within 1 second tolerance
                accordion.style.display = 'block';
                hasMatchingData = true;
            } else {
                accordion.style.display = 'none';
            }
        }
    });
    
    if (hasMatchingData) {
        // Hide message and show header and filtered content
        messageDiv.classList.add('hidden');
        contentDiv.classList.remove('hidden');
        headerDiv.classList.remove('hidden');
        
        // Update the counts for visible accordions
        updateTabCounts();
    } else {
        // No data found for this timestamp
        messageDiv.classList.remove('hidden');
        contentDiv.classList.add('hidden');
        headerDiv.classList.add('hidden');
    }
}

function toggleAccordion(presetId) {
    const content = document.getElementById(presetId + '-content');
    const icon = document.getElementById(presetId + '-icon');
    
    if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.classList.add('rotate-180');
        // Initialize first tab as active when accordion opens
        showTab(presetId, 'stages');
    } else {
        content.classList.add('hidden');
        icon.classList.remove('rotate-180');
    }
}

function showTab(presetId, tabName) {
    // Hide all tab contents for this preset
    const tabContents = document.querySelectorAll('#' + presetId + '-content .tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tabs for this preset
    const tabButtons = document.querySelectorAll('#' + presetId + '-content .tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    document.getElementById(presetId + '-' + tabName + '-content').classList.remove('hidden');
    
    // Add active class to selected tab
    const activeTab = document.getElementById(presetId + '-' + tabName + '-tab');
    activeTab.classList.remove('border-transparent', 'text-gray-500');
    activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
}

function updateTabCounts() {
    // Find all accordions and update their counts
    const accordions = document.querySelectorAll('[id^="preset-"][id$="-content"]');
    
    accordions.forEach(accordion => {
        const presetId = accordion.id.replace('-content', '');
        
        // Count Heights
        const heightsTable = accordion.querySelector(`#${presetId}-heights-content table tbody`);
        if (heightsTable) {
            const heightsCount = heightsTable.querySelectorAll('tr').length;
            const heightsCountEl = document.getElementById(`${presetId}-heights-count`);
            if (heightsCountEl) {
                heightsCountEl.textContent = heightsCount;
            }
        }
        
        // Count Metadata
        const metadataTable = accordion.querySelector(`#${presetId}-metadata-content table tbody`);
        if (metadataTable) {
            const metadataCount = metadataTable.querySelectorAll('tr').length;
            const metadataCountEl = document.getElementById(`${presetId}-metadata-count`);
            if (metadataCountEl) {
                metadataCountEl.textContent = metadataCount;
            }
        }
    });
}

// Listen for Plotly click events on the telemetry chart
document.addEventListener('DOMContentLoaded', function() {
    // Wait for the chart to be rendered
    setTimeout(() => {
        const chartElements = document.querySelectorAll('.plotly-graph-div');
        chartElements.forEach(chartElement => {
            chartElement.on('plotly_click', function(data) {
                if (data.points && data.points.length > 0) {
                    const point = data.points[0];
                    
                    // Check if this is a preset marker (from "Presets: Applying" or "Presets: Initializing" trace)
                    if (point.fullData && (
                        point.fullData.name === 'Presets: Applying' || 
                        point.fullData.name === 'Presets: Initializing' ||
                        (point.y && (point.y.includes('Presets: Applying') || point.y.includes('Presets: Initializing')))
                    )) {
                        const clickedData = point.customdata;
                        const clickedTimestamp = point.x; // Get the timestamp from the clicked point
                        
                        if (point.fullData.name === 'Presets: Applying' || 
                            (point.y && point.y.includes('Presets: Applying'))) {
                            // Handle applying presets (existing logic)
                            const clickedHash = clickedData;
                            if (clickedHash) {
                                showPresetDetailsForHash(clickedHash, clickedTimestamp);
                            } else {
                                showAllPresetDetails();
                            }
                        } else if (point.fullData.name === 'Presets: Initializing' || 
                                  (point.y && point.y.includes('Presets: Initializing'))) {
                            // Handle initializing presets (new logic for grouped display)
                            const timestampString = clickedData; // This is the timestamp string for grouping
                            if (timestampString) {
                                showInitializingPresetsForTimestamp(timestampString, clickedTimestamp);
                            } else {
                                showAllPresetDetails();
                            }
                        }
                    }
                }
            });
        });
    }, 1000); // Wait 1 second for chart to render
    
    // Update counts when page loads
    updateTabCounts();
});

function showInitializingPresetsForTimestamp(timestampString, clickedTimestamp) {
    // Show the header and content, hide message
    document.getElementById('preset-details-header').classList.remove('hidden');
    document.getElementById('preset-details-message').classList.add('hidden');
    const contentDiv = document.getElementById('preset-details-content');
    contentDiv.classList.remove('hidden');
    
    // Hide all existing accordions first (only within preset details section)
    const allAccordions = document.querySelectorAll('#preset-details-content .border.border-gray-200.rounded-lg');
    allAccordions.forEach(accordion => {
        accordion.style.display = 'none';
    });
    
    // Convert clicked timestamp to target time for comparison
    const targetTime = new Date(clickedTimestamp);
    let hasMatchingData = false;
    
    allAccordions.forEach((accordion, index) => {
        // Check if this accordion has initializing group_key
        const groupKey = accordion.getAttribute('data-group-key');
        const filterTimestamp = accordion.getAttribute('data-filter-timestamp');
        
        // Only show initializing presets that match the timestamp
        if (groupKey && groupKey.endsWith('_initializing') && filterTimestamp) {
            // Compare timestamps with tolerance (within 1 second)
            const accordionTimestamp = new Date(filterTimestamp);
            const timeDiff = Math.abs(accordionTimestamp - targetTime);
            
            if (timeDiff < 1000) { // Within 1 second tolerance
                accordion.style.display = 'block';
                hasMatchingData = true;
            } else {
                // Ensure non-matching initializing presets are hidden
                accordion.style.display = 'none';
            }
        } else {
            // Hide all non-initializing presets (applying presets, etc.)
            accordion.style.display = 'none';
        }
    });
    
    if (hasMatchingData) {
        // Set subtitle for initializing presets with timestamp
        if (clickedTimestamp) {
            const clickedDate = new Date(clickedTimestamp);
            const formattedTimestamp = clickedDate.toLocaleString('sv-SE').replace('T', ' ');
            document.getElementById('preset-type-subtitle').innerHTML = `Viewing Presets Initializing on <strong>${formattedTimestamp}</strong>`;
        } else {
            document.getElementById('preset-type-subtitle').textContent = 'Viewing Presets Initializing';
        }
        
        // Update the counts for visible accordions
        updateTabCounts();
    } else {
        // No data found - hide content and show message
        document.getElementById('preset-details-message').classList.remove('hidden');
        document.getElementById('preset-details-content').classList.add('hidden');
        document.getElementById('preset-details-header').classList.add('hidden');
    }
}

 