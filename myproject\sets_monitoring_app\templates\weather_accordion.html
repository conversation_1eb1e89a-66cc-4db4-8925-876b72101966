<!-- Weather Accordion Component -->
<!-- This template should be included within an Alpine.js component that provides weather data -->

<div x-show="weatherOpen" 
     x-cloak
     class="bg-white rounded-b-lg" style="box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);">
    
    <!-- Accordion Content -->
    <div class="p-4">
        <!-- Loading State -->
        <div x-show="weatherLoading" class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading weather forecast...</p>
        </div>
        
        <!-- Error State -->
        <div x-show="weatherError" class="py-4">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-red-600 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                    </svg>
                    <span class="text-red-800 font-medium">Error loading weather data</span>
                </div>
                <p class="text-red-700 mt-1" x-text="weatherError"></p>
                <button @click="loadWeatherData()" 
                        class="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors">
                    Retry
                </button>
            </div>
        </div>
        
        <!-- Weather Chart Container -->
        <div x-show="weatherData && !weatherLoading && !weatherError">
            <!-- Chart Instructions -->
            <div class="text-center mb-2">
                <p class="text-xs text-gray-500 font-medium italic">
                    Drag to select time period • Mouse wheel to zoom • Click "Reset Zoom" to return to full view
                </p>
            </div>
            <div class="w-full relative" style="min-height: 400px;">
                <canvas :id="'weather-chart-' + setName.toLowerCase().replace(/[^a-z0-9]/g, '-')" style="display: block; margin: 0 auto;"></canvas>
            </div>
        </div>
    </div>
</div>
