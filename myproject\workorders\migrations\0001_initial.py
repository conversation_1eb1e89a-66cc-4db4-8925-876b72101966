# Generated migration for VideoComment model

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='VideoComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_email', models.Email<PERSON>ield(help_text='Email of the user who added the comment', max_length=255)),
                ('workorder_id', models.IntegerField(help_text='ID of the workorder this comment relates to')),
                ('blade', models.CharField(help_text='Blade identifier (A, B, C)', max_length=10)),
                ('activity_type', models.CharField(help_text='Type of activity (Filler, Grinding, Cleaning, LEP)', max_length=50)),
                ('start_datetime', models.DateTimeField(help_text='Start datetime of the activity')),
                ('comment_text', models.TextField(help_text='The actual comment text')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, help_text='When the comment was created')),
            ],
            options={
                'db_table': 'video_comments',
                'ordering': ['created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='videocomment',
            index=models.Index(fields=['workorder_id', 'blade', 'activity_type', 'start_datetime'], name='video_comments_workorder_blade_activity_start_idx'),
        ),
        migrations.AddIndex(
            model_name='videocomment',
            index=models.Index(fields=['user_email'], name='video_comments_user_email_idx'),
        ),
        migrations.AddIndex(
            model_name='videocomment',
            index=models.Index(fields=['created_at'], name='video_comments_created_at_idx'),
        ),
    ] 