const CellFormatters = {
    /**
     * Formats decimals with conditional decimal places.
     * @param {number} value - The value to format
     * @returns {string} Formatted decimal string
     */
    formatDecimal(value) {
        if (value === undefined || value === null) return '0.0';
        // Check if the second decimal place is 0
        const valueStr = value.toFixed(2);
        if (valueStr.endsWith('0')) {
            return value.toFixed(1); // Show only one decimal place
        } else {
            return valueStr; // Show two decimal places
        }
    },

    /**
     * Formats a date for display in day/month/year format.
     * @param {Date} date - The date to format
     * @returns {string} Formatted date string
     */
    formatDate(date) {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    },

    /**
     * Formats a date for tooltip display.
     * @param {Date} date - The date to format
     * @returns {string} Formatted date string for tooltips
     */
    formatDateForTooltip(date) {
        return this.formatDate(date);
    }
}; 