const FilterManager = {
    _callbacks: {
        filtersUpdatedByControls: null // Callback for filter changes.
    },

    /**
     * Initializes filter controls.
     * @param {Object} callbacks - Callback functions
     */
    initialize(callbacks) {
        this._callbacks = callbacks;
        const urlFilters = UrlStateManager.getFiltersFromURL();

        if (Object.keys(urlFilters).length > 0) {
            let existingFilters = { ...workloadStore.activeFilters };

            if (urlFilters.dateRange) {
                 existingFilters.dateRange = {
                    start: urlFilters.dateRange.start || existingFilters.dateRange.start,
                    end: urlFilters.dateRange.end || existingFilters.dateRange.end
                 };
            }
            if (urlFilters.supervisors) {
                existingFilters.supervisors = urlFilters.supervisors;
            }
            if (urlFilters.serviceCodes) {
                existingFilters.serviceCodes = urlFilters.serviceCodes;
            }
            workloadStore.updateFilters(existingFilters, true); // Update store, and DO re-apply filters and URL.
        }

        DateRangeController.initializeDatePickers(() => this.handleFilterSubmit());
        DropdownController.initializeSupervisorSelect();
        this.refreshServiceCodeDropdown();
        DropdownController.initializeSelect2(
            () => this.handleSupervisorChange(),
            () => this.handleServiceCodeChange()
        );
        this._initializeFormEventListeners();

        // Make sure placeholders are applied immediately after initialization
        setTimeout(() => {
            DropdownController.applySimpleCountDisplay($('#supervisor'));
            DropdownController.applySimpleCountDisplay($('#service-code'));
        }, 50);
    },

    /**
     * Sets up form submit/reset listeners.
     */
    _initializeFormEventListeners() {
        const form = document.getElementById('workload-filter-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFilterSubmit();
        });

        form.addEventListener('reset', (e) => {
            e.preventDefault();
            this.handleFilterReset();
        });
    },

    /**
     * Refreshes the service code dropdown.
     */
    refreshServiceCodeDropdown() {
        DropdownController.initializeServiceCodeSelect();
    },

    /**
     * Handles supervisor selection changes.
     * @param {boolean} directUIChange - Whether this is a direct UI change
     */
    handleSupervisorChange(directUIChange = false) {
        let selectedSupervisors = $('#supervisor').val() || [];
        workloadStore.updateFilters({ supervisors: selectedSupervisors });
        UrlStateManager.updateURLWithFilters();

        DropdownController.applySimpleCountDisplay($('#supervisor'));
        this.refreshServiceCodeDropdown();

        if (selectedSupervisors.length === 0) {
             if (($('#service-code').val() || []).length > 0) {
                $('#service-code').val([]).trigger('change');
             } else {
                if (this._callbacks.filtersUpdatedByControls) {
                    this._callbacks.filtersUpdatedByControls('handleSupervisorChange_ServiceCodesAlreadyEmpty');
                }
             }
        } else {
            if (this._callbacks.filtersUpdatedByControls) {
                this._callbacks.filtersUpdatedByControls('handleSupervisorChange');
            }
        }

        if (directUIChange) {
            this.handleFilterSubmit();
        }
    },

    /**
     * Handles service code selection changes.
     * @param {boolean} directUIChange - Whether this is a direct UI change
     */
    handleServiceCodeChange(directUIChange = false) {
        let selectedServiceCodes = $('#service-code').val() || [];
        workloadStore.updateFilters({ serviceCodes: selectedServiceCodes });
        UrlStateManager.updateURLWithFilters();
        DropdownController.applySimpleCountDisplay($('#service-code'));

        if (this._callbacks.filtersUpdatedByControls) {
            this._callbacks.filtersUpdatedByControls('handleServiceCodeChange');
        }

        if (directUIChange) {
            this.handleFilterSubmit();
        }
    },

    /**
     * Handles filter form submission.
     */
    handleFilterSubmit() {
        const startDateInput = document.getElementById('start-date');
        const endDateInput = document.getElementById('end-date');
        const startDate = startDateInput.value ? new Date(startDateInput.value) : null;
        const endDate = endDateInput.value ? new Date(endDateInput.value) : null;

        let selectedSupervisors = $('#supervisor').val() || [];
        let selectedServiceCodes = $('#service-code').val() || [];

        DropdownController.applySimpleCountDisplay($('#supervisor'));
        DropdownController.applySimpleCountDisplay($('#service-code'));

        workloadStore.updateFilters({
            dateRange: { start: startDate, end: endDate },
            supervisors: selectedSupervisors,
            serviceCodes: selectedServiceCodes
        });
        UrlStateManager.updateURLWithFilters();

        if (this._callbacks.filtersUpdatedByControls) {
            this._callbacks.filtersUpdatedByControls('handleFilterSubmit');
        }
    },

    /**
     * Resets all filters.
     */
    handleFilterReset() {
        const endDate = new Date();
        const startDate = new Date(endDate);
        startDate.setDate(endDate.getDate() - 6);

        document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end-date').value = endDate.toISOString().split('T')[0];

        $('#supervisor').val([]).trigger('change');
        $('#service-code').val([]).trigger('change');

        workloadStore.updateFilters({
            dateRange: { start: startDate, end: endDate },
            supervisors: [],
            serviceCodes: []
        });
        UrlStateManager.updateURLWithFilters();

        if (this._callbacks.filtersUpdatedByControls) {
            this._callbacks.filtersUpdatedByControls('handleFilterReset');
        }
    },

    /**
     * Refreshes Select2 component displays.
     */
    refreshSelectDisplay() {
        const reinitializeSelect2 = ($el) => {
            if (!$el.length || !$.fn.select2) return;
            const currentVal = $el.val();
            $el.select2('destroy').select2({
                placeholder: '',  // Empty placeholder to avoid duplication
                allowClear: false,
                width: '100%',
                closeOnSelect: false,
                multiple: true,
                minimumResultsForSearch: 0, // Enable search
                dropdownParent: $('body'),
                theme: 'tailwindcss-3'
            }).val(currentVal).trigger('change.select2')
              .on('select2:select select2:unselect select2:close', (e) => {
                setTimeout(() => {
                    DropdownController.applySimpleCountDisplay($(e.currentTarget));
                }, 0);
            });

            // Force update the display immediately after initialization
            setTimeout(() => {
                DropdownController.applySimpleCountDisplay($el);
            }, 0);
        };

        reinitializeSelect2($('#supervisor'));
        reinitializeSelect2($('#service-code'));
    }
}; 