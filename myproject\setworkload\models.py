from django.db import models
from django.utils import timezone

class WorkloadComment(models.Model):
    """
    Model for storing user comments on workload data for specific sets and dates.
    """
    user_email = models.EmailField(max_length=255, help_text="Email of the user who added the comment")
    set_name = models.CharField(max_length=255, help_text="Name of the set this comment relates to")
    date = models.DateField(help_text="Date this comment relates to")
    comment_text = models.TextField(help_text="The actual comment text")
    created_at = models.DateTimeField(default=timezone.now, help_text="When the comment was created")

    class Meta:
        db_table = 'workload_comments'
        ordering = ['created_at']  # Order by creation time (oldest first)
        indexes = [
            models.Index(fields=['set_name', 'date']),  # For efficient querying by set and date
            models.Index(fields=['user_email']),  # For querying by user
            models.Index(fields=['created_at']),  # For ordering
        ]
        app_label = 'setworkload'

    def __str__(self):
        return f"Comment by {self.user_email} on {self.set_name} for {self.date}"


class WorkloadStatusType(models.Model):
    """
    Model for storing different status types that can be assigned to workload days.
    """
    status_code = models.CharField(max_length=20, unique=True, help_text="Unique code for the status")
    status_name = models.CharField(max_length=50, help_text="Display name for the status")
    color_class = models.CharField(max_length=50, help_text="CSS class for the status color")
    description = models.TextField(blank=True, help_text="Description of what this status means")
    is_active = models.BooleanField(default=True, help_text="Whether this status is available for selection")
    created_at = models.DateTimeField(default=timezone.now, help_text="When this status type was created")

    class Meta:
        db_table = 'workload_status_types'
        ordering = ['status_name']
        app_label = 'setworkload'

    def __str__(self):
        return f"{self.status_name} ({self.status_code})"


class WorkloadStatusChange(models.Model):
    """
    Model for storing status changes for specific sets and dates.
    """
    set_name = models.CharField(max_length=255, help_text="Name of the set this status change relates to")
    date = models.DateField(help_text="Date this status change relates to")
    status_code = models.ForeignKey(
        WorkloadStatusType, 
        on_delete=models.PROTECT, 
        to_field='status_code',
        help_text="The status code assigned to this day"
    )
    user_email = models.EmailField(max_length=255, help_text="Email of the user who made this change")
    changed_at = models.DateTimeField(default=timezone.now, help_text="When this status change was made")
    previous_status_code = models.CharField(
        max_length=20, 
        blank=True, 
        null=True,
        help_text="Previous status code before this change"
    )


    class Meta:
        db_table = 'workload_status_changes'
        ordering = ['-changed_at']
        indexes = [
            models.Index(fields=['set_name', 'date']),  # For efficient querying by set and date
            models.Index(fields=['user_email']),  # For querying by user
            models.Index(fields=['changed_at']),  # For ordering
        ]
        app_label = 'setworkload'

    def __str__(self):
        # Avoid accessing the foreign key relationship to prevent status_code_id issues
        return f"{self.set_name} on {self.date}: status change by {self.user_email}"
