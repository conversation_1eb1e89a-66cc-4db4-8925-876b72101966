<!-- Set Card with Weather Accordion -->

<!-- Hidden div for past status cubes to ensure colors are loaded -->
<div class="hidden">
    <div class="bg-red-500"></div>
    <div class="bg-orange-500"></div>
    <div class="bg-green-500"></div>
</div>

<!-- Alpine.js component for set card with weather accordion -->
<div x-data="WeatherComponent.createAlpineData('{{ set.name }}')" class="w-full">

<div class="relative bg-white" :class="weatherOpen ? 'rounded-t-lg' : 'rounded-lg shadow-md'">
    <div class="p-3 rounded-t-lg border-l-4 sm:p-4 {{ status_color }}">
        <div class="flex justify-between items-start mb-2">
            <div class="flex items-center min-w-0">
                <h3 class="flex items-center font-bold text-gray-800 truncate">
                    <span class="mr-1 text-gray-800">•</span>{{ set.name }}
                    <!-- Status cubes for past 5 days -->
                    <div class="flex items-center ml-2 space-x-1">
                        {% for status in set.past_days_status %}
                        <div class="status-cube w-4 h-4 rounded {{ status.color }} cursor-pointer transition-transform hover:scale-110"
                            data-status="{{ status.message }}">
                        </div>
                        {% endfor %}
                    </div>
                </h3>
            </div>
            <div class="flex items-center ml-2 text-sm text-gray-500 shrink-0">
                {% if set.is_daytime %}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="mr-1 w-4 h-4 text-gray-600">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" />
                </svg>
                {% else %}
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="mr-1 w-4 h-4 text-gray-600">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" />
                </svg>
                {% endif %}
                {{ set.local_time|time:"H:i" }}
            </div>
        </div>

        {% include 'project_details.html' %}
    </div>

    <div class="p-3 sm:p-4">
        <div class="space-y-2 text-sm text-gray-500">
            {% include 'activity_details.html' %}

            <div class="flex items-center">
                <svg class="mr-2 w-4 h-4 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                    </path>
                </svg>
                {{ set.country }}
            </div>
        </div>
    </div>

    <!-- Cloud icon button in bottom right corner -->
    <div class="absolute bottom-3 right-3 sm:bottom-4 sm:right-4">
        <button @click="toggleWeather()"
                class="flex items-center justify-center w-10 h-10 border rounded-full shadow-md transition-colors"
                :class="weatherOpen ? 'bg-blue-500 border-blue-500 text-white hover:bg-blue-600' : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'"
                :title="weatherOpen ? 'Close {{ set.name }} weather forecast' : 'Open {{ set.name }} weather forecast'">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z" />
            </svg>
        </button>
    </div>
</div>

<!-- Weather Accordion -->
{% include 'weather_accordion.html' %}

</div> <!-- End Alpine.js component -->