// Select2 Customization
// Define dropdowns at the top level so it's accessible to all functions
const projectFilterDropdowns = [
    { id: "service", placeholder: "Service" },
    { id: "project_status", placeholder: "Status" },
    { id: "project_manager", placeholder: "Project Manager" },
    { id: "region", placeholder: "Region" },
    { id: "client", placeholder: "Client" }
];

// Global flag to track if URL parameters have been applied
let urlParametersApplied = false;

// Force complete reinitialization of all Select2 dropdowns
function forceReinitializeAllDropdowns() {
    // First, destroy any existing instances
    projectFilterDropdowns.forEach(({ id }) => {
        const $select = $(`#${id}`);
        if ($select.hasClass('select2-hidden-accessible')) {
            try {
                $select.select2('destroy');
            } catch (e) {
                // Silently handle errors
            }
        }
    });

    // Then initialize again with custom display
    initializeSelect2Dropdowns();

    // Apply custom format to all dropdowns
    projectFilterDropdowns.forEach(({ id }) => {
        const $select = $(`#${id}`);
        applyTruncationWithCounter($select);
    });
}

function updateURLWithFilters() {
    const params = new URLSearchParams();
    projectFilterDropdowns.forEach(({ id }) => {
        const $select = $(`#${id}`);
        const selectedValues = $select.val() || [];
        if (selectedValues.length > 0) {
            selectedValues.forEach(value => {
                params.append(id, value);
            });
        }
    });
    const queryString = params.toString();
    const newUrl = queryString ? `${window.location.pathname}?${queryString}` : window.location.pathname;

    // Avoid pushing same state repeatedly
    if (window.location.href.split('#')[0] !== newUrl.split('#')[0]) {
        history.pushState({}, '', newUrl);
    }
}

function applyFiltersFromURLAndInitialize() {
    // Don't reapply if already done
    if (urlParametersApplied) {
        return true;
    }

    const currentParams = new URLSearchParams(window.location.search);
    let filtersAppliedFromURL = false;

    // Only proceed if there are URL parameters to apply
    if (currentParams.toString()) {
        projectFilterDropdowns.forEach(({ id }) => {
            if (currentParams.has(id)) {
                const $select = $(`#${id}`);
                const urlValues = currentParams.getAll(id);

                if ($select.length) {
                    try {
                        // Collect valid values that exist in the dropdown
                        const validUrlValues = [];

                        urlValues.forEach(val => {
                            // Create option if it doesn't exist
                            if ($select.find(`option[value="${val}"]`).length === 0) {
                                $select.append(`<option value="${val}">${val}</option>`);
                            }
                            validUrlValues.push(val);
                        });

                        if (validUrlValues.length > 0) {
                            // Set values
                            $select.val(validUrlValues);

                            // Update select2 if initialized
                            if ($select.hasClass('select2-hidden-accessible')) {
                                $select.trigger('change.select2');
                            }

                            $select.trigger('change');
                            filtersAppliedFromURL = true;
                            applyTruncationWithCounter($select);
                        }
                    } catch (e) {
                        console.error(`Error applying URL filter for ${id}:`, e);
                    }
                }
            }
        });

        // If filters were applied, filter the table
        if (filtersAppliedFromURL) {
            urlParametersApplied = true;

            setTimeout(() => {
                try {
                    if ($.fn.DataTable.isDataTable('#projects-table')) {
                        const table = $('#projects-table').DataTable();
                        import('./datatable-config.js').then(module => {
                            try {
                                module.filterTable(table);
                            } catch (e) {
                                // Non-critical error handling
                            }
                        }).catch(e => {
                            console.error("Error importing filterTable function:", e);
                        });
                    }
                } catch (e) {
                    // Silent error handling
                }
            }, 250);
        }
    }

    return filtersAppliedFromURL;
}

function initializeSelect2Dropdowns() {
    projectFilterDropdowns.forEach(({ id }) => {
        const $select = $(`#${id}`);

        // Destroy existing instance if present
        if ($select.hasClass('select2-hidden-accessible')) {
            $select.select2('destroy');
        }

        // Ensure empty option exists
        if ($select.find('option[value=""]').length === 0) {
            $select.prepend('<option value=""></option>');
        }

        // Initialize Select2
        $select.select2({
            placeholder: '',
            allowClear: false,
            width: '100%',
            closeOnSelect: false,
            multiple: true,
            minimumResultsForSearch: 0, // Enable search
            dropdownParent: $('body'),
            theme: 'tailwindcss-3',
            language: {
                noResults: function () {
                    return "No results found";
                }
            }
        });

        // Apply custom display immediately
        applyTruncationWithCounter($select);

        // Handle selection changes
        $select.on('select2:select select2:unselect select2:close change', function (e) {
            applyTruncationWithCounter($(e.currentTarget));
            updateURLWithFilters();

            // Filter the table
            const table = $('#projects-table').DataTable();
            if (table && $.fn.DataTable.isDataTable('#projects-table')) {
                import('./datatable-config.js').then(module => {
                    module.filterTable(table);
                });
            }
        });

        // Focus the search field when dropdown is opened and ensure proper positioning
        $select.on('select2:open', function () {
            setTimeout(function() {
                // Focus the search field
                const $searchField = $('.select2-search__field:visible');
                $searchField.focus();

                // Ensure the search field is properly positioned
                const $container = $select.next('.select2-container');
                const $rendered = $container.find('.select2-selection__rendered');
                const $searchContainer = $rendered.find('.select2-search--inline');

                // Reset any inline styles that might interfere with positioning
                $searchContainer.css({
                    'position': 'absolute',
                    'left': '0',
                    'top': '0',
                    'width': '100%'
                });

                // Ensure the search field takes the full width (minus dropdown arrow)
                $searchField.css({
                    'width': 'calc(100% - 30px)',
                    'padding-left': '8px'
                });
            }, 10);
        });
    });
}

// Function for "n selected" display
function applyTruncationWithCounter($select) {
    const $container = $select.next('.select2-container');
    const $rendered = $container.find('.select2-selection__rendered');
    const isOpen = $container.hasClass('select2-container--open');

    // If dropdown is open, don't modify the search field
    if (isOpen) {
        return;
    }

    // Remove any pill elements
    $rendered.find('.select2-selection__choice').remove();

    const allSelectedValues = $select.val() || [];

    // Clear existing content
    const $searchField = $rendered.find('.select2-search--inline').detach();
    $rendered.empty();
    if ($searchField.length) {
        $rendered.append($searchField);
        // Don't hide the search field - it will be controlled by CSS
    }

    // Create display text
    const displayText = allSelectedValues.length > 0
        ? `${allSelectedValues.length} selected`
        : "All selected";

    // Create display element
    const $displayTextElement = $('<span>')
        .addClass('select2-selection__rendered-text')
        .text(displayText);

    // Add tooltip with selected items
    if (allSelectedValues.length > 0) {
        const selectedTexts = allSelectedValues.map(value => {
            const escapedValue = String(value).replace(/'/g, "\\'");
            const $option = $select.find(`option[value='${escapedValue}']`);
            return $option.text().trim();
        });
        $rendered.attr('title', selectedTexts.join(', '));
    } else {
        $rendered.removeAttr('title');
    }

    $rendered.append($displayTextElement);

    // Style the rendered area
    $rendered.css({
        'width': 'calc(100% - 30px)',
        'text-align': 'left',
        'padding-left': '8px',
        'line-height': '36px'
    });
}

// Function to refresh the display of Select2 components
function refreshSelectDisplay() {
    // Re-initializes Select2 to correctly apply custom display logic (truncation)
    const reinitializeSelect2 = ($el) => {
        if (!$el.length || !$.fn.select2) return; // Ensure element and Select2 exist
        const currentVal = $el.val();
        $el.select2('destroy').select2({
            placeholder: '', // Empty placeholder to avoid showing any text
            allowClear: false, // Disable clear all button
            width: '100%',
            closeOnSelect: false,
            multiple: true,
            minimumResultsForSearch: 0, // Enable search
            dropdownParent: $('body'),
            templateSelection: (data) => data.text,
            theme: 'tailwindcss-3' // Added TailwindCSS theme
        }).val(currentVal).trigger('change.select2') // Restore value and trigger internal select2 update
          .on('select2:select select2:unselect select2:close change', (e) => { // Re-bind truncation
            setTimeout(() => {
                applyTruncationWithCounter($(e.currentTarget));
            }, 10); // Increased timeout for better reliability
        });
    };

    // Refresh each dropdown
    projectFilterDropdowns.forEach(({ id }) => {
        reinitializeSelect2($(`#${id}`));
        applyTruncationWithCounter($(`#${id}`));
    });
}

// Function to clear duplicates
function removeDuplicateOptions() {
    projectFilterDropdowns.forEach(({ id }) => {
        const $select = $(`#${id}`);
        const seen = new Set();

        $select.find('option').each(function() {
            const value = $(this).val();
            if (value === "") return; // Keep empty option

            if (seen.has(value)) {
                $(this).remove();
            } else {
                seen.add(value);
            }
        });
    });
}

// Initialize when document is ready
$(document).ready(function () {
    // Initialize Select2 dropdowns
    forceReinitializeAllDropdowns();

    // Monitor for pills and remove them
    projectFilterDropdowns.forEach(({ id }) => {
        const $select = $(`#${id}`);
        if ($select.length) {
            const $container = $select.next('.select2-container');
            if ($container.length) {
                const observer = new MutationObserver(function(mutations) {
                    if ($container.find('.select2-selection__choice').length > 0) {
                        applyTruncationWithCounter($select);
                    }
                });

                observer.observe($container[0], {
                    childList: true,
                    subtree: true,
                    characterData: false
                });
            }
        }
    });

    // Handle DataTable initialization
    $(document).on('datatableLoaded', function () {
        removeDuplicateOptions();
        urlParametersApplied = false;

        setTimeout(() => {
            applyFiltersFromURLAndInitialize();
        }, 100);
    });
});

// Also handle window.onload as a fallback for URL parameter application
$(window).on('load', function() {
    if (window.location.search && !urlParametersApplied) {
        try {
            const tableExists = $.fn.DataTable.isDataTable('#projects-table');
            if (tableExists) {
                removeDuplicateOptions();
                urlParametersApplied = false;

                setTimeout(() => {
                    try {
                        applyFiltersFromURLAndInitialize();
                    } catch (e) {
                        // Silent error handling
                    }
                }, 300);
            }
        } catch (e) {
            // Silent error handling
        }
    }
});