from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
import pytz

# Models will be defined here after analyzing the data structure

class RoboticSet(models.Model):
    """
    Model representing a robotic set and its current status.
    Note: This is a Django model but we're not actually storing this in the database.
    We're using it as a data structure to help organize our BigQuery data.
    """
    name = models.CharField(max_length=50)
    project_code = models.Char<PERSON>ield(max_length=50)
    country = models.CharField(max_length=100)
    timezone_name = models.CharField(max_length=100)
    last_activity = models.DateTimeField()
    local_time = models.DateTimeField(default=timezone.now)  # Store the BigQuery-calculated local time
    minutes_worked = models.IntegerField(null=True)  # Add this field
    service_code = models.CharField(max_length=50, null=True)  # Add service_code field
    activity_name = models.Char<PERSON><PERSON>(max_length=200, null=True)
    activity_type = models.Cha<PERSON><PERSON><PERSON>(max_length=100, null=True)
    start_datetime = models.DateTimeField(null=True)
    past_days_activity = models.J<PERSON><PERSON>ield(null=True)  # Add field for past days activity
    project_start_date = models.DateTimeField(null=True)  # Project actual start date
    project_end_date = models.DateTimeField(null=True)  # Project actual end date

    def get_status_for_day(self, date, minutes_worked=None, has_activity=False):
        """
        Get status color and message for a specific day
        - RED: No activity that day
        - GREEN (active): Activity that day with no minutes data, OR activity with 8-10h worked
        - ORANGE (attention): Activity that day with minutes worked < 8h or > 10h
        """
        date_str = date.strftime("%Y-%m-%d")
        
        if not has_activity:
            return {
                'color': 'bg-red-500',
                'message': f'{date_str}: No activity'
            }
            
        # If there's activity but no minutes data, show as active
        if minutes_worked is None:
            return {
                'color': 'bg-green-500',
                'message': f'{date_str}: active (worked time not available)'
            }
            
        # Calculate hours and minutes from minutes_worked
        hours = minutes_worked // 60
        minutes = minutes_worked % 60
        
        # Determine the worked time display format
        if hours < 1:
            minute_label = 'minute' if minutes == 1 else 'minutes'
            worked_time = f'({minutes} {minute_label} worked)'
        else:
            hour_label = 'hour' if hours == 1 else 'hours'
            minute_label = 'minute' if minutes == 1 else 'minutes'
            worked_time = f'({hours} {hour_label}, {minutes} {minute_label} worked)'
        
        # If we have minutes data, check if it's within normal range
        if 480 <= minutes_worked <= 600:  # Between 8-10 hours
            return {
                'color': 'bg-green-500',
                'message': f'{date_str}: active {worked_time}'
            }
        else:  # Less than 8 hours or more than 10 hours
            return {
                'color': 'bg-orange-500',
                'message': f'{date_str}: review {worked_time}'
            }

    @property
    def past_days_status(self):
        """
        Get status for the past 6 days including current day
        Returns a list of dictionaries with date, color, and message, ordered from oldest to newest (left to right)
        """
        if not self.past_days_activity:
            # Return 6 red status cubes if no activity data
            current_date = self.local_time.date()
            return [{
                'date': current_date - timedelta(days=i),
                'color': 'bg-red-500',
                'message': f'{(current_date - timedelta(days=i)).strftime("%Y-%m-%d")}: No activity recorded'
            } for i in range(5, -1, -1)]

        current_date = self.local_time.date()
        days_status = []
        
        # Create a lookup dictionary for quick access to activity data
        activity_lookup = {
            str(day['date']): day 
            for day in self.past_days_activity['daily_activities']
        }

        # Process past 5 days first (oldest to newest)
        for i in range(5, 0, -1):
            check_date = current_date - timedelta(days=i)
            check_date_str = check_date.strftime('%Y-%m-%d')
            
            if check_date_str in activity_lookup:
                day_activity = activity_lookup[check_date_str]
                # Check if there was actual activity that day
                has_activity = day_activity['activity_count'] > 0
                status = self.get_status_for_day(
                    check_date, 
                    day_activity['total_minutes'],
                    has_activity=has_activity
                )
            else:
                status = {
                    'color': 'bg-red-500',
                    'message': f'{check_date_str}: No activity recorded'
                }
            
            days_status.append({
                'date': check_date,
                'color': status['color'],
                'message': status['message']
            })

        # Add current day status last (rightmost)
        current_status_color = self.status_color
        
        # Map status colors to their actual names
        status_name_map = {
            'border-red-600': 'attention',
            'border-orange-500': 'review',
            'border-green-600': 'active',
            'bg-red-500': 'attention',
            'bg-orange-500': 'review',
            'bg-green-500': 'active'
        }
        
        # Map border colors to background colors
        border_to_bg_map = {
            'border-red-600': 'bg-red-500',
            'border-orange-500': 'bg-orange-500',
            'border-green-600': 'bg-green-500'
        }
        
        # Get the first class from status color (should be the border color)
        base_color = current_status_color.split()[0]
        
        # Get the status name
        status_name = status_name_map.get(base_color, 'unknown')
        
        # Get the background color from the map
        current_bg_color = border_to_bg_map.get(base_color, base_color)

        days_status.append({
            'date': current_date,
            'color': current_bg_color,
            'message': f'Current status: {status_name}'
        })

        return days_status

    @property
    def activity_details(self):
        """Get formatted activity details for tooltip"""
        if not self.activity_name or not self.activity_type:
            return None
        
        start_time = self.start_datetime.strftime(f'%Y-%m-%d %H:%M:%S {self.timezone_city}')
        end_time = self.last_activity.strftime(f'%Y-%m-%d %H:%M:%S {self.timezone_city}')
        return {
            'name': self.activity_name,
            'type': self.activity_type,
            'start': start_time,
            'end': end_time
        }
    
    @property
    def project_details(self):
        """Get formatted project dates for tooltip"""
        start_date = "..." if not self.project_start_date else self.project_start_date.strftime('%Y-%m-%d')
        end_date = "..." if not self.project_end_date else self.project_end_date.strftime('%Y-%m-%d')
        
        return {
            'start': start_date,
            'end': end_date
        }
    
    @property
    def display_project_code(self):
        """Return project code with service code in brackets if available"""
        if self.service_code:
            return f"{self.project_code} ({self.service_code})"
        return self.project_code
    
    @property
    def status_color(self):
        """Determine the status color based on the set's activity and working hours"""
        if not self.local_time or not self.last_activity:
            return "border-gray-300 bg-gray-50"

        # Ensure both times are timezone-aware and in the same timezone
        if self.local_time.tzinfo is None:
            local_time = timezone.make_aware(self.local_time)
        else:
            local_time = self.local_time

        if self.last_activity.tzinfo is None:
            last_activity = timezone.make_aware(self.last_activity)
        else:
            last_activity = self.last_activity

        current_hour = local_time.hour
        current_date = local_time.date()
        last_activity_date = last_activity.date()

        # First check if we have minutes_worked for the current day and if it's the current day
        if self.minutes_worked is not None and current_date == last_activity_date:
            if self.minutes_worked < 480:  # Less than 8 hours
                return "border-orange-500 bg-orange-50"
            elif self.minutes_worked > 600:  # More than 10 hours
                return "border-orange-500 bg-orange-50"

        # If it's the current day and we have activity at any time today, mark as GREEN
        if current_date == last_activity_date:
            return "border-green-600 bg-green-50"

        # If it's before 7 AM on the next day after last activity
        if current_hour < 7 and (current_date - last_activity_date).days == 1:
            return "border-green-600 bg-green-50"

        # If we're in working hours (7 AM - 5 PM) and no activity today, mark as RED
        if 7 <= current_hour < 17 and current_date != last_activity_date:
            return "border-red-600 bg-red-50"

        # For any other case (after hours with no activity today), preserve the last status
        if current_date == last_activity_date:
            return "border-green-600 bg-green-50"
        
        return "border-red-600 bg-red-50"

    @property
    def is_daytime(self):
        """Check if it's currently daytime at the set's location (night hours are 23:00-7:00)"""
        return not (self.local_time.hour >= 23 or self.local_time.hour < 7)
    
    @property
    def time_since_activity_hours(self):
        """Get the time since last activity in hours"""
        if not self.last_activity or not self.local_time:
            return float('inf')
            
        time_diff = self.local_time - self.last_activity
        return time_diff.total_seconds() / 3600
    
    @property
    def timezone_city(self):
        """Extract and format the timezone name for display"""
        if not self.timezone_name:
            return None
        try:
            # Get the timezone object
            tz = pytz.timezone(self.timezone_name)
            # Get the current time in this timezone
            current = datetime.now(tz)
            # Get the timezone name without region/country code
            tz_name = current.tzname()
            # Remove any parentheses and their contents
            tz_name = tz_name.split('(')[0].strip()
            # Replace underscores with spaces
            return tz_name.replace('_', ' ').strip()
        except:
            # Fallback to the old method if there's any error
            parts = self.timezone_name.split('/')
            if len(parts) > 1:
                return parts[-1].replace('_', ' ').split('[')[0]
            return None
    
    @property
    def activity_time(self):
        """Get the formatted time from last activity (already in set's local time)"""
        return self.last_activity.strftime('%H:%M')
    
    @property
    def time_since_activity(self):
        """Calculate time difference between last activity and current local time"""
        if not self.last_activity or not self.local_time:
            return None
            
        # Strip timezone info since both times are in the same timezone
        naive_local_time = self.local_time.replace(tzinfo=None)
        naive_last_activity = self.last_activity.replace(tzinfo=None)
        
        # Calculate time difference in seconds
        time_diff = naive_local_time - naive_last_activity
        total_seconds = int(time_diff.total_seconds())
        
        def format_time_parts(seconds):
            """Helper function to format time parts"""
            days = seconds // 86400
            remaining = seconds % 86400
            hours = remaining // 3600
            remaining = remaining % 3600
            minutes = (remaining // 60) + (1 if remaining % 60 > 0 else 0)  # Round up minutes if there are seconds
            
            parts = []
            if days > 0:
                parts.append(f"{days}d")
            if hours > 0 or days > 0:
                parts.append(f"{hours}h")
            if minutes > 0 or hours > 0 or days > 0:
                parts.append(f"{minutes}m")
            
            # If no parts (meaning less than 1 minute), show "1m"
            if not parts:
                return "1m"
                
            return " ".join(parts)
        
        # Handle future timestamps
        if total_seconds < 0:
            return f"in {format_time_parts(abs(total_seconds))}"
        elif total_seconds < 30:  # Increased threshold since we're not showing seconds
            return "just now"
        else:
            return f"{format_time_parts(total_seconds)} ago"
    
    class Meta:
        ordering = ['-last_activity']
