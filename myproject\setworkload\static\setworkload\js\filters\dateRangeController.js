const DateRangeController = {
    /**
     * Initializes date pickers with constraints and event listeners.
     * @param {Function} onFilterSubmit - Callback for when dates change
     */
    initializeDatePickers(onFilterSubmit) {
        const startInput = document.getElementById('start-date');
        const endInput = document.getElementById('end-date');
        if (!startInput || !endInput) return;

        const initialEndDate = workloadStore.activeFilters.dateRange.end || new Date();
        let initialStartDate = workloadStore.activeFilters.dateRange.start;

        if (!initialStartDate) {
            initialStartDate = new Date(new Date().setDate(initialEndDate.getDate() - 6));
            workloadStore.updateFilters({ dateRange: { start: initialStartDate, end: initialEndDate } }, false);
        }

        startInput.value = initialStartDate.toISOString().split('T')[0];
        endInput.value = initialEndDate.toISOString().split('T')[0];

        const storeStart = workloadStore.storedPeriod.start;
        const storeEnd = workloadStore.storedPeriod.end;

        if (storeStart && storeEnd) {
            startInput.min = storeStart.toISOString().split('T')[0];
            startInput.max = storeEnd.toISOString().split('T')[0];
            endInput.min = storeStart.toISOString().split('T')[0];
            endInput.max = storeEnd.toISOString().split('T')[0];
        }

        startInput.addEventListener('change', (e) => {
            const newStart = new Date(e.target.value);
            const currentEnd = new Date(endInput.value);
            let maxEnd = new Date(newStart);
            maxEnd.setDate(newStart.getDate() + 89); // Max 90-day range.

            if (storeEnd && maxEnd > storeEnd) maxEnd = storeEnd;
            if (currentEnd > maxEnd || currentEnd < newStart) endInput.value = maxEnd.toISOString().split('T')[0];

            endInput.min = newStart.toISOString().split('T')[0];
            endInput.max = maxEnd.toISOString().split('T')[0];

            if (onFilterSubmit) onFilterSubmit();
        });

        endInput.addEventListener('change', (e) => {
            const newEnd = new Date(e.target.value);
            const currentStart = new Date(startInput.value);
            let minStart = new Date(newEnd);
            minStart.setDate(newEnd.getDate() - 89); // Max 90-day range.

            if (storeStart && minStart < storeStart) minStart = storeStart;
            if (currentStart < minStart || currentStart > newEnd) startInput.value = minStart.toISOString().split('T')[0];

            startInput.min = minStart.toISOString().split('T')[0];
            startInput.max = newEnd.toISOString().split('T')[0];

            if (onFilterSubmit) onFilterSubmit();
        });
    }
}; 