const UrlStateManager = {
    /**
     * Updates URL with current filter values and reloads the page.
     */
    updateURLWithFilters() {
        const params = new URLSearchParams(window.location.search);
        
        // Get current filter values from Tom Select instances
        const regions = SetsFilterManager.tomSelectInstances.region ? SetsFilterManager.tomSelectInstances.region.getValue() : [];
        const sets = SetsFilterManager.tomSelectInstances.sets ? SetsFilterManager.tomSelectInstances.sets.getValue() : [];
        const serviceCodes = SetsFilterManager.tomSelectInstances['service-codes'] ? SetsFilterManager.tomSelectInstances['service-codes'].getValue() : [];
        const projects = SetsFilterManager.tomSelectInstances.projects ? SetsFilterManager.tomSelectInstances.projects.getValue() : [];

        // Update URL parameters
        if (regions.length > 0) {
            params.set('region', regions.join(','));
        } else {
            params.delete('region');
        }
        
        if (sets.length > 0) {
            params.set('sets', sets.join(','));
        } else {
            params.delete('sets');
        }
        
        if (serviceCodes.length > 0) {
            params.set('service_codes', serviceCodes.join(','));
        } else {
            params.delete('service_codes');
        }
        
        if (projects.length > 0) {
            params.set('projects', projects.join(','));
        } else {
            params.delete('projects');
        }
        
        // Update URL and reload page
        const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
        window.location.href = newUrl;
    },
    
    /**
     * Gets filter values from current URL parameters.
     */
    getFiltersFromURL() {
        const params = new URLSearchParams(window.location.search);
        
        return {
            regions: params.get('region') ? params.get('region').split(',').filter(Boolean) : [],
            sets: params.get('sets') ? params.get('sets').split(',').filter(Boolean) : [],
            serviceCodes: params.get('service_codes') ? params.get('service_codes').split(',').filter(Boolean) : [],
            projects: params.get('projects') ? params.get('projects').split(',').filter(Boolean) : []
        };
    }
};
