# Migration for adding Technical Idle status option

from django.db import migrations


def add_technical_idle_status(apps, schema_editor):
    """Add Technical Idle status type to the workload status system."""
    WorkloadStatusType = apps.get_model('setworkload', 'WorkloadStatusType')
    
    # Add Technical Idle status
    WorkloadStatusType.objects.create(
        status_code='technical_idle',
        status_name='Technical Idle',
        color_class='bg-gray-100',
        description='Set idle due to technical issues or maintenance',
        is_active=True
    )


def remove_technical_idle_status(apps, schema_editor):
    """Remove Technical Idle status type from the workload status system."""
    WorkloadStatusType = apps.get_model('setworkload', 'WorkloadStatusType')
    
    # Remove Technical Idle status
    WorkloadStatusType.objects.filter(status_code='technical_idle').delete()


class Migration(migrations.Migration):

    dependencies = [
        ('setworkload', '0002_add_status_models'),
    ]

    operations = [
        migrations.RunPython(
            add_technical_idle_status,
            remove_technical_idle_status,
        ),
    ]
