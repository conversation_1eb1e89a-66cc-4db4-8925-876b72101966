<div class="w-full activity-section flex flex-col">
    <!-- Filters and Count -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-3">
        <!-- Filter -->
        <div class="flex items-center gap-3">
            <div class="relative">
                <button id="activity-filter-btn" class="min-w-[120px] px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 cursor-pointer inline-flex items-center justify-between">
                    <span id="activity-filter-text">All Activities</span>
                    <svg class="ml-2 -mr-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
                <div id="activity-filter-dropdown" class="hidden absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                    <div class="py-1" role="menu">
                        <button class="activity-filter-option block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" data-filter="all" role="menuitem">
                            All Activities
                        </button>
                        <button class="activity-filter-option block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" data-filter="STANDARD" role="menuitem">
                            Standard Only
                        </button>
                        <button class="activity-filter-option block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" data-filter="DELAY" role="menuitem">
                            Delay Only
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Count -->
        <div class="text-sm text-gray-500 sm:text-right">
            <span id="activity-visible-count">{{ activities|length }}</span> of {{ activities|length }} activities
        </div>
    </div>
    {% if activities %}
        <div class="activity-table-container">
            <div class="activity-table-scroll overflow-x-auto">
                <table id="activity-table" class="min-w-full divide-y divide-gray-200 table-fixed">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[140px]">
                            Start Date/Time
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[140px]">
                            End Date/Time
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[200px]">
                            Name
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[100px]">
                            Group
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[100px]">
                            Duration
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[100px]">
                            Type
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="activity-table-body">
                    {% for activity in activities %}
                        <tr class="{% if activity.activity_type == 'DELAY' %}bg-red-50{% endif %}" data-activity-type="{{ activity.activity_type }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ activity.start_datetime }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ activity.end_datetime }}
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900 break-words">
                                <div class="w-full break-words">{{ activity.activity_name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if activity.activity_type == 'DELAY' %}bg-red-100 text-red-800{% else %}bg-green-100 text-green-800{% endif %}">
                                    {{ activity.activity_type }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ activity.duration }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ activity.activity_duration_type }}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            </div>
        </div>
        <div class="flex flex-col sm:flex-row justify-between items-center gap-4 bg-gray-50 rounded-lg p-2 activity-pagination">
            <div class="text-sm text-gray-700 text-center sm:text-left">
                Showing <span id="activity-start-range" class="font-medium">1</span> to <span id="activity-end-range" class="font-medium">8</span> of <span id="activity-total" class="font-medium">{{ activities|length }}</span> activities
            </div>
            <div class="flex justify-center">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button id="activity-prev" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-150">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div id="activity-page-numbers" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                        <span id="activity-current-page" class="font-bold">1</span> / <span id="activity-total-pages">1</span>
                    </div>
                    <button id="activity-next" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-150">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </nav>
            </div>
        </div>
    {% else %}
        <div class="activity-table-container">
            <div class="activity-table-scroll flex items-center justify-center">
                <p class="text-gray-600 text-center">No activity data available for this workorder.</p>
            </div>
        </div>
    {% endif %}
</div> 