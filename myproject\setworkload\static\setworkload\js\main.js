// Main initialization
document.addEventListener('DOMContentLoaded', async () => {

    // Get initial data from the page
    const dataElement = document.getElementById('workload-data');
    if (!dataElement) {
        return;
    }

    try {
        // Parse initial data
        const data = JSON.parse(dataElement.dataset.workload);

        if (!Array.isArray(data)) {
            throw new Error('Data is not an array');
        }

        if (data.length === 0) {
            return;
        }

        // Calculate initial period
        const dates = data.map(item => new Date(item.date));
        const period = {
            start: new Date(Math.min(...dates)),
            end: new Date(Math.max(...dates))
        };

        // Wait for StatusManager to initialize and load status changes before initializing store
        if (typeof StatusManager !== 'undefined') {
            await StatusManager.initialize();

            // Load status changes for the period
            const startDate = period.start.toISOString().split('T')[0];
            const endDate = period.end.toISOString().split('T')[0];
            await StatusManager.loadStatusChanges(startDate, endDate);
        }

        // Initialize store with raw data and the overall stored period
        workloadStore.initialize(data, period);

        // Initialize FilterManager and provide a callback for filter updates.
        // FilterManager reads from URL or sets defaults.
        FilterManager.initialize({
            filtersUpdatedByControls: (sourceOfChange) => {
                // Update table based on the source of the filter change
                if (sourceOfChange === 'handleServiceCodeChange') {
                    TableManager.updateTableWithoutServiceCodeRefresh();
                } else {
                    // For other changes, a full table update is appropriate.
                    TableManager.updateTable();
                }
            }
        });

        // Initial table render. This also populates service codes.
        TableManager.updateTable();

    } catch (error) {
        // Error is caught silently.
    }
});