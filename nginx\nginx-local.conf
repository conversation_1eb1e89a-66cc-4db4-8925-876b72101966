
server {
    listen       80;
    listen  [::]:80;

    server_name _;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name _;

    ssl_certificate     /etc/letsencrypt/live/datatools.aerones.local/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/datatools.aerones.local/privkey.pem;

    access_log off;
    error_log on;

    location / {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;

        proxy_pass http://app:8000;
        proxy_redirect off;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /static/ {
        alias /app/static/;
    }
    location /static/css/dist {
        alias /app/theme/static/css/dist/;
    }
}
