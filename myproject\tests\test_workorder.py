"""Test cases for the workorder app."""

import datetime

from django.test import Client, RequestFactory, TestCase
from django.urls import reverse

import workorders
from querycache.models import BigQueryCache


class ProjectLevelViewTests(TestCase):
    """Refresh cache view tests."""

    def setUp(self):
        # Set up the test client
        self.client = Client()

    def test_refresh_cache_post_success(self):
        """Test that POST request to project-level /refresh-cache endpoint returns 200 OK."""
        # Get the URL for the project-level refresh cache endpoint
        url = reverse(
            "project_refresh_cache"
        )  # Ensure this name matches your project urls.py

        # Make a POST request to the endpoint
        response = self.client.post(url)

        # Assert that the status code is 200
        self.assertEqual(response.status_code, 200)
        # Optionally, assert the content
        self.assertContains(
            response,
            "Cache refresh process placeholder: OK (Project Level)",
            status_code=200,
        )


class Workorder(TestCase):
    """Check that workorder page loads."""

    def setUp(self):
        """Set up the test client."""
        self.factory = RequestFactory()

    def test(self):
        """GET workorder page."""
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        request = self.factory.get("/workoders")
        response = workorders.views.workorder_detail(request, 1)
        self.assertEqual(response.status_code, 200)
        response = self.client.post(reverse("project_refresh_cache"))
        self.assertEqual(response.status_code, 200)
        # pylint: disable=no-member
        for cache in BigQueryCache.objects.all():
            cache.last_updated = cache.last_updated - datetime.timedelta(days=1)
            cache.save()
        response = self.client.post(reverse("project_refresh_cache"))
        self.assertEqual(response.status_code, 200)
        for cache in BigQueryCache.objects.all():
            self.assertGreater(cache.last_updated, now, f"Cache {cache.key} not updated")
