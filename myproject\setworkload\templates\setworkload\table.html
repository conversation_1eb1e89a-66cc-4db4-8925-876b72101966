<!-- Legend section - separate from table -->
<div class="bg-gray-50 px-3 py-2 border-b border-gray-200 sticky top-0 z-40">
  <div class="flex flex-wrap gap-4 items-center text-xs font-medium text-gray-500">
    <span>D: Direct Hours</span>
    <span>I: Idle Hours</span>
    <span>W: Virtual WTG Finished/Expected</span>
    <span>TTS: Total Time Span</span>
    <span class="ml-4">|</span>
    <span class="px-2 bg-green-50">Daily KPI Met</span>
    <span class="px-2 bg-yellow-50">Daily KPI Not Met</span>
    <span class="px-2 bg-red-50">No Activity</span>
    <span class="px-2 bg-blue-100">Weather</span>
    <span class="px-2 bg-purple-100">Travel</span>
    <span class="px-2 bg-orange-100">Idle</span>
    <span class="px-2 bg-gray-100">Technical Idle</span>
    <span class="ml-4">|</span>
    <span class="flex items-center">
      <svg class="mr-1 w-3.5 h-3.5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
      </svg>
      Daily Report Link
    </span>
    <span class="flex items-center">
      <svg class="mr-1 w-3.5 h-3.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
      </svg>
      Supervisor Comments
    </span>
    <span class="flex items-center">
      <svg class="mr-1 w-3.5 h-3.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M3 3v1.5M3 21v-6m0 0l2.77-.693a9 9 0 016.208.682l.108.054a9 9 0 006.086.71l3.114-.732a48.524 48.524 0 01-.005-10.499l-3.11.732a9 9 0 01-6.085-.711l-.108-.054a9 9 0 00-6.208-.682L3 4.5M3 15V4.5" />
      </svg>
      Status Changes
    </span>
  </div>
</div>

<!-- Table section -->
<div class="relative overflow-x-auto overflow-y-auto max-h-[calc(100vh-240px)]">
  <table class="w-full bg-white border-separate border-spacing-0">
    <thead class="bg-gray-50">
      <!-- Data headers row - sticky top -->
      <tr class="bg-gray-50">
        <!-- Set column header - sticky left and top -->
        <th
          class="sticky top-0 left-0 z-30 bg-gray-50 border-b border-r px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[200px] min-w-[200px] max-w-[200px] truncate">
          Set / Service
        </th>
        <!-- Date headers - sticky top -->
        {% for date in dates %}
        <th
          class="sticky top-0 z-20 bg-gray-50 border-b border-r px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[120px] min-w-[120px] max-w-[120px] md:w-[100px] md:min-w-[100px] md:max-w-[100px]">
          {{ date|date:"d.M" }}
        </th>
        {% endfor %}
        <!-- Metrics header - sticky right and top -->
        <th
          class="sticky top-0 right-0 z-30 bg-gray-50 border-b border-l px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[200px] min-w-[200px]">
          Metrics
        </th>
      </tr>
    </thead>
    <tbody id="workload-table-body">
      <!-- Table body will be populated by JavaScript -->
    </tbody>
  </table>
</div>