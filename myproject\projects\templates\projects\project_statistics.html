<div class="bg-white rounded-lg shadow-sm p-4 mb-6">
  <table class="w-full">
    <thead>
      <tr class="border-b">
        <th class="p-2 text-sm text-gray-600 font-medium" title="Total number of projects">
          Total Projects
        </th>
        <th class="p-2 text-sm text-gray-600 font-medium" title="Percentage of projects that started on time">
          On-time Start Rate
        </th>
        <th class="p-2 text-sm text-gray-600 font-medium" title="Percentage of projects that completed on time">
          On-Time Completion Rate
        </th>
        <th class="p-2 text-sm text-gray-600 font-medium" title="Average absolute variance in start dates (in days)">
          Avg Abs Start Variance
        </th>
        <th class="p-2 text-sm text-gray-600 font-medium" title="Average absolute variance in end dates (in days)">
          Avg Abs End Variance
        </th>
        <th class="p-2 text-sm text-gray-600 font-medium" title="Average number of date changes per project">
          Avg Date Changes
        </th>
      </tr>
    </thead>
    <tbody>
      <tr class="text-center">
        <td class="p-3">
          <span id="projects_total" class="text-xl font-bold text-gray-800"></span>
        </td>
        <td class="p-3">
          <span id="projects_onTimeStartRate" class="text-xl font-bold"></span>
        </td>
        <td class="p-3">
          <span id="projects_onTimeCompleteRate" class="text-xl font-bold"></span>
        </td>
        <td class="p-3">
          <div class="flex items-center justify-center">
            <span id="projects_avgStartVariance" class="text-xl font-bold text-gray-800"></span>
            <span class="text-sm text-gray-500 ml-1">days</span>
          </div>
        </td>
        <td class="p-3">
          <div class="flex items-center justify-center">
            <span id="projects_avgEndVariance" class="text-xl font-bold text-gray-800"></span>
            <span class="text-sm text-gray-500 ml-1">days</span>
          </div>
        </td>
        <td class="p-3">
          <span id="projects_avgDateChanges" class="text-xl font-bold text-gray-800"></span>
        </td>
      </tr>
    </tbody>
  </table>
</div>

{% load static %}
<script type="module">
  import { initializeStatisticsUI } from '{% static "projects/js/statistics.js" %}';
  initializeStatisticsUI();
</script>
