FROM node:22-alpine AS build-tailwind

WORKDIR /app

COPY . .

RUN cd theme/static_src \
 && npm install \
 && npm run build


FROM python:3.12-slim AS workorder-app

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=myproject.settings_prod
ENV PORT=8000

EXPOSE $PORT
WORKDIR /app

RUN pip install --upgrade pip

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . /app/

COPY --from=build-tailwind /app/theme/static/ /app/theme/static/

RUN chmod +x entry.sh
CMD ["./entry.sh"]
