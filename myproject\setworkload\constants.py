# Maximum allowed date range for queries in days
MAX_DATE_RANGE = 90

# SQL query template for workload data
WORKLOAD_QUERY_TEMPLATE = """
WITH SetServiceCodes AS (
    -- Get service codes directly from project_field_activity based on set_name and date
    -- Include historical data for forward fill (look back up to 90 days to match workload chart period)
    SELECT
        set_name,
        DATE(start_datetime) as activity_date,
        service_code,
        project_name
    FROM
        `presentation.project_field_activity`
    WHERE
        DATE(start_datetime) BETWEEN DATE_SUB('{start_date}', INTERVAL 90 DAY) AND '{end_date}'
        AND service_code IS NOT NULL
    GROUP BY
        set_name, activity_date, service_code, project_name
),
WorkorderInfo AS (
    SELECT
        workorder_id,
        service_code,
        project_name
    FROM
        `presentation.project_field_activity`
    WHERE
        DATE(start_datetime) BETWEEN '{start_date}' AND '{end_date}'
    GROUP BY
        workorder_id, service_code, project_name
),
DateRange AS (
    -- Generate all dates in the range
    SELECT date
    FROM UNNEST(GENERATE_DATE_ARRAY('{start_date}', '{end_date}')) AS date
),
ServiceKPI AS (
    -- Get expected units per workday for each service code
    SELECT
        service_code,
        CAST(units_per_workday AS FLOAT64) as units_per_workday
    FROM
        `budibase-dev-369406.presentation.md_service_kpi`
),
-- Find the most recent workorder date and service code for each set (for forward fill)
-- Only consider sets that exist in the current date range
LastWorkorderBySet AS (
    SELECT
        ssc.set_name,
        ssc.activity_date as last_workorder_date,
        ssc.service_code as last_service_code,
        ROW_NUMBER() OVER (PARTITION BY ssc.set_name ORDER BY ssc.activity_date DESC) as rn
    FROM
        SetServiceCodes ssc
    INNER JOIN (
        SELECT DISTINCT set_name
        FROM `presentation.daily_team_workload`
        WHERE date BETWEEN '{start_date}' AND '{end_date}'
    ) current_sets ON ssc.set_name = current_sets.set_name
    WHERE
        ssc.service_code IS NOT NULL
),
MostRecentWorkorderBySet AS (
    -- Keep only the most recent workorder for each set
    SELECT
        set_name,
        last_workorder_date,
        last_service_code
    FROM
        LastWorkorderBySet
    WHERE
        rn = 1
),
-- For dates without workorders, use global forward fill logic
AllDatesWithServiceCodes AS (
    SELECT
        dtw.set_name,
        dr.date as activity_date,
        CASE
            WHEN ssc.service_code IS NOT NULL THEN ssc.service_code
            ELSE mrws.last_service_code
        END as service_code,
        CASE
            WHEN ssc.service_code IS NOT NULL THEN 'workorder'
            ELSE 'previous_workorder'
        END as source
    FROM
        `presentation.daily_team_workload` dtw
    CROSS JOIN
        DateRange dr
    LEFT JOIN
        SetServiceCodes ssc
    ON
        dtw.set_name = ssc.set_name AND dr.date = ssc.activity_date
    LEFT JOIN
        MostRecentWorkorderBySet mrws
    ON
        dtw.set_name = mrws.set_name
    WHERE
        dr.date BETWEEN '{start_date}' AND '{end_date}'
    GROUP BY
        dtw.set_name, dr.date, ssc.service_code, mrws.last_service_code
),
ServiceCodeWithKPI AS (
    -- Join service codes with KPI data
    SELECT
        adsc.set_name,
        adsc.activity_date,
        adsc.service_code,
        adsc.source,
        IFNULL(kpi.units_per_workday, 0) AS expected_units
    FROM
        AllDatesWithServiceCodes adsc
    LEFT JOIN
        ServiceKPI kpi
    ON
        adsc.service_code = kpi.service_code
),
AllWorkorders AS (
    -- Get all workorders for each set and date
    SELECT
        dtw.set_name,
        dtw.date,
        CAST(wo_id AS INT64) as workorder_id
    FROM `presentation.daily_team_workload` dtw,
    UNNEST(dtw.workorder_ids) as wo_id
    WHERE dtw.date BETWEEN '{start_date}' AND '{end_date}'
),
WorkorderTimeframes AS (
    -- Get the timeframe for each workorder
    SELECT
        workorder_id,
        MIN(start_datetime) as min_start_date,
        MAX(end_datetime) as max_end_date,
        FORMAT_DATE('%Y-%m-%d', DATE(MIN(start_datetime))) as start_date_str,
        FORMAT_DATE('%Y-%m-%d', DATE(MAX(end_datetime))) as end_date_str
    FROM `presentation.project_field_activity`
    WHERE DATE(start_datetime) BETWEEN '{start_date}' AND '{end_date}'
    GROUP BY workorder_id
),
AllReports AS (
    -- Find all daily reports for all workorders with improved date filtering
    SELECT
        aw.set_name,
        aw.date,
        CONCAT('https://portal.aerones.com/html-reports/daily.html?rid=', rs.report_token) as report_url,
        rs.created_at,
        rs.dates
    FROM AllWorkorders aw
    JOIN WorkorderTimeframes wt ON aw.workorder_id = wt.workorder_id
    JOIN `budibase-dev-369406.presentation.tmp_report_setting` rs
    ON (
        -- Check if workorder_id is anywhere in the array as a substring
        REGEXP_CONTAINS(rs.workorder_ids, CAST(aw.workorder_id AS STRING))
    )
    WHERE rs.report_type = 'DAILY'
    AND rs.archived = true
    AND JSON_EXTRACT_ARRAY(rs.dates)[OFFSET(0)] IS NOT NULL
    -- Check if any date in the first 3 positions of the dates array matches our target date
    -- We need to handle both full ISO date strings (with T and time) and just date portions
    AND (
        -- Check for exact date matches
        FORMAT_DATE('%Y-%m-%d', aw.date) = JSON_EXTRACT_SCALAR(rs.dates, '$[0]')
        OR FORMAT_DATE('%Y-%m-%d', aw.date) = JSON_EXTRACT_SCALAR(rs.dates, '$[1]')
        OR FORMAT_DATE('%Y-%m-%d', aw.date) = JSON_EXTRACT_SCALAR(rs.dates, '$[2]')
        -- Also check for partial matches (date embedded in ISO datetime string)
        OR REGEXP_CONTAINS(JSON_EXTRACT_SCALAR(rs.dates, '$[0]'), FORMAT_DATE('%Y-%m-%d', aw.date))
        OR REGEXP_CONTAINS(JSON_EXTRACT_SCALAR(rs.dates, '$[1]'), FORMAT_DATE('%Y-%m-%d', aw.date))
        OR REGEXP_CONTAINS(JSON_EXTRACT_SCALAR(rs.dates, '$[2]'), FORMAT_DATE('%Y-%m-%d', aw.date))
    )
),
LatestReports AS (
    -- Find latest daily report for each set and date across all workorders
    -- Regardless of which day it was created on
    SELECT
        set_name,
        date,
        report_url,
        created_at,
        dates,
        ROW_NUMBER() OVER (PARTITION BY set_name, date ORDER BY created_at DESC) as rn
    FROM AllReports
),
DailyReportLinks AS (
    -- Get only the latest report for each set and date
    SELECT
        set_name,
        date,
        report_url,
        created_at,
        dates
    FROM LatestReports
    WHERE rn = 1
),
-- Find the latest report for each set regardless of date (fallback)
LatestSetReports AS (
    SELECT
        set_name,
        report_url as fallback_report_url,
        created_at,
        ROW_NUMBER() OVER (PARTITION BY set_name ORDER BY created_at DESC) as rn
    FROM AllReports
),
FallbackReportLinks AS (
    -- Get only the latest report for each set
    SELECT
        set_name,
        fallback_report_url
    FROM LatestSetReports
    WHERE rn = 1
)
SELECT
    dtw.set_name,
    dtw.supervisor_name,
    dtw.date,
    dtw.direct_minutes,
    dtw.idle_minutes,
    dtw.minutes_worked,
    dtw.units,
    dtw.workorder_ids,
    -- Get service codes and sources
    ARRAY_AGG(DISTINCT CASE WHEN sc.service_code IS NOT NULL THEN sc.service_code ELSE NULL END IGNORE NULLS) AS service_codes,
    ARRAY_AGG(DISTINCT ssc.project_name IGNORE NULLS) AS project_codes,
    -- Get service code sources
    ARRAY_AGG(DISTINCT sc.source IGNORE NULLS) AS service_code_sources,
    -- Get service code KPI data
    ARRAY_AGG(STRUCT(sc.service_code AS code, sc.expected_units AS expected_units) IGNORE NULLS) AS service_kpis,
    MAX(drl.report_url) AS report_url,
    MAX(frl.fallback_report_url) AS fallback_report_url
FROM
    `presentation.daily_team_workload` dtw
-- Join with ServiceCodeWithKPI to get service codes with their sources
LEFT JOIN ServiceCodeWithKPI sc
    ON dtw.set_name = sc.set_name AND dtw.date = sc.activity_date
-- Join with SetServiceCodes for project codes
LEFT JOIN SetServiceCodes ssc
    ON dtw.set_name = ssc.set_name AND dtw.date = ssc.activity_date
LEFT JOIN DailyReportLinks drl
    ON dtw.set_name = drl.set_name AND dtw.date = drl.date
LEFT JOIN FallbackReportLinks frl
    ON dtw.set_name = frl.set_name
WHERE
    dtw.date BETWEEN '{start_date}' AND '{end_date}'
GROUP BY
    dtw.set_name, dtw.supervisor_name, dtw.date,
    dtw.direct_minutes, dtw.idle_minutes, dtw.minutes_worked,
    dtw.units, dtw.workorder_ids
ORDER BY
    dtw.set_name, dtw.date
"""