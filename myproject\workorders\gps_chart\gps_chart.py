"""
GPS Chart Generation Module
Creates composite Plotly charts with speed area and activity timeline
"""
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
from typing import List, Dict, Any
import logging
from .gps_data import get_activity_color, format_address

logger = logging.getLogger(__name__)


def generate_gps_speed_chart(speed_data: List[Dict[str, Any]], activity_timeline: List[Dict[str, Any]],
                           vehicle_name: str, start_time: str, end_time: str, timezone: str = 'UTC') -> str:
    """
    Generate a composite GPS speed chart with area chart and activity timeline.
    
    Args:
        speed_data: List of speed data points with time, speed, and activity
        activity_timeline: List of activity segments with time ranges and details
        vehicle_name: Name of the vehicle
        start_time: Start time for the chart
        end_time: End time for the chart
        
    Returns:
        HTML string containing the Plotly chart
    """
    try:
        if not speed_data and not activity_timeline:
            return None
            
        # Create subplot with secondary y-axis for activity timeline
        fig = make_subplots(
            rows=2, cols=1,
            row_heights=[0.8, 0.2],  # Main chart 80%, activity timeline 20%
            shared_xaxes=True,
            vertical_spacing=0.05
            # Removed subplot_titles to eliminate "Speed Over Time" and "Activity Timeline"
        )
        
        # Add speed area chart
        if speed_data:
            times = [datetime.fromisoformat(point['time'].replace('Z', '+00:00')) for point in speed_data]
            speeds = [point['speed'] for point in speed_data]
            activities = [point['activity'] for point in speed_data]
            start_addresses = [point['start_address'] for point in speed_data]
            
            # Process addresses for display
            wrapped_addresses = [wrap_address(addr) for addr in start_addresses]
            
            fig.add_trace(
                go.Scatter(
                    x=times,
                    y=speeds,
                    mode='lines',
                    fill='tozeroy',
                    fillcolor='rgba(219, 39, 119, 0.3)',
                    line=dict(color='#db2777', width=1),
                    name='Speed',
                    customdata=list(zip(activities, wrapped_addresses)),
                    hovertemplate='<b>%{y:.1f} km/h</b><br>Activity: %{customdata[0]}<br>Start Address: %{customdata[1]}<br>%{x}<extra></extra>'
                ),
                row=1, col=1
            )
        
        # Add activity timeline
        if activity_timeline:
            y_position = 1  # Fixed y position for timeline bar
            
            for activity in activity_timeline:
                start_dt = datetime.fromisoformat(activity['start_time'].replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(activity['end_time'].replace('Z', '+00:00'))
                
                fig.add_trace(
                    go.Scatter(
                        x=[start_dt, end_dt, end_dt, start_dt, start_dt],
                        y=[y_position-0.4, y_position-0.4, y_position+0.4, y_position+0.4, y_position-0.4],
                        mode='lines',
                        fill='toself',
                        fillcolor=get_activity_color(activity['activity']),
                        line=dict(color=get_activity_color(activity['activity']), width=0),
                        name=activity['activity'].title(),
                        showlegend=False,  # Hide legend for activity timeline
                        hovertemplate=(
                            f"<b>{activity['activity'].title()}</b><br>"
                            f"Duration: {activity['duration_minutes']:.1f} min<br>"
                            f"Distance: {activity['distance']:.2f} km<br>"
                            f"Start Address: {format_address(activity.get('start_address', activity['address']))}<br>"
                            f"Start: {start_dt.strftime('%H:%M')}<br>"
                            f"End: {end_dt.strftime('%H:%M')}<extra></extra>"
                        ),
                        legendgroup=activity['activity']
                    ),
                    row=2, col=1
                )
        
        # Format datetimes for title
        try:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            datetime_range = f"{start_dt.strftime('%b %d %H:%M')} - {end_dt.strftime('%b %d %H:%M, %Y')}, {timezone}"
        except:
            datetime_range = f"Date Range, {timezone}"

        # Create title with line breaks for long titles
        title_text = f"GPS Speed Data ({vehicle_name}, {datetime_range})"
        # Break title into multiple lines if it's too long (roughly 80 characters)
        if len(title_text) > 80:
            # Try to break at a natural point (comma or space)
            if ', ' in datetime_range:
                title_text = f"GPS Speed Data ({vehicle_name},<br>{datetime_range})"
            else:
                # Fallback: break at vehicle name
                title_text = f"GPS Speed Data<br>({vehicle_name}, {datetime_range})"

        # Update layout
        fig.update_layout(
            title={
                'text': title_text,
                'x': 0.04,
                'y': 0.90,
                'font': {'size': 16},
                'xanchor': 'left',
                'yanchor': 'top',
                'automargin': True
            },
            showlegend=False,  # Hide all legends to remove activity list at top
            margin=dict(l=90, r=50, t=100, b=50),  # Increased top margin for wrapped titles
            height=400,
            autosize=True,  # Enable responsive sizing
            plot_bgcolor='white',
            paper_bgcolor='white'
        )
        
        # Update speed chart y-axis
        fig.update_yaxes(
            title_text="Speed<br>(km/h)",
            title_font_size=12,
            title_standoff=10,
            gridcolor='lightgray',
            gridwidth=1,
            dtick=10,  # Set tick interval to 10 km/h
            range=[0, 130],  # Set range from 0 to 130 like Navirec
            row=1, col=1
        )
        
        # Update activity timeline y-axis
        fig.update_yaxes(
            showticklabels=False,
            showgrid=False,
            range=[0.5, 1.5],
            row=2, col=1
        )
        
        # Update x-axes - let Plotly automatically handle time formatting like AeroTask chart
        fig.update_xaxes(
            gridcolor='lightgray',
            gridwidth=1,
            showticklabels=False,  # Hide x-axis labels on speed chart
            row=1, col=1
        )
        
        fig.update_xaxes(
            showticklabels=True,  # Show x-axis labels only on activity timeline
            row=2, col=1
        )
        
        # Convert to HTML with responsive configuration
        html_content = fig.to_html(
            include_plotlyjs='cdn',
            div_id="gps-speed-chart",
            config={'responsive': True, 'displayModeBar': 'hover'}
        )

        # Add custom styling to the div by modifying the HTML
        html_content = html_content.replace(
            '<div id="gps-speed-chart"',
            '<div id="gps-speed-chart" style="width: 100%; height: 400px; min-height: 400px;"'
        )

        return html_content
        
    except Exception as e:
        logger.error(f"Error generating GPS speed chart: {str(e)}")
        return None



def wrap_address(address: str, max_length: int = 40) -> str:
    """Wrap long addresses into multiple lines for better display in tooltips."""
    if not address:
        return "Unknown Location"
    
    # Split address by commas for natural break points
    parts = address.split(',')
    
    # If we have multiple parts, wrap them intelligently
    if len(parts) > 1:
        lines = []
        current_line = ""
        
        for part in parts:
            part = part.strip()
            # If adding this part would make the line too long, start a new line
            if current_line and len(current_line + ", " + part) > max_length:
                lines.append(current_line)
                current_line = part
            else:
                if current_line:
                    current_line += ", " + part
                else:
                    current_line = part
        
        # Add the last line
        if current_line:
            lines.append(current_line)
        
        # Join with HTML line breaks for tooltip display
        return "<br>".join(lines)
    else:
        # Single part, just break if too long
        if len(address) > max_length:
            # Find a good break point (space or punctuation)
            mid_point = max_length
            while mid_point > 0 and address[mid_point] not in [' ', '-', '.']:
                mid_point -= 1
            
            if mid_point > 0:
                return address[:mid_point] + "<br>" + address[mid_point:].strip()
        
        return address 