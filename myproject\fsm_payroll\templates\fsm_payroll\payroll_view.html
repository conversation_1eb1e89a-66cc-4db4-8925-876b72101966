{% extends 'base.html' %}

{% block title %}FSM Payroll{% endblock %}

{% load static %}

{% block header %}
Payroll
{% endblock %}

{% block content %}
<div class="overflow-hidden w-full">
    <div class="overflow-x-auto w-full">
        <!-- Payroll data for JavaScript -->
        <script id="payroll-data" type="application/json">{{ payroll_data_json|safe }}</script>

        <!-- Table section -->
        <div class="bg-white rounded-lg shadow-sm">
            {% include "fsm_payroll/table.html" %}
        </div>
    </div>
</div>

<!-- Loading overlay -->
<div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 flex flex-col items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
        <p class="text-gray-600">Loading payroll data...</p>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<script src="{% static 'fsm_payroll/js/payroll_table.js' %}"></script>
<script src="{% static 'fsm_payroll/js/payroll_tooltips.js' %}"></script>
{% endblock %}
