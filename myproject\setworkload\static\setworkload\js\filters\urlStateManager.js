const UrlStateManager = {
    /**
     * Updates URL with current filters.
     */
    updateURLWithFilters() {
        const filters = workloadStore.activeFilters;
        const params = new URLSearchParams(window.location.search);

        if (filters.dateRange.start) {
            params.set('start_date', filters.dateRange.start.toISOString().split('T')[0]);
        } else {
            params.delete('start_date');
        }

        if (filters.dateRange.end) {
            params.set('end_date', filters.dateRange.end.toISOString().split('T')[0]);
        } else {
            params.delete('end_date');
        }

        if (filters.supervisors && filters.supervisors.length > 0) {
            params.set('supervisors', filters.supervisors.join(','));
        } else {
            params.delete('supervisors');
        }

        if (filters.serviceCodes && filters.serviceCodes.length > 0) {
            params.set('service_codes', filters.serviceCodes.join(','));
        } else {
            params.delete('service_codes');
        }

        const newRelativePathQuery = window.location.pathname + '?' + params.toString();
        history.pushState(null, '', newRelativePathQuery);
    },

    /**
     * Reads filters from URL parameters.
     * @returns {Object} Parsed filters from URL
     */
    getFiltersFromURL() {
        const params = new URLSearchParams(window.location.search);
        const filters = {};

        if (params.has('start_date')) {
            const startDate = new Date(params.get('start_date'));
            if (!isNaN(startDate)) {
                if (!filters.dateRange) filters.dateRange = {};
                filters.dateRange.start = startDate;
            }
        }

        if (params.has('end_date')) {
            const endDate = new Date(params.get('end_date'));
            if (!isNaN(endDate)) {
                if (!filters.dateRange) filters.dateRange = {};
                filters.dateRange.end = endDate;
            }
        }

        if (params.has('supervisors')) {
            filters.supervisors = params.get('supervisors').split(',').filter(Boolean);
        }

        if (params.has('service_codes')) {
            filters.serviceCodes = params.get('service_codes').split(',').filter(Boolean);
        }

        return filters;
    }
}; 