from typing import Optional
import plotly.io as pio
import pandas as pd
import plotly.express as px
import plotly.graph_objs as go
import logging
import gc

from ..workorder_data import get_raw_workorder_data
from .aerotask_data import (
    filter_workorder_data,
    process_data,
    format_comment,
    format_custom_fields,
    calculate_activity_performance_data
)

logger = logging.getLogger(__name__)


def generate_plotly_chart(df):
    """Generate a Plotly timeline chart from processed aerotask data."""
    df['duration_min'] = (df['end_datetime'] - df['start_datetime']).dt.total_seconds() / 60
    df['duration'] = df['duration_min'].apply(lambda x: f"{int(x // 60)}h {int(x % 60)}min")

    # Use row_id for y-axis to prevent overlapping
    fig = px.timeline(
        df, x_start="start_datetime", x_end="end_datetime", y="row_id",
        # Use minimal hover data and customize it later
        hover_data=None,
        color='activity_name',
        labels={'row_id': 'blade_key'},  # Only rename the y-axis label
        title=df['wo_name'].iloc[0] if 'wo_name' in df.columns and len(df) > 0 else 'Timeline',
        height=350  # Reduced from 500px to make chart more compact
    )

    # Create a custom hover template that includes comments and custom fields
    # Format: activity_name=WTG off\nstart_datetime=Apr 21, 2025, 15:14\nend_datetime=Apr 21, 2025, 19:54\nblade_key=Common\nduration=4h 40min\ncomment=...\nproperties=...
    hovertemplate = 'activity_name=%{customdata[0]}<br>start_datetime=%{customdata[3]}<br>end_datetime=%{customdata[4]}<br>blade_key=%{customdata[1]}<br>duration=%{customdata[2]}<br>comment=%{customdata[5]}<br>properties=%{customdata[6]}<extra></extra>'

    # Update each trace with the custom hover template
    for i, trace in enumerate(fig.data):
        # Get the activity name for this trace
        activity_name = trace.name
        # Filter dataframe for this activity
        activity_df = df[df['activity_name'] == activity_name]
        # Create custom data array with the needed hover information
        custom_data = []
        for _, row in activity_df.iterrows():
            # Format dates manually to match the original format
            start_datetime = row['start_datetime'].strftime('%b %d, %Y, %H:%M')
            end_datetime = row['end_datetime'].strftime('%b %d, %Y, %H:%M')

            # Format comment and custom fields
            comment = format_comment(row.get('service_activity_comments'))
            custom_fields = format_custom_fields(row.get('service_activity_custom_fields'))

            custom_data.append([row['activity_name'], row['blade_key'], row['duration'], start_datetime, end_datetime, comment, custom_fields])
        # Update the trace with custom data and hover template
        fig.data[i].customdata = custom_data
        fig.data[i].hovertemplate = hovertemplate

    # Customize y-axis ticks to show only the blade_key part
    y_ticks = []
    y_positions = []

    # Group by blade_key to create category headers in the original chart order
    for blade in ['A', 'B', 'C', 'Common']:  # Original chart order with A at top
        blade_rows = df[df['blade_key'] == blade]
        if not blade_rows.empty:
            # Get all row IDs for this blade category
            row_ids = blade_rows['row_id'].tolist()

            # For each blade category, add all its rows
            for i, row_id in enumerate(row_ids):
                y_positions.append(row_id)
                # Only show the blade name for the first row in each category
                if i == 0:
                    y_ticks.append(blade)  # Use the blade name as the label
                else:
                    y_ticks.append('')  # Empty label for additional rows

    # Set custom y-axis ticks
    fig.update_yaxes(
        tickvals=y_positions,
        ticktext=y_ticks,
        title='blade_key',
        autorange='reversed'  # This ensures A is at the top and Common at the bottom
    )

    # We don't need annotations anymore since we're using the y-axis ticks for labels

    # Adjust the layout to accommodate the new row structure
    fig.update_layout(
        autosize=True,
        showlegend=False,  # Hide the activity_name legend as it's redundant with hover tooltips
        margin=dict(l=50, r=50, t=50, b=100),  # Reduced bottom margin since no legend
        # Ensure the chart has a reasonable width
        width=None  # Let it be responsive but constrained by the container
    )

    # Convert to HTML
    chart_html = pio.to_html(fig, full_html=False, config={'responsive': True})

    # Memory cleanup: Explicitly delete large objects
    del fig

    return chart_html


def get_chart_data(workorder_id: int) -> Optional[str]:
    """Main entry point to get aerotask chart data for a workorder."""
    raw_data = get_raw_workorder_data(workorder_id)
    if not raw_data.empty:
        filtered_data = filter_workorder_data(raw_data)
        if filtered_data.empty:
            # Clean up raw_data before returning
            del raw_data
            return None
        processed_data = process_data(filtered_data)
        aerotask_chart_html = generate_plotly_chart(processed_data)

        # Memory cleanup: Delete large DataFrames after use
        del raw_data
        del filtered_data
        del processed_data
        gc.collect()

        return aerotask_chart_html
    else:
        # Clean up raw_data before returning
        del raw_data
        return None


def get_chart_and_performance_data(workorder_id: int) -> tuple:
    """
    Main entry point to get both aerotask chart data and performance data for a workorder.

    Returns:
        tuple: (aerotask_chart_html, performance_data, performance_summary)
    """
    raw_data = get_raw_workorder_data(workorder_id)
    if not raw_data.empty:
        filtered_data = filter_workorder_data(raw_data)
        if filtered_data.empty:
            # Clean up raw_data before returning
            del raw_data
            return None, [], {}

        processed_data = process_data(filtered_data)
        aerotask_chart_html = generate_plotly_chart(processed_data)

        # Calculate performance data using the filtered data (before processing for chart)
        performance_result = calculate_activity_performance_data(filtered_data)
        performance_data = performance_result['performance_data']
        performance_summary = performance_result['summary_stats']

        # Memory cleanup: Delete large DataFrames after use
        del raw_data
        del filtered_data
        del processed_data
        gc.collect()

        return aerotask_chart_html, performance_data, performance_summary
    else:
        # Clean up raw_data before returning
        del raw_data
        return None, [], {}