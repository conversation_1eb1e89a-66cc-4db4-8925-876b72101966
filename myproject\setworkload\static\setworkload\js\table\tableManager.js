const TableManager = {
    // Store comment counts for the current date range
    commentCounts: {},

    /**
     * Updates the entire workload table, including fetching filtered data,
     * regenerating table rows and headers, and re-initializing service code filters.
     */
    updateTable() {
        const tbody = document.getElementById('workload-table-body');
        if (!tbody) {
            return;
        }
        tbody.innerHTML = '';

        const dates = this.getDateRange();
        const setData = this.processFilteredData();
        const activeSetData = this.getActiveSetData(setData);

        TableStructure.updateTableHeader(dates);

        // Fetch both comment counts AND status changes for the date range, then create table rows
        Promise.all([
            this.fetchCommentCounts(dates),
            this.fetchStatusChanges(dates)
        ]).then(() => {
            activeSetData.forEach(([setName, data]) => {
                const tr = TableStructure.createTableRow(setName, data, dates, this.commentCounts);
                tbody.appendChild(tr);
            });

            if (typeof FilterManager !== 'undefined' && FilterManager.refreshServiceCodeDropdown) {
                FilterManager.refreshServiceCodeDropdown();
            }
        });
    },

    /**
     * Updates the workload table similarly to `updateTable`, but
     * skips the re-initialization of the service code dropdown.
     * This is useful when filters change but service codes remain the same.
     */
    updateTableWithoutServiceCodeRefresh() {
        const tbody = document.getElementById('workload-table-body');
        if (!tbody) {
            return;
        }
        tbody.innerHTML = '';

        const dates = this.getDateRange();
        const setData = this.processFilteredData();
        const activeSetData = this.getActiveSetData(setData);

        TableStructure.updateTableHeader(dates);

        // Fetch both comment counts AND status changes for the date range, then create table rows
        Promise.all([
            this.fetchCommentCounts(dates),
            this.fetchStatusChanges(dates)
        ]).then(() => {
            activeSetData.forEach(([setName, data]) => {
                const tr = TableStructure.createTableRow(setName, data, dates, this.commentCounts);
                tbody.appendChild(tr);
            });
        });
    },

    /**
     * Gets the date range for the current filters.
     * @returns {string[]} Array of date strings
     */
    getDateRange() {
        const startDate = workloadStore.activeFilters.dateRange.start;
        const endDate = workloadStore.activeFilters.dateRange.end;

        const dates = [];
        if (startDate && endDate) {
            let currentDate = new Date(startDate);
            while (currentDate <= endDate) {
                dates.push(currentDate.toISOString().split('T')[0]);
                currentDate.setDate(currentDate.getDate() + 1);
            }
        }

        return dates;
    },

    /**
     * Processes the filtered data and organizes it by set.
     * @returns {Object} Organized set data
     */
    processFilteredData() {
        const filteredData = workloadStore.filteredData;
        const setData = {};

        filteredData.forEach(item => {
            if (!setData[item.set_name]) {
                setData[item.set_name] = {
                    metrics: workloadStore.metrics[item.set_name],
                    days: {}
                };
            }
            
            // If a record already exists for this set-date combination, merge key data
            if (setData[item.set_name].days[item.date]) {
                const existing = setData[item.set_name].days[item.date];
                
                // For basic fields, use the maximum (they should be the same for the same date anyway)
                // For arrays, merge them to avoid losing data
                setData[item.set_name].days[item.date] = {
                    ...existing,
                    direct_hours: Math.max(existing.direct_hours || 0, item.direct_hours || 0),
                    idle_hours: Math.max(existing.idle_hours || 0, item.idle_hours || 0),
                    units: Math.max(existing.units || 0, item.units || 0),
                    expected_units: Math.max(existing.expected_units || 0, item.expected_units || 0),
                    total_time_span: Math.max(existing.total_time_span || 0, item.total_time_span || 0),
                    service_codes: [...new Set([...(existing.service_codes || []), ...(item.service_codes || [])])],
                    service_code_sources: [...new Set([...(existing.service_code_sources || []), ...(item.service_code_sources || [])])],
                    project_codes: [...new Set([...(existing.project_codes || []), ...(item.project_codes || [])])],
                    workorder_ids: [...new Set([...(existing.workorder_ids || []), ...(item.workorder_ids || [])])],
                    service_kpis: item.service_kpis || existing.service_kpis || [],
                    report_url: existing.report_url || item.report_url,
                    fallback_report_url: existing.fallback_report_url || item.fallback_report_url
                };
            } else {
                setData[item.set_name].days[item.date] = item;
            }
        });

        return setData;
    },

    /**
     * Filters set data to only include sets with activity.
     * @param {Object} setData - The organized set data
     * @returns {Array} Array of [setName, data] pairs for active sets
     */
    getActiveSetData(setData) {
        return Object.entries(setData).filter(([_, data]) => {
            const hasNonZeroValues = Object.values(data.days).some(day =>
                day.direct_hours > 0 || day.idle_hours > 0 || day.units > 0
            );
            return hasNonZeroValues;
        });
    },

    /**
     * Fetches comment counts for all sets and dates in the given date range.
     * @param {string[]} dates - Array of date strings in YYYY-MM-DD format.
     * @returns {Promise} Promise that resolves when comment counts are fetched.
     */
    async fetchCommentCounts(dates) {
        if (dates.length === 0) {
            return;
        }

        const startDate = dates[0];
        const endDate = dates[dates.length - 1];

        try {
            const response = await fetch(`/setworkload/api/comments/bulk/?start_date=${startDate}&end_date=${endDate}`);
            const data = await response.json();

            if (data.success) {
                this.commentCounts = data.comment_counts;
            }
        } catch (error) {
            console.debug('Could not fetch comment counts:', error);
            this.commentCounts = {};
        }
    },

    /**
     * Fetches status changes for the given date range.
     * @param {string[]} dates - Array of date strings in YYYY-MM-DD format.
     * @returns {Promise} Promise that resolves when status changes are fetched.
     */
    async fetchStatusChanges(dates) {
        if (dates.length === 0) {
            return;
        }

        const startDate = dates[0];
        const endDate = dates[dates.length - 1];

        try {
            if (typeof StatusManager !== 'undefined' && StatusManager.loadStatusChanges) {
                await StatusManager.loadStatusChanges(startDate, endDate);
            }
        } catch (error) {
            console.debug('Could not fetch status changes:', error);
        }
    }
}; 