<!-- Legend section - separate from table -->
<div class="bg-gray-50 px-3 py-2 border-b border-gray-200 sticky top-0 z-40">
  <div class="flex flex-wrap gap-4 items-center text-xs font-medium text-gray-500">
    <span class="px-2 bg-green-50">Approved Hours</span>
    <span class="px-2 bg-yellow-50">Pending Approval</span>
    <span class="px-2 bg-red-50">Rejected Hours</span>
    <span class="px-2 bg-gray-100">No Data</span>
  </div>
</div>

<!-- Table section -->
<div class="relative overflow-x-auto overflow-y-auto max-h-[calc(100vh-180px)]">
  <table class="w-full bg-white border-separate border-spacing-0">
    <thead class="bg-gray-50">
      <!-- Table headers will be populated by JavaScript -->
    </thead>
    <tbody id="payroll-table-body">
      <!-- Table body will be populated by JavaScript -->
    </tbody>
  </table>
</div>

<!-- Empty state -->
<div id="empty-state" class="hidden p-8 text-center">
  <div class="flex flex-col items-center">
    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No payroll data found</h3>
  </div>
</div>

