# Generated by Django 5.1.2 on 2025-05-12 13:14

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BigQueryCache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.TextField()),
                ('result', models.BinaryField()),
                ('query', models.TextField()),
                ('job_config', models.BinaryField()),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
