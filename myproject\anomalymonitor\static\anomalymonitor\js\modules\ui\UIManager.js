export class UIManager {
    constructor() {
        // Initialize all element references as null
        this.elements = {
            form: null,
            compareBtn: null,
            resultsContainer: null,
            imageInputs: {},
            canvases: {},
            statistics: {},
            masks: {}
        };

        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }

    initialize() {
        try {
            console.log('[Debug] UIManager: Starting initialization');

            // Get form elements
            this.elements.form = document.getElementById('comparisonForm');
            this.elements.compareBtn = document.getElementById('compareBtn');
            this.elements.resultsContainer = document.getElementById('results');

            if (!this.elements.form) {
                throw new Error('Form element not found');
            }

            // Get image inputs and canvases
            ['original', 'yesterday', 'today'].forEach(type => {
                const inputId = `${type}Image`;
                const canvasId = `${type}Canvas`;
                
                this.elements.imageInputs[type] = document.getElementById(inputId);
                this.elements.canvases[type] = document.getElementById(canvasId);

                if (!this.elements.imageInputs[type] || !this.elements.canvases[type]) {
                    console.warn(`Missing elements for ${type} image`);
                }
            });

            // Initialize event handlers
            this.initializeEventHandlers();
            console.log('[Debug] UIManager: Initialization complete');

        } catch (error) {
            console.error('[Debug] UIManager: Initialization failed:', error);
            alert('Failed to initialize the application. Please refresh the page.');
        }
    }

    initializeEventHandlers() {
        // Form submit handler
        this.elements.form.addEventListener('submit', async (event) => {
            event.preventDefault();
            try {
                await this.handleComparison();
            } catch (error) {
                console.error('[Debug] UIManager: Comparison error:', error);
                alert(error.message || 'An unexpected error occurred');
            }
        });

        // Image input handlers
        Object.entries(this.elements.imageInputs).forEach(([type, input]) => {
            if (input && this.elements.canvases[type]) {
                input.addEventListener('change', (event) => {
                    if (event.target.files?.[0]) {
                        this.previewImage(event.target.files[0], this.elements.canvases[type]);
                    }
                });
            }
        });
    }

    previewImage(file, canvas) {
        if (!file || !canvas) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const scale = Math.min(
                    canvas.width / img.width,
                    canvas.height / img.height
                );
                
                const x = (canvas.width - img.width * scale) / 2;
                const y = (canvas.height - img.height * scale) / 2;
                
                ctx.drawImage(img, x, y, img.width * scale, img.height * scale);
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    setLoading(isLoading) {
        if (this.elements.compareBtn) {
            this.elements.compareBtn.disabled = isLoading;
            this.elements.compareBtn.textContent = isLoading ? 'Processing...' : 'Compare Images';
        }

        // Disable all form inputs
        const inputs = this.elements.form.querySelectorAll('input');
        inputs.forEach(input => {
            input.disabled = isLoading;
        });
    }

    async handleComparison() {
        if (!this.elements.form) {
            throw new Error('Form not initialized');
        }

        try {
            const formData = new FormData(this.elements.form);
            
            // Validate files
            ['original_image', 'yesterday_image', 'today_image'].forEach(name => {
                const file = formData.get(name);
                if (!file?.size) {
                    throw new Error('Please select all three images');
                }
            });

            this.setLoading(true);
            
            const response = await fetch(this.elements.form.action, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            const result = await response.json();
            
            if (!response.ok || !result.success) {
                throw new Error(result.error || `Server error: ${response.status}`);
            }

            await this.displayResults(result);

        } catch (error) {
            console.error('[Debug] UIManager: Comparison failed:', error);
            throw error;
        } finally {
            this.setLoading(false);
        }
    }

    async displayResults(result) {
        // Show results container
        if (this.elements.resultsContainer) {
            this.elements.resultsContainer.style.display = 'block';
        }

        // Update statistics
        const stats = {
            'originalTodayScore': result.original_today_score,
            'originalTodayDiffPercentage': result.original_today_percentage,
            'originalTodayNumRegions': result.original_today_num_differences,
            'yesterdayTodayScore': result.yesterday_today_score,
            'yesterdayTodayDiffPercentage': result.yesterday_today_percentage,
            'yesterdayTodayNumRegions': result.yesterday_today_num_differences
        };

        Object.entries(stats).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = typeof value === 'number' ? 
                    (id.includes('Percentage') ? `${value.toFixed(2)}%` : value.toFixed(4)) :
                    value || '-';
            }
        });

        // Update difference images for both comparisons
        // Note: diff_base64 is the intensity heatmap (COLORMAP_JET)
        // diff_vis_base64 is the highlighted differences (green overlay with blue boxes)
        const visualizations = {
            'originalTodayDiff': result.original_today_diff,      // Intensity heatmap
            'originalTodayMask': result.original_today_mask,      // Highlighted differences
            'yesterdayTodayDiff': result.yesterday_today_diff,    // Intensity heatmap
            'yesterdayTodayMask': result.yesterday_today_mask     // Highlighted differences
        };

        Object.entries(visualizations).forEach(([id, base64Data]) => {
            const img = document.getElementById(id);
            if (img && base64Data) {
                img.src = `data:image/png;base64,${base64Data}`;
            }
        });
    }
}
