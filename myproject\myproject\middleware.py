"""Authentication middleware module for handling user authentication and redirects."""

from django.shortcuts import redirect
from django.urls import reverse
from django.http import HttpResponseForbidden


# pylint: disable=too-few-public-methods
class AuthenticationMiddleware:
    """Middleware to enforce authentication on all routes except specified excluded paths."""

    def __init__(self, get_response):
        self.get_response = get_response
        # Allowed email domains
        self.allowed_domains = ["aerones.com", "nordic.aerones.com"]

    def __call__(self, request):
        # Check for authentication first
        if not request.user.is_authenticated and not any(
            request.path.startswith(path)
            for path in ["/oidc/", "/admin/", "/authenticate/", "/callback/", "/refresh-cache/"]
        ):
            return redirect(reverse("oidc_authentication_init"))

        # Domain restriction check for authenticated users
        if request.user.is_authenticated:
            user_email = request.user.email.lower()
            # Check if email ends with any of the allowed domains
            if not any(
                user_email.endswith("@" + domain) for domain in self.allowed_domains
            ):
                return HttpResponseForbidden(
                    "Access denied. Only Aerones email addresses are allowed."
                )

        response = self.get_response(request)
        return response
