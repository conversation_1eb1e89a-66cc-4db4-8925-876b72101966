from myproject.utils import get_bigquery_client


def fetch_project_data(filters=None):
    client = get_bigquery_client()
    if client is None:
        return []
    query_base = """
    SELECT
        project_name,
        project_id,
        service,
        service_unit_type,
        first_promised_date,
        start_date_variance,
        abs_start_date_variance,
        duration_variance,
        planned_start_date,
        planned_due_date,
        CONCAT(scope_done, ' / ', COALESCE(scope,0)) AS units,
        first_activity_date as first_activity,
        completed_date,
        last_activity_date as last_activity,
        project_status,
        project_manager,
        region,
        customer AS client,
        date_changes_count,
        end_date_variance,
        abs_end_date_variance,
        started_on_time,
        completed_on_time,
        has_planned_start_date,
        set_name,
        wrike_link
    FROM
        `budibase-dev-369406.presentation.project_planned_vs_actual`
    """

    conditions = []
    if filters:
        for key, values in filters.items():
            if not values:
                continue

            if not isinstance(values, list):
                values = [values]
            
            safe_values = []
            for v in values:
                if isinstance(v, str):
                    escaped_v = v.replace("'", "\\'")
                    safe_values.append(f"'{escaped_v}'")
                elif isinstance(v, (int, float)):
                    safe_values.append(str(v))

            if not safe_values:
                continue

            db_column = key
            if key == 'client': 
                db_column = 'customer' 

            conditions.append(f"{db_column} IN ({', '.join(safe_values)})")

    query = query_base
    if conditions:
        query += " WHERE " + " AND ".join(conditions)

    query_job = client.query(query)
    results = query_job.result()

    data = []
    for row in results:
        data.append({
            'project_name': row.project_name,
            'project_id': row.project_id if row.project_id else 0,
            'first_promised_date': row.first_promised_date.strftime('%Y-%m-%d') if row.first_promised_date else '',
            'planned_start_date': row.planned_start_date.strftime('%Y-%m-%d') if row.planned_start_date else '',
            'planned_due_date': row.planned_due_date.strftime('%Y-%m-%d') if row.planned_due_date else '',
            'start_date_variance': row.start_date_variance,
            'abs_start_date_variance': row.abs_start_date_variance,
            'service': row.service,
            'unit_type': row.service_unit_type,
            'units': row.units,
            'duration_variance': row.duration_variance,
            'planned_dates': f"{row.planned_start_date if row.planned_start_date else ''} .. {row.planned_due_date if row.planned_due_date else ''}",
            'actual_dates': f"{row.first_activity if row.first_activity else ''} .. {row.completed_date if row.completed_date else ''}",
            'date_changes_count': row.date_changes_count,
            'project_status': row.project_status,
            'project_manager': row.project_manager,
            'region': row.region,
            'client': row.client,
            'end_date_variance': row.end_date_variance,
            'abs_end_date_variance': row.abs_end_date_variance,
            'started_on_time': row.started_on_time,
            'completed_on_time': row.completed_on_time,
            'set_name': row.set_name,
            'wrike_link': row.wrike_link
        })

    return data

def fetch_weekly_metrics():
    client = get_bigquery_client()
    if client is None:
        return []
    query = """
    SELECT
    *
    FROM
    `presentation.project_otd_weekly_metrics`

    """
    query_job = client.query(query)
    results = query_job.result()

    return [dict(row) for row in results]

def fetch_monthly_metrics():
    client = get_bigquery_client()
    if client is None:
        return []
    query = """
    SELECT
        *
    FROM
    `presentation.project_otd_monthly_metrics`
    """
    query_job = client.query(query)
    results = query_job.result()

    return [dict(row) for row in results]